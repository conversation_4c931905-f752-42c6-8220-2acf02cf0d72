import api from './api'

// Token management with remember me support
export const getToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token')
}

export const setToken = (token, rememberMe = false) => {
  if (token) {
    if (rememberMe) {
      localStorage.setItem('token', token)
      sessionStorage.removeItem('token')
    } else {
      sessionStorage.setItem('token', token)
      localStorage.removeItem('token')
    }
  } else {
    localStorage.removeItem('token')
    sessionStorage.removeItem('token')
  }
}

export const removeToken = () => {
  localStorage.removeItem('token')
  sessionStorage.removeItem('token')
}

export const getRefreshToken = () => {
  return localStorage.getItem('refreshToken') || sessionStorage.getItem('refreshToken')
}

export const setRefreshToken = (refreshToken, rememberMe = false) => {
  if (refreshToken) {
    if (rememberMe) {
      localStorage.setItem('refreshToken', refreshToken)
      sessionStorage.removeItem('refreshToken')
    } else {
      sessionStorage.setItem('refreshToken', refreshToken)
      localStorage.removeItem('refreshToken')
    }
  } else {
    localStorage.removeItem('refreshToken')
    sessionStorage.removeItem('refreshToken')
  }
}

export const getRememberMe = () => {
  return localStorage.getItem('rememberMe') === 'true'
}

export const setRememberMe = (rememberMe) => {
  if (rememberMe) {
    localStorage.setItem('rememberMe', 'true')
  } else {
    localStorage.removeItem('rememberMe')
  }
}

export const clearAllAuthData = () => {
  removeToken()
  localStorage.removeItem('refreshToken')
  sessionStorage.removeItem('refreshToken')
  localStorage.removeItem('userData')
  sessionStorage.removeItem('userData')
  localStorage.removeItem('rememberMe')
}

export const isTokenExpired = (token) => {
  if (!token) return true

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    return true
  }
}

const authService = {
  async login(email, password, rememberMe = false) {
    const response = await api.post('/auth/login', { email, password, rememberMe })
    return response.data
  },

  async register(userData) {
    const response = await api.post('/auth/register', userData)
    return response.data
  },

  async logout() {
    const response = await api.post('/auth/logout')
    return response.data
  },

  async getCurrentUser() {
    const response = await api.get('/auth/me')
    return response.data
  },

  async updateProfile(profileData) {
    const response = await api.put('/auth/profile', profileData)
    return response.data
  },

  async refreshToken(refreshToken) {
    const response = await api.post('/auth/refresh', { refreshToken })
    return response.data
  },

  async validateToken() {
    const response = await api.post('/auth/validate')
    return response.data
  },

  async autoLogin() {
    const token = getToken()
    if (!token || isTokenExpired(token)) {
      const refreshToken = getRefreshToken()
      if (refreshToken && !isTokenExpired(refreshToken)) {
        try {
          const refreshResponse = await this.refreshToken(refreshToken)
          const rememberMe = getRememberMe()
          setToken(refreshResponse.data.token, rememberMe)
          setRefreshToken(refreshResponse.data.refreshToken, rememberMe)
          return refreshResponse
        } catch (error) {
          clearAllAuthData()
          throw error
        }
      } else {
        clearAllAuthData()
        throw new Error('No valid token found')
      }
    } else {
      try {
        return await this.validateToken()
      } catch (error) {
        clearAllAuthData()
        throw error
      }
    }
  },
}

export default authService
