const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

async function testAlertStructure() {
  try {
    console.log('Testing Alert and Rule Data Structure...');
    
    // Test 1: Get alert structure
    console.log('\n1. Testing Alert Data Structure');
    const alertsResponse = await axios.get(`${API_BASE}/alerts?page=1&limit=1`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    
    if (alertsResponse.data.data.alerts.length > 0) {
      const alert = alertsResponse.data.data.alerts[0];
      console.log('Alert structure:');
      console.log('- ID:', alert._id);
      console.log('- Name:', alert.name);
      console.log('- Description:', alert.description);
      console.log('- Severity:', alert.severity);
      console.log('- Status:', alert.status);
      console.log('- Triggered At:', alert.triggeredAt);
      console.log('- Rule ID:', alert.ruleId);
      console.log('- Trigger Data:', typeof alert.triggerData);
      console.log('- Notes:', alert.notes ? alert.notes.length : 'undefined');
      console.log('- Assigned To:', alert.assignedTo);
      
      // Get detailed alert
      console.log('\n2. Testing Detailed Alert Data');
      const detailResponse = await axios.get(`${API_BASE}/alerts/${alert._id}`, {
        headers: {
          'X-API-Key': 'test-api-key-12345'
        }
      });
      
      const detailedAlert = detailResponse.data.data.alert;
      console.log('Detailed alert structure:');
      console.log('- ID:', detailedAlert._id);
      console.log('- Name:', detailedAlert.name);
      console.log('- Description:', detailedAlert.description);
      console.log('- Rule populated:', detailedAlert.ruleId ? 'Yes' : 'No');
      if (detailedAlert.ruleId) {
        console.log('  - Rule Name:', detailedAlert.ruleId.name);
      }
    }
    
    // Test 3: Get rule structure
    console.log('\n3. Testing Rule Data Structure');
    const rulesResponse = await axios.get(`${API_BASE}/alerts/rules?limit=1`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    
    if (rulesResponse.data.data.rules.length > 0) {
      const rule = rulesResponse.data.data.rules[0];
      console.log('Rule structure:');
      console.log('- ID:', rule._id);
      console.log('- Name:', rule.name);
      console.log('- Description:', rule.description);
      console.log('- Category:', rule.category);
      console.log('- Severity:', rule.severity);
      console.log('- Rule Type:', rule.ruleType);
      console.log('- Enabled:', rule.enabled);
      console.log('- Is Default:', rule.isDefault);
      console.log('- Time Window:', rule.timeWindow);
      console.log('- Threshold:', rule.threshold);
      console.log('- Conditions:', typeof rule.conditions);
      console.log('- Actions:', rule.actions ? rule.actions.length : 'undefined');
      console.log('- Statistics:', rule.statistics);
      console.log('- Created At:', rule.createdAt);
      console.log('- Created By:', rule.createdBy);
      
      // Get detailed rule
      console.log('\n4. Testing Detailed Rule Data');
      const ruleDetailResponse = await axios.get(`${API_BASE}/alerts/rules/${rule._id}`, {
        headers: {
          'X-API-Key': 'test-api-key-12345'
        }
      });
      
      const detailedRule = ruleDetailResponse.data.data.rule;
      console.log('Detailed rule structure:');
      console.log('- ID:', detailedRule._id);
      console.log('- Name:', detailedRule.name);
      console.log('- Created By populated:', detailedRule.createdBy ? 'Yes' : 'No');
      if (detailedRule.createdBy) {
        console.log('  - Created By Name:', detailedRule.createdBy.firstName, detailedRule.createdBy.lastName);
      }
    }
    
    console.log('\n✅ Data structure testing completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testAlertStructure();
