/**
 * Permission utility functions for controlling UI access and navigation
 */

// Navigation permission mappings
export const NAVIGATION_PERMISSIONS = {
  dashboard: 'nav_dashboard',
  logs: 'nav_logs',
  alerts: 'nav_alerts',
  agents: 'nav_agents',
  reports: 'nav_reports',
  users: 'nav_users',
  settings: 'nav_settings',
}

// Feature permission mappings
export const FEATURE_PERMISSIONS = {
  // Dashboard permissions
  viewDashboards: 'view_dashboards',
  manageDashboards: 'manage_dashboards',
  
  // Log permissions
  viewLogs: 'view_logs',
  searchLogs: 'search_logs',
  exportLogs: 'export_logs',
  
  // Alert permissions
  viewAlerts: 'view_alerts',
  manageAlerts: 'manage_alerts',
  
  // Agent permissions
  viewAgents: 'view_agents',
  manageAgents: 'manage_agents',
  
  // Report permissions
  viewReports: 'view_reports',
  generateReports: 'generate_reports',
  
  // User permissions
  viewUsers: 'view_users',
  manageUsers: 'manage_users',
  
  // Settings permissions
  viewSettings: 'view_settings',
  manageSettings: 'manage_settings',
  
  // System permissions
  systemAdmin: 'system_admin',
}

/**
 * Get all user permissions (role + custom permissions)
 * @param {Object} user - User object with role and permissions
 * @param {Array} roles - Available roles array
 * @returns {Array} Combined permissions array
 */
export const getUserPermissions = (user, roles = []) => {
  if (!user) return []
  
  // Get role permissions
  const userRole = roles.find(role => role.id === user.role)
  const rolePermissions = userRole?.permissions || []
  
  // Get custom permissions
  const customPermissions = user.permissions || []
  
  // Combine and deduplicate
  return [...new Set([...rolePermissions, ...customPermissions])]
}

/**
 * Check if user has a specific permission
 * @param {Array} userPermissions - User's permissions array
 * @param {string} permission - Permission to check
 * @returns {boolean} Whether user has the permission
 */
export const hasPermission = (userPermissions = [], permission) => {
  if (!permission) return false
  return userPermissions.includes(permission) || userPermissions.includes('system_admin')
}

/**
 * Check if user has any of the specified permissions
 * @param {Array} userPermissions - User's permissions array
 * @param {Array} permissions - Array of permissions to check
 * @returns {boolean} Whether user has any of the permissions
 */
export const hasAnyPermission = (userPermissions = [], permissions = []) => {
  if (!permissions.length) return false
  return permissions.some(permission => hasPermission(userPermissions, permission))
}

/**
 * Check if user has all of the specified permissions
 * @param {Array} userPermissions - User's permissions array
 * @param {Array} permissions - Array of permissions to check
 * @returns {boolean} Whether user has all of the permissions
 */
export const hasAllPermissions = (userPermissions = [], permissions = []) => {
  if (!permissions.length) return true
  return permissions.every(permission => hasPermission(userPermissions, permission))
}

/**
 * Check if user can access a navigation section
 * @param {Array} userPermissions - User's permissions array
 * @param {string} section - Section name (dashboard, logs, alerts, etc.)
 * @returns {boolean} Whether user can access the section
 */
export const canAccessSection = (userPermissions = [], section) => {
  const navPermission = NAVIGATION_PERMISSIONS[section]
  return hasPermission(userPermissions, navPermission)
}

/**
 * Get available navigation sections for user
 * @param {Array} userPermissions - User's permissions array
 * @returns {Array} Array of accessible section names
 */
export const getAccessibleSections = (userPermissions = []) => {
  return Object.keys(NAVIGATION_PERMISSIONS).filter(section => 
    canAccessSection(userPermissions, section)
  )
}

/**
 * Check if user can perform a specific feature action
 * @param {Array} userPermissions - User's permissions array
 * @param {string} feature - Feature permission key
 * @returns {boolean} Whether user can perform the action
 */
export const canPerformAction = (userPermissions = [], feature) => {
  const permission = FEATURE_PERMISSIONS[feature]
  return hasPermission(userPermissions, permission)
}

/**
 * Filter navigation items based on user permissions
 * @param {Array} navigationItems - Array of navigation items
 * @param {Array} userPermissions - User's permissions array
 * @returns {Array} Filtered navigation items
 */
export const filterNavigationItems = (navigationItems = [], userPermissions = []) => {
  return navigationItems.filter(item => {
    if (!item.permission) return true // No permission required
    return canAccessSection(userPermissions, item.permission)
  })
}

/**
 * Get permission-based menu configuration
 * @param {Array} userPermissions - User's permissions array
 * @returns {Object} Menu configuration object
 */
export const getMenuConfig = (userPermissions = []) => {
  const sections = getAccessibleSections(userPermissions)
  
  return {
    dashboard: sections.includes('dashboard'),
    logs: sections.includes('logs'),
    alerts: sections.includes('alerts'),
    agents: sections.includes('agents'),
    reports: sections.includes('reports'),
    users: sections.includes('users'),
    settings: sections.includes('settings'),
  }
}

/**
 * Check if user is system administrator
 * @param {Array} userPermissions - User's permissions array
 * @returns {boolean} Whether user is system admin
 */
export const isSystemAdmin = (userPermissions = []) => {
  return hasPermission(userPermissions, 'system_admin')
}

/**
 * Get user's effective permissions with role inheritance
 * @param {Object} user - User object
 * @param {Array} roles - Available roles
 * @returns {Object} Permissions breakdown
 */
export const getPermissionBreakdown = (user, roles = []) => {
  const userRole = roles.find(role => role.id === user?.role)
  const rolePermissions = userRole?.permissions || []
  const customPermissions = user?.permissions || []
  const allPermissions = getUserPermissions(user, roles)
  
  return {
    role: user?.role || 'none',
    roleName: userRole?.name || 'No Role',
    rolePermissions,
    customPermissions,
    allPermissions,
    isAdmin: isSystemAdmin(allPermissions),
    accessibleSections: getAccessibleSections(allPermissions),
    menuConfig: getMenuConfig(allPermissions),
  }
}

/**
 * Permission-based component wrapper utility
 * @param {Array} userPermissions - User's permissions array
 * @param {string|Array} requiredPermissions - Required permission(s)
 * @param {React.Component} component - Component to render if authorized
 * @param {React.Component} fallback - Fallback component if not authorized
 * @returns {React.Component} Authorized component or fallback
 */
export const withPermission = (userPermissions, requiredPermissions, component, fallback = null) => {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
  const hasAccess = hasAnyPermission(userPermissions, permissions)
  return hasAccess ? component : fallback
}

/**
 * Route permission checker
 * @param {Array} userPermissions - User's permissions array
 * @param {string} route - Route path
 * @returns {boolean} Whether user can access the route
 */
export const canAccessRoute = (userPermissions = [], route) => {
  const routePermissionMap = {
    '/dashboard': 'nav_dashboard',
    '/logs': 'nav_logs',
    '/alerts': 'nav_alerts',
    '/agents': 'nav_agents',
    '/reports': 'nav_reports',
    '/users': 'nav_users',
    '/settings': 'nav_settings',
  }
  
  const requiredPermission = routePermissionMap[route]
  if (!requiredPermission) return true // No permission required for unmapped routes
  
  return hasPermission(userPermissions, requiredPermission)
}

export default {
  NAVIGATION_PERMISSIONS,
  FEATURE_PERMISSIONS,
  getUserPermissions,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  canAccessSection,
  getAccessibleSections,
  canPerformAction,
  filterNavigationItems,
  getMenuConfig,
  isSystemAdmin,
  getPermissionBreakdown,
  withPermission,
  canAccessRoute,
}
