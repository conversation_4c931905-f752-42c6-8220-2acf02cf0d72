# ExLog Implementation References
## Sprint 1-2 Technical Documentation Guide

**Date:** June 11, 2025  
**Version:** 1.0  
**Status:** Final  

---

This document provides specific file and line number references for all key implementations discussed in the Sprint 1-2 Progress Report. Team members should use this guide to locate and study the actual code implementations.

## Table of Contents

1. [Authentication System](#authentication-system)
2. [Database Schema Implementation](#database-schema-implementation)
3. [API Documentation Implementation](#api-documentation-implementation)
4. [Docker Containerization](#docker-containerization)
5. [Network Configuration Fixes](#network-configuration-fixes)
6. [Database Architecture](#database-architecture)
7. [Log Persistence Fix](#log-persistence-fix)
8. [Dashboard Implementation](#dashboard-implementation)
9. [Additional Resources](#additional-resources)

---

## Authentication System

### JWT Token Generation
- **File**: `backend/src/services/authService.js`
- **Line Numbers**: 45-59
- **Description**: Implementation of JWT token generation with proper expiration and refresh mechanism
- **Key Features**:
  - Access token generation with user ID and roles
  - Refresh token generation with extended expiration
  - Environment variable configuration for secrets

```javascript
// backend/src/services/authService.js (lines 45-59)
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    { id: user._id, roles: user.roles },
    process.env.JWT_SECRET,
    { expiresIn: '1h' }
  );
  
  const refreshToken = jwt.sign(
    { id: user._id },
    process.env.REFRESH_TOKEN_SECRET,
    { expiresIn: '30d' }
  );
  
  return { accessToken, refreshToken };
};
```

### Remember Me Functionality
- **File**: `backend/src/controllers/authController.js`
- **Line Numbers**: 78-103
- **Description**: Implementation of "Remember Me" functionality for extended session persistence
- **Key Features**:
  - Configurable cookie expiration based on user preference
  - Secure cookie storage with proper flags
  - Session persistence across browser restarts
- **Additional Reference**: See `REMEMBER_ME_IMPLEMENTATION.md` for comprehensive documentation

```javascript
// backend/src/controllers/authController.js (lines 78-103)
const handleLogin = async (credentials, rememberMe) => {
  const { email, password } = credentials;
  const user = await User.findOne({ email });
  
  if (!user || !await bcrypt.compare(password, user.password)) {
    throw new Error('Invalid credentials');
  }
  
  const tokens = generateTokens(user);
  
  // Set cookies with appropriate expiration based on rememberMe
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    expires: rememberMe 
      ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)  // 30 days
      : new Date(Date.now() + 1 * 60 * 60 * 1000)        // 1 hour
  };
  
  return { user, tokens, cookieOptions };
};
```

## Database Schema Implementation

### MongoDB Log Schema
- **File**: `backend/src/models/Log.js`
- **Line Numbers**: 12-57
- **Description**: Comprehensive MongoDB schema for log storage with proper indexing
- **Key Features**:
  - Required fields with validation
  - Appropriate indexing for query optimization
  - Support for additional fields and metadata
  - Timestamp tracking

```javascript
// backend/src/models/Log.js (lines 12-57)
const logSchema = new mongoose.Schema({
  logId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  timestamp: {
    type: Date,
    required: true,
    index: true
  },
  // Additional fields...
}, { timestamps: true });
```

### TTL Index Fix
- **File**: `backend/src/models/Log.js`
- **Line Number**: 60
- **Description**: Fixed TTL index configuration to prevent immediate log deletion
- **Key Features**:
  - Configurable retention period (30 days default)
  - Environment variable support for customization
- **Additional Reference**: See `CHANGELOG.md` (line 15) for issue tracking

```javascript
// backend/src/models/Log.js (line 60)
logSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 }); // 30 days
```

## API Documentation Implementation

### Swagger Configuration
- **File**: `backend/src/config/swagger.js`
- **Line Numbers**: 5-62
- **Description**: OpenAPI 3.0 specification for comprehensive API documentation
- **Key Features**:
  - Detailed schema definitions for all models
  - Security scheme configurations
  - Server and endpoint descriptions
  - Interactive UI configuration
- **Additional Reference**: See `API_IMPROVEMENTS.md` (lines 10-25) for feature details

```javascript
// backend/src/config/swagger.js (lines 5-62)
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'ExLog API',
      version: '1.0.0',
      description: 'API for ExLog Cybersecurity Log Management Dashboard',
      contact: {
        name: 'ExLog Support',
        email: '<EMAIL>'
      }
    },
    // Additional configuration...
  },
  apis: ['./src/routes/*.js']
};
```

## Docker Containerization

### Docker Compose Configuration
- **File**: `docker-compose.yml`
- **Line Numbers**: 1-50
- **Description**: Multi-container Docker application configuration
- **Key Features**:
  - Service definitions for all components
  - Volume mapping for development
  - Environment variable configuration
  - Service dependencies and networking
- **Additional Reference**: See `NETWORKING_FIXES_SUMMARY.md` (lines 40-55) for network configuration

```yaml
# docker-compose.yml (lines 1-50)
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    # Additional configuration...

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    # Additional configuration...
```

## Network Configuration Fixes

### Frontend API Client
- **File**: `frontend/src/services/api.js`
- **Line Numbers**: 3-12
- **Description**: Dynamic API URL detection for cross-environment compatibility
- **Key Features**:
  - Environment variable support
  - Fallback to relative URLs
  - Consistent headers and credentials
- **Additional Reference**: See `FINAL_VERIFICATION.md` (lines 15-20) for verification details

```javascript
// frontend/src/services/api.js (lines 3-12)
const apiBaseUrl = process.env.REACT_APP_API_URL || '/api/v1';

const apiClient = axios.create({
  baseURL: apiBaseUrl,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});
```

### Backend CORS Configuration
- **File**: `backend/src/app.js`
- **Line Numbers**: 25-45
- **Description**: Comprehensive CORS configuration for secure cross-origin requests
- **Key Features**:
  - Dynamic origin validation
  - Support for credentials
  - Proper method and header allowances
- **Additional Reference**: See `NETWORKING_FIXES_SUMMARY.md` (lines 60-75) for detailed fixes

```javascript
// backend/src/app.js (lines 25-45)
app.use(cors({
  origin: function(origin, callback) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS 
      ? process.env.ALLOWED_ORIGINS.split(',') 
      : ['http://localhost:3000', 'http://localhost:8080'];
    
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  // Additional configuration...
}));
```

## Database Architecture

### Polyglot Persistence Approach
- **File**: `ExLog_Technical_Architecture.md`
- **Line Numbers**: 120-150
- **Description**: Multi-database architecture for optimized data storage and retrieval
- **Key Features**:
  - MongoDB for document storage
  - TimescaleDB for time-series data
  - Elasticsearch for full-text search
  - Redis for caching and real-time data
- **Additional Reference**: See `DASHBOARD_IMPLEMENTATION_SUMMARY.md` (lines 30-45) for implementation details

## Log Persistence Fix

### TTL Index Correction
- **File**: `backend/src/models/Log.js`
- **Line Numbers**: 58-60
- **Description**: Fixed log persistence issue by correcting TTL index configuration
- **Key Features**:
  - Changed from immediate deletion to 30-day retention
  - Made retention period configurable
- **Additional Reference**: See `TROUBLESHOOTING.md` (lines 10-25) and `LOG_PERSISTENCE_FIX.md` for detailed explanation

```javascript
// backend/src/models/Log.js (lines 58-60)
// Before: Immediate deletion
// logSchema.index({ createdAt: 1 }, { expireAfterSeconds: 0 });

// After: Configurable retention period
logSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });
```

## Dashboard Implementation

### Real-time Data Retrieval
- **File**: `frontend/src/pages/Dashboard/Dashboard.jsx`
- **Line Numbers**: 75-95
- **Description**: Implementation of real-time data retrieval for dashboard components
- **Key Features**:
  - Redux integration for state management
  - Auto-refresh functionality
  - Loading state handling
  - Error recovery
- **Additional Reference**: See `DASHBOARD_IMPLEMENTATION_SUMMARY.md` for comprehensive documentation

## Additional Resources

### Comprehensive Documentation
- **Remember Me Implementation**: `REMEMBER_ME_IMPLEMENTATION.md`
- **API Improvements**: `API_IMPROVEMENTS.md`
- **Network Configuration**: `NETWORKING_FIXES_SUMMARY.md` and `FINAL_VERIFICATION.md`
- **Log Persistence Fix**: `LOG_PERSISTENCE_FIX.md`
- **Dashboard Implementation**: `DASHBOARD_IMPLEMENTATION_SUMMARY.md`
- **Troubleshooting Guide**: `TROUBLESHOOTING.md`

### Key Configuration Files
- **Docker Compose**: `docker-compose.yml`
- **Nginx Configuration**: `nginx/nginx.conf`
- **Backend Environment**: `backend/.env.example`
- **Frontend Environment**: `frontend/.env.example`

### Testing Resources
- **Authentication Tests**: `backend/src/tests/auth.test.js`
- **Log API Tests**: `backend/src/tests/logs.test.js`
- **Network Tests**: `test-network-access.ps1` and `test-network-access.sh`

---

**Note to Team**: When studying these implementations, please refer to the actual code files for the most up-to-date versions. This document serves as a guide to locate the relevant sections but may not reflect changes made after Sprint 1-2.