import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Tabs,
  Tab,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import {
  Add,
  Assessment,
  Security,
  TrendingUp,
  BarChart,
  PieChart,
  Timeline,
  MoreVert,
  Refresh,
  DateRange,
} from '@mui/icons-material'
import { setActiveTab, fetchDashboardOverview } from '../../store/slices/reportingSlice'
import SecurityAnalytics from './components/SecurityAnalytics'
import ComplianceReporting from './components/ComplianceReporting'
import CustomReportBuilder from './components/CustomReportBuilder'
import ReportLibrary from './components/ReportLibrary'
import ReportTemplates from './components/ReportTemplates'
import ScheduledReports from './components/ScheduledReports'

const Reports = () => {
  const dispatch = useDispatch()
  const { activeTab, dashboardOverview, loading, error } = useSelector(state => state.reporting)
  const [anchorEl, setAnchorEl] = useState(null)
  const [timeRange, setTimeRange] = useState('24h')

  useEffect(() => {
    dispatch(fetchDashboardOverview(timeRange))
  }, [dispatch, timeRange])

  const handleTabChange = (event, newValue) => {
    dispatch(setActiveTab(newValue))
  }

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const handleTimeRangeChange = (newTimeRange) => {
    setTimeRange(newTimeRange)
    setAnchorEl(null)
    dispatch(fetchDashboardOverview(newTimeRange))
  }

  const handleRefresh = () => {
    dispatch(fetchDashboardOverview(timeRange))
  }

  const getTimeRangeLabel = (range) => {
    switch (range) {
      case '1h': return 'Last Hour'
      case '24h': return 'Last 24 Hours'
      case '7d': return 'Last 7 Days'
      case '30d': return 'Last 30 Days'
      default: return 'Last 24 Hours'
    }
  }

  const renderOverviewCards = () => {
    if (loading.dashboardOverview || !dashboardOverview) {
      return (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {[1, 2, 3, 4].map((i) => (
            <Grid item xs={12} sm={6} md={3} key={i}>
              <Card>
                <CardContent>
                  <Box sx={{ height: 80, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Typography color="text.secondary">Loading...</Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )
    }

    return (
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Security color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Security Score
                </Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {dashboardOverview.securityScore || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overall security posture
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Assessment color="error" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Total Incidents
                </Typography>
              </Box>
              <Typography variant="h4" color="error">
                {dashboardOverview.totalIncidents || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Critical: {dashboardOverview.criticalIncidents || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Timeline color="info" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Avg MTTR
                </Typography>
              </Box>
              <Typography variant="h4" color="info">
                {Math.round((dashboardOverview.avgMTTR || 0) / (1000 * 60))}m
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Mean time to resolve
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <BarChart color="success" sx={{ mr: 1 }} />
                <Typography variant="h6" component="div">
                  Log Volume
                </Typography>
              </Box>
              <Typography variant="h4" color="success">
                {(dashboardOverview.logVolume || 0).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Sources: {dashboardOverview.activeLogSources || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Reports & Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Comprehensive security insights, compliance reporting, and analytics dashboards.
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Chip
            icon={<DateRange />}
            label={getTimeRangeLabel(timeRange)}
            onClick={handleMenuOpen}
            variant="outlined"
          />
          <IconButton onClick={handleRefresh} disabled={loading.dashboardOverview}>
            <Refresh />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => dispatch(setActiveTab(2))}
          >
            Create Report
          </Button>
        </Box>
      </Box>

      {/* Time Range Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {['1h', '24h', '7d', '30d'].map((range) => (
          <MenuItem
            key={range}
            onClick={() => handleTimeRangeChange(range)}
            selected={timeRange === range}
          >
            {getTimeRangeLabel(range)}
          </MenuItem>
        ))}
      </Menu>

      {/* Overview Cards */}
      {renderOverviewCards()}

      {/* Main Content Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<TrendingUp />}
            label="Security Analytics"
            iconPosition="start"
          />
          <Tab
            icon={<Assessment />}
            label="Compliance"
            iconPosition="start"
          />
          <Tab
            icon={<Assessment />}
            label="Templates"
            iconPosition="start"
          />
          <Tab
            icon={<Add />}
            label="Custom Reports"
            iconPosition="start"
          />
          <Tab
            icon={<PieChart />}
            label="Report Library"
            iconPosition="start"
          />
          <Tab
            icon={<Timeline />}
            label="Scheduled Reports"
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      <Box>
        {activeTab === 0 && <SecurityAnalytics timeRange={timeRange} />}
        {activeTab === 1 && <ComplianceReporting />}
        {activeTab === 2 && <ReportTemplates />}
        {activeTab === 3 && <CustomReportBuilder />}
        {activeTab === 4 && <ReportLibrary />}
        {activeTab === 5 && <ScheduledReports />}
      </Box>
    </Box>
  )
}

export default Reports
