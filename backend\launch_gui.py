"""
Simple launcher for the GUI application
This can be used during development and testing
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Launch the GUI application"""
    try:
        # Import and run the GUI
        from gui.main import run_gui
        
        print("Starting Python Logging Agent GUI...")
        run_gui()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements-gui.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
