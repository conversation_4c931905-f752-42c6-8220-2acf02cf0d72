# ExLog Network Access Test Script
# This script helps test network connectivity to the ExLog application

Write-Host "=== ExLog Network Access Test ===" -ForegroundColor Green
Write-Host ""

# Function to get local IP addresses
function Get-LocalIPAddresses {
    $ipAddresses = @()
    
    # Get all network adapters with IP addresses
    $adapters = Get-NetIPAddress -AddressFamily IPv4 | Where-Object { 
        $_.IPAddress -ne "127.0.0.1" -and 
        $_.IPAddress -ne "169.254.*" -and
        $_.PrefixOrigin -eq "Dhcp" -or $_.PrefixOrigin -eq "Manual"
    }
    
    foreach ($adapter in $adapters) {
        $ipAddresses += $adapter.IPAddress
    }
    
    return $ipAddresses
}

# Function to test HTTP connectivity
function Test-HTTPConnection {
    param(
        [string]$url,
        [string]$description
    )
    
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $description - OK" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $description - HTTP $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $description - Connection failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test Docker containers
function Test-DockerContainers {
    Write-Host "Checking Docker containers..." -ForegroundColor Yellow
    
    try {
        $containers = docker-compose ps --format json | ConvertFrom-Json
        
        if ($containers) {
            foreach ($container in $containers) {
                $status = if ($container.State -eq "running") { "✅" } else { "❌" }
                Write-Host "$status $($container.Service): $($container.State)" -ForegroundColor $(if ($container.State -eq "running") { "Green" } else { "Red" })
            }
        } else {
            Write-Host "❌ No containers found. Run 'docker-compose up -d' first." -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Docker not available or containers not running" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Main test execution
Write-Host "1. Checking Docker containers..." -ForegroundColor Cyan
$dockerOK = Test-DockerContainers
Write-Host ""

if (-not $dockerOK) {
    Write-Host "Please start the Docker containers first:" -ForegroundColor Yellow
    Write-Host "  docker-compose up -d" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host "2. Getting local IP addresses..." -ForegroundColor Cyan
$ipAddresses = Get-LocalIPAddresses

if ($ipAddresses.Count -eq 0) {
    Write-Host "❌ No network IP addresses found" -ForegroundColor Red
    exit 1
}

Write-Host "Found IP addresses:" -ForegroundColor Yellow
foreach ($ip in $ipAddresses) {
    Write-Host "  - $ip" -ForegroundColor White
}
Write-Host ""

Write-Host "3. Testing localhost access..." -ForegroundColor Cyan
$localhostTests = @(
    @{ url = "http://localhost/health"; desc = "Nginx Health Check" },
    @{ url = "http://localhost/api/v1/health"; desc = "API Health Check" },
    @{ url = "http://localhost:3000/health"; desc = "Frontend Health Check" },
    @{ url = "http://localhost:5000/health"; desc = "Backend Direct Access" }
)

$localhostOK = $true
foreach ($test in $localhostTests) {
    $result = Test-HTTPConnection -url $test.url -description $test.desc
    if (-not $result) { $localhostOK = $false }
}
Write-Host ""

Write-Host "4. Testing network IP access..." -ForegroundColor Cyan
$networkOK = $true

foreach ($ip in $ipAddresses) {
    Write-Host "Testing IP: $ip" -ForegroundColor Yellow
    
    $networkTests = @(
        @{ url = "http://$ip/health"; desc = "  Nginx Health Check" },
        @{ url = "http://$ip/api/v1/health"; desc = "  API Health Check" },
        @{ url = "http://$ip:3000/health"; desc = "  Frontend Direct Access" },
        @{ url = "http://$ip:5000/health"; desc = "  Backend Direct Access" }
    )
    
    foreach ($test in $networkTests) {
        $result = Test-HTTPConnection -url $test.url -description $test.desc
        if (-not $result) { $networkOK = $false }
    }
    Write-Host ""
}

# Summary
Write-Host "=== Test Summary ===" -ForegroundColor Green
Write-Host ""

if ($localhostOK) {
    Write-Host "✅ Localhost access: Working" -ForegroundColor Green
} else {
    Write-Host "❌ Localhost access: Issues detected" -ForegroundColor Red
}

if ($networkOK) {
    Write-Host "✅ Network access: Working" -ForegroundColor Green
} else {
    Write-Host "❌ Network access: Issues detected" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Access URLs ===" -ForegroundColor Green
Write-Host ""
Write-Host "Localhost access:" -ForegroundColor Yellow
Write-Host "  Dashboard: http://localhost" -ForegroundColor White
Write-Host "  API: http://localhost/api/v1" -ForegroundColor White
Write-Host ""

if ($ipAddresses.Count -gt 0) {
    Write-Host "Network access:" -ForegroundColor Yellow
    foreach ($ip in $ipAddresses) {
        Write-Host "  Dashboard: http://$ip" -ForegroundColor White
        Write-Host "  API: http://$ip/api/v1" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "Default login: <EMAIL> / Admin123!" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "=== Troubleshooting ===" -ForegroundColor Green
Write-Host ""

if (-not $localhostOK -or -not $networkOK) {
    Write-Host "If you're experiencing issues:" -ForegroundColor Yellow
    Write-Host "1. Check firewall settings on this computer" -ForegroundColor White
    Write-Host "2. Verify Docker containers are running: docker-compose ps" -ForegroundColor White
    Write-Host "3. Check container logs: docker-compose logs [service-name]" -ForegroundColor White
    Write-Host "4. Restart services: docker-compose restart" -ForegroundColor White
    Write-Host "5. Rebuild if needed: docker-compose down && docker-compose up --build -d" -ForegroundColor White
    Write-Host ""
    Write-Host "For detailed troubleshooting, see NETWORKING_CONFIGURATION.md" -ForegroundColor Cyan
}

Write-Host "Test completed!" -ForegroundColor Green
