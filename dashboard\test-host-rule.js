const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

async function testHostRule() {
  try {
    console.log('Testing Host-based Rule...');
    
    // Get current alert count
    const initialResponse = await axios.get(`${API_BASE}/alerts/statistics`, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    const initialCount = initialResponse.data.data.statistics.total;
    console.log('Initial alert count:', initialCount);
    
    // Send a test log that should trigger the "tester" rule
    console.log('\nSending test log for DESKTOP-PLUAU4C...');
    const testLog = {
      timestamp: new Date().toISOString(),
      source: "System",
      sourceType: "event",
      host: "DESKTOP-PLUAU4C", // This should match your rule
      logLevel: "info",
      message: "Test log to trigger host-based rule",
      logId: `test-host-${Date.now()}`,
      rawData: null,
      additionalFields: {
        test: true,
        event_type: "test_event"
      }
    };
    
    await axios.post(`${API_BASE}/logs`, { logs: [testLog] }, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    
    console.log('✅ Test log sent successfully');
    
    // Wait a moment for processing
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if alert was created
    const afterResponse = await axios.get(`${API_BASE}/alerts/statistics`, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    const afterCount = afterResponse.data.data.statistics.total;
    
    console.log('\nResults:');
    console.log('- Initial alerts:', initialCount);
    console.log('- After test:', afterCount);
    
    if (afterCount > initialCount) {
      console.log('✅ SUCCESS: Alert was triggered!');
      
      // Get the latest alert to verify it's the right one
      const alertsResponse = await axios.get(`${API_BASE}/alerts?page=1&limit=1`, {
        headers: { 'X-API-Key': 'test-api-key-12345' }
      });
      
      if (alertsResponse.data.data.alerts.length > 0) {
        const latestAlert = alertsResponse.data.data.alerts[0];
        console.log('Latest alert:', {
          name: latestAlert.name,
          severity: latestAlert.severity,
          triggerHost: latestAlert.triggerData?.log?.host,
          triggerMessage: latestAlert.triggerData?.log?.message
        });
      }
    } else {
      console.log('❌ FAILED: No alert was triggered');
      console.log('This indicates an issue with the rule configuration or correlation engine');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testHostRule();
