import React, { useState, useEffect } from 'react'
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Chip,
  Divider,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Switch,
  Tooltip,
  IconButton,
  Button,
} from '@mui/material'
import {
  ExpandMore,
  Info,
  Visibility,
  VisibilityOff,
  Security,
  Navigation,
  Settings,
} from '@mui/icons-material'

const PermissionManager = ({ 
  selectedRole, 
  userPermissions = [], 
  onPermissionsChange, 
  roles = [], 
  groupedPermissions = {},
  disabled = false 
}) => {
  const [permissions, setPermissions] = useState(userPermissions)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [expandedCategories, setExpandedCategories] = useState({})

  useEffect(() => {
    setPermissions(userPermissions)
  }, [userPermissions])

  const getSelectedRoleData = () => {
    return roles.find(role => role.id === selectedRole)
  }

  const getRoleDefaultPermissions = () => {
    const roleData = getSelectedRoleData()
    return roleData?.permissions || []
  }

  const isRoleDefault = (permissionId) => {
    return getRoleDefaultPermissions().includes(permissionId)
  }

  const isCustomSelected = (permissionId) => {
    return permissions.includes(permissionId)
  }

  const isPermissionActive = (permissionId) => {
    return isRoleDefault(permissionId) || isCustomSelected(permissionId)
  }

  const handlePermissionToggle = (permissionId) => {
    if (disabled || isRoleDefault(permissionId)) return

    const newPermissions = isCustomSelected(permissionId)
      ? permissions.filter(p => p !== permissionId)
      : [...permissions, permissionId]
    
    setPermissions(newPermissions)
    onPermissionsChange(newPermissions)
  }

  const handleCategoryToggle = (category) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }))
  }

  const getCategoryIcon = (category) => {
    switch (category.toLowerCase()) {
      case 'navigation':
        return <Navigation />
      case 'dashboard':
      case 'logs':
      case 'alerts':
      case 'agents':
      case 'reports':
        return <Visibility />
      case 'users':
      case 'settings':
      case 'system':
        return <Security />
      default:
        return <Settings />
    }
  }

  const getCategoryColor = (category) => {
    switch (category.toLowerCase()) {
      case 'navigation':
        return 'primary'
      case 'dashboard':
        return 'info'
      case 'logs':
        return 'warning'
      case 'alerts':
        return 'error'
      case 'agents':
        return 'success'
      case 'reports':
        return 'secondary'
      case 'users':
        return 'primary'
      case 'settings':
        return 'default'
      case 'system':
        return 'error'
      default:
        return 'default'
    }
  }

  const getPermissionCount = (category) => {
    const categoryPermissions = groupedPermissions[category] || []
    const activeCount = categoryPermissions.filter(p => isPermissionActive(p.id)).length
    const totalCount = categoryPermissions.length
    return { active: activeCount, total: totalCount }
  }

  const handleQuickPreset = (presetType) => {
    let newPermissions = []
    
    switch (presetType) {
      case 'navigation_only':
        newPermissions = Object.entries(groupedPermissions)
          .filter(([category]) => category === 'Navigation')
          .flatMap(([, perms]) => perms.map(p => p.id))
          .filter(p => !isRoleDefault(p))
        break
      case 'view_only':
        newPermissions = Object.values(groupedPermissions)
          .flat()
          .filter(p => p.name.toLowerCase().includes('view') && !isRoleDefault(p.id))
          .map(p => p.id)
        break
      case 'clear_custom':
        newPermissions = []
        break
      default:
        return
    }
    
    setPermissions(newPermissions)
    onPermissionsChange(newPermissions)
  }

  const roleData = getSelectedRoleData()

  return (
    <Box>
      {/* Role Information */}
      {roleData && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle2">{roleData.name}</Typography>
          <Typography variant="body2">{roleData.description}</Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            <strong>Default Permissions:</strong> {getRoleDefaultPermissions().length} permissions included
          </Typography>
        </Alert>
      )}

      {/* No Data Warning */}
      {Object.keys(groupedPermissions).length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="subtitle2">No Permission Data</Typography>
          <Typography variant="body2">
            Permission data is not loaded. Please refresh the page or contact support.
          </Typography>
        </Alert>
      )}

      {/* Quick Actions */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Permission Management</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showAdvanced}
                    onChange={(e) => setShowAdvanced(e.target.checked)}
                    disabled={disabled}
                  />
                }
                label="Advanced Mode"
              />
              <Tooltip title="Show detailed permission information">
                <IconButton size="small">
                  <Info />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Quick Presets */}
          <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
            <Button
              size="small"
              variant="outlined"
              onClick={() => handleQuickPreset('navigation_only')}
              disabled={disabled}
            >
              Navigation Only
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={() => handleQuickPreset('view_only')}
              disabled={disabled}
            >
              View Permissions Only
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="warning"
              onClick={() => handleQuickPreset('clear_custom')}
              disabled={disabled}
            >
              Clear Custom Permissions
            </Button>
          </Box>

          {/* Permission Summary */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => {
              const { active, total } = getPermissionCount(category)
              return (
                <Chip
                  key={category}
                  icon={getCategoryIcon(category)}
                  label={`${category}: ${active}/${total}`}
                  color={active > 0 ? getCategoryColor(category) : 'default'}
                  variant={active === total ? 'filled' : 'outlined'}
                  size="small"
                />
              )
            })}
          </Box>
        </CardContent>
      </Card>

      {/* Permission Categories */}
      {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => {
        const { active, total } = getPermissionCount(category)
        const isExpanded = expandedCategories[category] ?? (category === 'Navigation')
        
        return (
          <Accordion 
            key={category} 
            expanded={isExpanded}
            onChange={() => handleCategoryToggle(category)}
            sx={{ mb: 1 }}
          >
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                {getCategoryIcon(category)}
                <Typography variant="subtitle1" sx={{ flex: 1 }}>
                  {category}
                </Typography>
                <Chip
                  label={`${active}/${total}`}
                  color={active > 0 ? getCategoryColor(category) : 'default'}
                  size="small"
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <FormGroup>
                {categoryPermissions.map((permission) => {
                  const isDefault = isRoleDefault(permission.id)
                  const isCustom = isCustomSelected(permission.id)
                  const isActive = isDefault || isCustom
                  
                  return (
                    <Box key={permission.id} sx={{ mb: 1 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={isActive}
                            onChange={() => handlePermissionToggle(permission.id)}
                            disabled={disabled || isDefault}
                            color={isDefault ? 'primary' : 'secondary'}
                          />
                        }
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="body2" sx={{ fontWeight: isActive ? 'medium' : 'normal' }}>
                                {permission.name}
                                {permission.isNavigation && (
                                  <Chip label="Navigation" size="small" sx={{ ml: 1 }} color="primary" />
                                )}
                              </Typography>
                              {showAdvanced && (
                                <Typography variant="caption" color="text.secondary">
                                  {permission.description}
                                </Typography>
                              )}
                            </Box>
                            <Box sx={{ display: 'flex', gap: 0.5 }}>
                              {isDefault && (
                                <Chip 
                                  label="Role Default" 
                                  size="small" 
                                  color="primary"
                                  variant="outlined"
                                />
                              )}
                              {isCustom && !isDefault && (
                                <Chip 
                                  label="Custom" 
                                  size="small" 
                                  color="secondary"
                                />
                              )}
                            </Box>
                          </Box>
                        }
                        sx={{ 
                          width: '100%',
                          m: 0,
                          '& .MuiFormControlLabel-label': { width: '100%' }
                        }}
                      />
                      {showAdvanced && permission.requiresNav && (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 4, display: 'block' }}>
                          Requires: {permission.requiresNav}
                        </Typography>
                      )}
                    </Box>
                  )
                })}
              </FormGroup>
            </AccordionDetails>
          </Accordion>
        )
      })}

      {/* Permission Summary */}
      <Card sx={{ mt: 2 }}>
        <CardContent>
          <Typography variant="subtitle2" gutterBottom>
            Permission Summary
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Role Permissions: {getRoleDefaultPermissions().length}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Custom Permissions: {permissions.length}
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary">
                Total Active Permissions: {getRoleDefaultPermissions().length + permissions.length}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  )
}

export default PermissionManager
