const express = require('express');
const { catchAsync } = require('../middleware/errorHandler');
const { authorize, authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Define role configurations
const ROLES = {
  admin: {
    name: 'System Administrator',
    description: 'Full access to all system features including user management and system configuration',
    permissions: [
      // Navigation permissions
      'nav_dashboard', 'nav_logs', 'nav_alerts', 'nav_agents', 'nav_reports', 'nav_users', 'nav_settings',
      // Feature permissions
      'view_logs', 'search_logs', 'export_logs',
      'view_alerts', 'manage_alerts',
      'view_agents', 'manage_agents',
      'view_users', 'manage_users',
      'view_reports', 'generate_reports',
      'view_dashboards', 'manage_dashboards',
      'view_settings', 'manage_settings',
      'system_admin',
    ],
  },
  security_analyst: {
    name: 'Security Analyst',
    description: 'Access to logs, alerts, and dashboards with limited configuration capabilities',
    permissions: [
      // Navigation permissions
      'nav_dashboard', 'nav_logs', 'nav_alerts', 'nav_agents', 'nav_reports',
      // Feature permissions
      'view_logs', 'search_logs', 'export_logs',
      'view_alerts', 'manage_alerts',
      'view_agents', 'manage_agents',
      'view_reports', 'generate_reports',
      'view_dashboards', 'manage_dashboards',
    ],
  },
  compliance_officer: {
    name: 'Compliance Officer',
    description: 'Access to compliance reports and logs with limited dashboard access',
    permissions: [
      // Navigation permissions
      'nav_dashboard', 'nav_logs', 'nav_alerts', 'nav_reports',
      // Feature permissions
      'view_logs', 'search_logs', 'export_logs',
      'view_alerts',
      'view_reports', 'generate_reports',
      'view_dashboards',
    ],
  },
  executive: {
    name: 'Executive',
    description: 'Access to executive dashboards and reports without detailed logs or configuration',
    permissions: [
      // Navigation permissions
      'nav_dashboard', 'nav_reports',
      // Feature permissions
      'view_reports',
      'view_dashboards',
    ],
  },
};

// Define all available permissions
const PERMISSIONS = {
  // Navigation permissions (control which tabs/sections are visible)
  nav_dashboard: {
    name: 'Dashboard Navigation',
    description: 'Access to the main dashboard section',
    category: 'Navigation',
    isNavigation: true,
  },
  nav_logs: {
    name: 'Logs Navigation',
    description: 'Access to the logs section',
    category: 'Navigation',
    isNavigation: true,
  },
  nav_alerts: {
    name: 'Alerts Navigation',
    description: 'Access to the alerts section',
    category: 'Navigation',
    isNavigation: true,
  },
  nav_agents: {
    name: 'Agents Navigation',
    description: 'Access to the agents section',
    category: 'Navigation',
    isNavigation: true,
  },
  nav_reports: {
    name: 'Reports Navigation',
    description: 'Access to the reports section',
    category: 'Navigation',
    isNavigation: true,
  },
  nav_users: {
    name: 'Users Navigation',
    description: 'Access to the user management section',
    category: 'Navigation',
    isNavigation: true,
  },
  nav_settings: {
    name: 'Settings Navigation',
    description: 'Access to the settings section',
    category: 'Navigation',
    isNavigation: true,
  },

  // Dashboard permissions
  view_dashboards: {
    name: 'View Dashboards',
    description: 'View dashboard pages and statistics',
    category: 'Dashboard',
    requiresNav: 'nav_dashboard',
  },
  manage_dashboards: {
    name: 'Manage Dashboards',
    description: 'Create, edit, and delete dashboard configurations',
    category: 'Dashboard',
    requiresNav: 'nav_dashboard',
  },

  // Log permissions
  view_logs: {
    name: 'View Logs',
    description: 'View log entries and basic log information',
    category: 'Logs',
    requiresNav: 'nav_logs',
  },
  search_logs: {
    name: 'Search Logs',
    description: 'Search and filter through log entries',
    category: 'Logs',
    requiresNav: 'nav_logs',
  },
  export_logs: {
    name: 'Export Logs',
    description: 'Export log data to various formats',
    category: 'Logs',
    requiresNav: 'nav_logs',
  },

  // Alert permissions
  view_alerts: {
    name: 'View Alerts',
    description: 'View alert notifications and alert history',
    category: 'Alerts',
    requiresNav: 'nav_alerts',
  },
  manage_alerts: {
    name: 'Manage Alerts',
    description: 'Create, edit, delete, and configure alert rules',
    category: 'Alerts',
    requiresNav: 'nav_alerts',
  },

  // Agent permissions
  view_agents: {
    name: 'View Agents',
    description: 'View agent status and basic agent information',
    category: 'Agents',
    requiresNav: 'nav_agents',
  },
  manage_agents: {
    name: 'Manage Agents',
    description: 'Deploy, configure, and manage log collection agents',
    category: 'Agents',
    requiresNav: 'nav_agents',
  },

  // Report permissions
  view_reports: {
    name: 'View Reports',
    description: 'View generated reports and report history',
    category: 'Reports',
    requiresNav: 'nav_reports',
  },
  generate_reports: {
    name: 'Generate Reports',
    description: 'Create, schedule, and export reports',
    category: 'Reports',
    requiresNav: 'nav_reports',
  },

  // User permissions
  view_users: {
    name: 'View Users',
    description: 'View user accounts and user information',
    category: 'Users',
    requiresNav: 'nav_users',
  },
  manage_users: {
    name: 'Manage Users',
    description: 'Create, edit, delete, and manage user accounts',
    category: 'Users',
    requiresNav: 'nav_users',
  },

  // Settings permissions
  view_settings: {
    name: 'View Settings',
    description: 'View system and user settings',
    category: 'Settings',
    requiresNav: 'nav_settings',
  },
  manage_settings: {
    name: 'Manage Settings',
    description: 'Modify system and user settings',
    category: 'Settings',
    requiresNav: 'nav_settings',
  },

  // System permissions
  system_admin: {
    name: 'System Administration',
    description: 'Full system administration capabilities',
    category: 'System',
  },
};

/**
 * @route   GET /api/v1/roles/public
 * @desc    Get role definitions for permission calculation (accessible to all authenticated users)
 * @access  Private (no specific permission required)
 */
router.get('/public', authenticateToken, catchAsync(async (req, res) => {
  const roles = Object.keys(ROLES).map(key => ({
    id: key,
    name: ROLES[key].name,
    permissions: ROLES[key].permissions,
  }));

  res.json({
    status: 'success',
    data: {
      roles,
    },
  });
}));

/**
 * @route   GET /api/v1/roles
 * @desc    Get all available roles (admin only)
 * @access  Private
 */
router.get('/', authorize(['view_users']), catchAsync(async (req, res) => {
  const roles = Object.keys(ROLES).map(key => ({
    id: key,
    ...ROLES[key],
  }));

  res.json({
    status: 'success',
    data: {
      roles,
    },
  });
}));

/**
 * @route   GET /api/v1/roles/permissions
 * @desc    Get all available permissions
 * @access  Private
 */
router.get('/permissions', authorize(['view_users']), catchAsync(async (req, res) => {
  const permissions = Object.keys(PERMISSIONS).map(key => ({
    id: key,
    ...PERMISSIONS[key],
  }));

  // Group permissions by category
  const groupedPermissions = permissions.reduce((acc, permission) => {
    const category = permission.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(permission);
    return acc;
  }, {});

  res.json({
    status: 'success',
    data: {
      permissions,
      groupedPermissions,
    },
  });
}));

/**
 * @route   GET /api/v1/roles/:id
 * @desc    Get role details by ID
 * @access  Private
 */
router.get('/:id', authorize(['view_users']), catchAsync(async (req, res) => {
  const roleId = req.params.id;
  const role = ROLES[roleId];

  if (!role) {
    return res.status(404).json({
      status: 'error',
      message: 'Role not found',
    });
  }

  res.json({
    status: 'success',
    data: {
      role: {
        id: roleId,
        ...role,
      },
    },
  });
}));

module.exports = router;
