<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ExLog Frontend API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>ExLog Frontend API Test</h1>
    <p>This page tests the API connectivity from the frontend perspective.</p>
    
    <div id="results"></div>
    
    <button onclick="testLogin()">Test Login</button>
    <button onclick="testApiHealth()">Test API Health</button>
    <button onclick="clearResults()">Clear Results</button>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function testApiHealth() {
            log('Testing API Health...', 'info');
            
            try {
                const response = await fetch('/api/v1/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API Health Check Success: ${data.status}`, 'success');
                } else {
                    log(`❌ API Health Check Failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ API Health Check Error: ${error.message}`, 'error');
            }
        }
        
        async function testLogin() {
            log('Testing Login...', 'info');
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin123!'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Login Success: ${data.data.user.email} (${data.data.user.role})`, 'success');
                    log(`Token: ${data.data.token.substring(0, 20)}...`, 'info');
                } else {
                    const errorData = await response.json();
                    log(`❌ Login Failed: ${response.status} - ${errorData.message}`, 'error');
                }
            } catch (error) {
                log(`❌ Login Error: ${error.message}`, 'error');
            }
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', function() {
            log('Page loaded. Running automatic tests...', 'info');
            log(`Current URL: ${window.location.href}`, 'info');
            log(`Hostname: ${window.location.hostname}`, 'info');
            log(`Port: ${window.location.port}`, 'info');
            
            setTimeout(() => {
                testApiHealth();
                setTimeout(() => {
                    testLogin();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
