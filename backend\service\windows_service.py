"""
Windows Service Implementation

This module provides Windows service functionality for the Python Logging Agent
using the pywin32 library.
"""

import logging
import sys
import os
import time
from pathlib import Path

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    PYWIN32_AVAILABLE = True
except ImportError:
    PYWIN32_AVAILABLE = False


class PythonLoggingAgentService:
    """Windows service wrapper for the Python Logging Agent."""

    def __init__(self, args=None):
        """Initialize the service."""
        if not PYWIN32_AVAILABLE:
            raise ImportError("pywin32 is required for Windows service functionality")

        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.agent = None
        self.logger = None

    def SvcStop(self):
        """Handle service stop request."""
        try:
            if self.logger:
                self.logger.info("Service stop requested")

            # Signal the service to stop
            win32event.SetEvent(self.hWaitStop)

            # Stop the agent
            if self.agent:
                self.agent.stop()

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error stopping service: {e}")

    def SvcDoRun(self):
        """Main service execution method."""
        try:
            # Log service start
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self.__class__._svc_name_, '')
            )

            # Set up logging for the service
            self._setup_service_logging()

            # Initialize and start the agent
            self._start_agent()

            # Wait for stop signal
            win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)

            # Log service stop
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self.__class__._svc_name_, '')
            )

        except Exception as e:
            # Log error
            servicemanager.LogErrorMsg(f"Service error: {e}")
            if self.logger:
                self.logger.error(f"Service execution error: {e}")

    def _setup_service_logging(self):
        """Set up logging for the service."""
        try:
            # Try to create logs directory in current working directory
            try:
                log_dir = Path("logs")
                log_dir.mkdir(exist_ok=True)
                log_file_path = log_dir / "service.log"
            except Exception:
                # Fallback to temp directory if current directory is not writable
                import tempfile
                temp_dir = Path(tempfile.gettempdir()) / "PythonLoggingAgent"
                temp_dir.mkdir(exist_ok=True)
                log_file_path = temp_dir / "service.log"
                servicemanager.LogInfoMsg(f"Using temp directory for logs: {temp_dir}")

            # Set up basic logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(str(log_file_path)),
                    logging.StreamHandler()
                ]
            )

            self.logger = logging.getLogger(__name__)
            self.logger.info("Service logging initialized")
            self.logger.info(f"Log file: {log_file_path}")

        except Exception as e:
            servicemanager.LogErrorMsg(f"Error setting up service logging: {e}")
            # Create a minimal logger that only logs to Windows Event Log
            self.logger = logging.getLogger(__name__)
            self.logger.addHandler(logging.NullHandler())

    def _start_agent(self):
        """Initialize and start the logging agent."""
        try:
            # Import here to avoid circular imports
            from logging_agent.agent import LoggingAgent

            # Initialize the agent with signals disabled for service mode
            self.agent = LoggingAgent(enable_signals=False)

            # Start the agent
            if self.agent.start():
                self.logger.info("Logging agent started successfully")
            else:
                raise Exception("Failed to start logging agent")

        except Exception as e:
            self.logger.error(f"Error starting agent: {e}")
            raise


# Make the service class compatible with win32serviceutil
if PYWIN32_AVAILABLE:
    class PythonLoggingAgentServiceWin32(win32serviceutil.ServiceFramework, PythonLoggingAgentService):
        """Windows service class that inherits from ServiceFramework."""

        # Service configuration - these must be on the ServiceFramework class
        _svc_name_ = "PythonLoggingAgent"
        _svc_display_name_ = "Python Logging Agent"
        _svc_description_ = "Collects and standardizes Windows logs for security monitoring"

        def __init__(self, args):
            """Initialize the service."""
            # Set up Python path for service execution
            self._setup_python_path()

            win32serviceutil.ServiceFramework.__init__(self, args)
            PythonLoggingAgentService.__init__(self, args)

        def _setup_python_path(self):
            """Set up Python path for service execution."""
            try:
                servicemanager.LogInfoMsg("Starting Python path setup for service")

                # Detect if we're running as a packaged executable
                if getattr(sys, 'frozen', False):
                    # Running as packaged executable
                    servicemanager.LogInfoMsg("Detected packaged executable")

                    # Get the executable path and log it
                    executable_path = Path(sys.executable)
                    servicemanager.LogInfoMsg(f"Executable path: {executable_path}")

                    # For cx_Freeze, we need to find the main installation directory
                    # Start with the executable's parent directory
                    project_root = executable_path.parent
                    servicemanager.LogInfoMsg(f"Initial project root: {project_root}")

                    # Simple approach: just use the directory where the executable is located
                    # This should be the main installation directory like:
                    # C:\Users\<USER>\AppData\Local\programs\Python Logging Agent\

                    # Validate that this looks like a valid installation directory
                    # by checking if it contains expected files/directories
                    expected_items = ['lib', 'LoggingAgent.exe', 'LoggingAgentGUI.exe', 'LoggingAgentService.exe']
                    found_items = []

                    try:
                        for item_name in expected_items:
                            item_path = project_root / item_name
                            if item_path.exists():
                                found_items.append(item_name)

                        servicemanager.LogInfoMsg(f"Found installation items: {found_items}")

                        # If we don't find any expected items, this might not be the right directory
                        if not found_items:
                            servicemanager.LogInfoMsg("No expected installation items found, using executable parent anyway")

                    except Exception as check_error:
                        servicemanager.LogErrorMsg(f"Error checking installation directory: {check_error}")

                else:
                    # Running as Python script
                    servicemanager.LogInfoMsg("Detected Python script execution")
                    service_dir = Path(__file__).parent.absolute()
                    project_root = service_dir.parent
                    servicemanager.LogInfoMsg(f"Using script-based project root: {project_root}")

                # Final validation and setup
                project_root_str = str(project_root)
                servicemanager.LogInfoMsg(f"Final project root: {project_root_str}")

                # Validate that project_root is actually a directory, not a file
                try:
                    if project_root.exists():
                        if project_root.is_dir():
                            servicemanager.LogInfoMsg("Project root is a valid directory")
                        else:
                            servicemanager.LogErrorMsg(f"Project root is not a directory: {project_root_str}")
                            # Use parent directory instead
                            project_root = project_root.parent
                            project_root_str = str(project_root)
                            servicemanager.LogInfoMsg(f"Using parent directory: {project_root_str}")
                    else:
                        servicemanager.LogErrorMsg(f"Project root does not exist: {project_root_str}")
                        # Create fallback directory
                        project_root = Path.home() / "PythonLoggingAgent"
                        project_root.mkdir(exist_ok=True)
                        project_root_str = str(project_root)
                        servicemanager.LogInfoMsg(f"Created fallback directory: {project_root_str}")

                except Exception as validation_error:
                    servicemanager.LogErrorMsg(f"Error validating project root: {validation_error}")
                    # Use safe fallback
                    project_root = Path.home() / "PythonLoggingAgent"
                    project_root.mkdir(exist_ok=True)
                    project_root_str = str(project_root)
                    servicemanager.LogInfoMsg(f"Using safe fallback directory: {project_root_str}")

                # Add project root to Python path
                try:
                    if project_root_str not in sys.path:
                        sys.path.insert(0, project_root_str)
                        servicemanager.LogInfoMsg(f"Added project root to Python path: {project_root_str}")
                    else:
                        servicemanager.LogInfoMsg("Project root already in Python path")
                except Exception as path_error:
                    servicemanager.LogErrorMsg(f"Error adding to Python path: {path_error}")

                # Set working directory
                try:
                    if project_root.exists() and project_root.is_dir():
                        os.chdir(project_root_str)
                        servicemanager.LogInfoMsg(f"Working directory set to: {project_root_str}")
                    else:
                        servicemanager.LogInfoMsg(f"Cannot set working directory, keeping current: {os.getcwd()}")
                except Exception as chdir_error:
                    servicemanager.LogErrorMsg(f"Failed to change working directory: {chdir_error}")
                    servicemanager.LogInfoMsg(f"Keeping current working directory: {os.getcwd()}")

                servicemanager.LogInfoMsg("Python path setup completed successfully")

            except Exception as e:
                # Log error but don't fail initialization
                servicemanager.LogErrorMsg(f"Error setting up service environment: {e}")
                servicemanager.LogInfoMsg("Attempting fallback configuration")

                # Try to continue with a safe directory
                try:
                    # Use a safe fallback directory in user's home
                    safe_dir = Path.home() / "PythonLoggingAgent"
                    safe_dir.mkdir(exist_ok=True)

                    # Create necessary subdirectories
                    (safe_dir / "logs").mkdir(exist_ok=True)
                    (safe_dir / "config").mkdir(exist_ok=True)

                    # Only change directory if the safe directory is valid
                    if safe_dir.exists() and safe_dir.is_dir():
                        os.chdir(str(safe_dir))
                        servicemanager.LogInfoMsg(f"Using safe fallback directory: {safe_dir}")

                        # Add to Python path
                        safe_dir_str = str(safe_dir)
                        if safe_dir_str not in sys.path:
                            sys.path.insert(0, safe_dir_str)
                    else:
                        servicemanager.LogErrorMsg(f"Safe directory creation failed: {safe_dir}")

                except Exception as fallback_error:
                    servicemanager.LogErrorMsg(f"Fallback directory setup failed: {fallback_error}")
                    servicemanager.LogInfoMsg(f"Continuing with current directory: {os.getcwd()}")
                    # Continue with whatever directory we're in - don't fail completely
else:
    PythonLoggingAgentServiceWin32 = None


def install_service():
    """Install the Windows service."""
    if not PYWIN32_AVAILABLE:
        print("Error: pywin32 is required for Windows service functionality")
        return False

    try:
        # Use HandleCommandLine for proper service installation
        win32serviceutil.HandleCommandLine(
            PythonLoggingAgentServiceWin32,
            argv=['', 'install']
        )
        print(f"Service '{PythonLoggingAgentServiceWin32._svc_display_name_}' installed successfully")
        return True

    except Exception as e:
        print(f"Error installing service: {e}")
        return False


def remove_service():
    """Remove the Windows service."""
    if not PYWIN32_AVAILABLE:
        print("Error: pywin32 is required for Windows service functionality")
        return False

    try:
        win32serviceutil.RemoveService(PythonLoggingAgentServiceWin32._svc_name_)
        print(f"Service '{PythonLoggingAgentServiceWin32._svc_display_name_}' removed successfully")
        return True

    except Exception as e:
        print(f"Error removing service: {e}")
        return False


def start_service():
    """Start the Windows service."""
    if not PYWIN32_AVAILABLE:
        print("Error: pywin32 is required for Windows service functionality")
        return False

    try:
        win32serviceutil.StartService(PythonLoggingAgentServiceWin32._svc_name_)
        print(f"Service '{PythonLoggingAgentServiceWin32._svc_display_name_}' started successfully")
        return True

    except Exception as e:
        print(f"Error starting service: {e}")
        return False


def stop_service():
    """Stop the Windows service."""
    if not PYWIN32_AVAILABLE:
        print("Error: pywin32 is required for Windows service functionality")
        return False

    try:
        win32serviceutil.StopService(PythonLoggingAgentServiceWin32._svc_name_)
        print(f"Service '{PythonLoggingAgentServiceWin32._svc_display_name_}' stopped successfully")
        return True

    except Exception as e:
        print(f"Error stopping service: {e}")
        return False


def get_service_status():
    """Get the current service status."""
    if not PYWIN32_AVAILABLE:
        print("Error: pywin32 is required for Windows service functionality")
        return None

    try:
        status = win32serviceutil.QueryServiceStatus(PythonLoggingAgentServiceWin32._svc_name_)

        status_map = {
            win32service.SERVICE_STOPPED: "Stopped",
            win32service.SERVICE_START_PENDING: "Start Pending",
            win32service.SERVICE_STOP_PENDING: "Stop Pending",
            win32service.SERVICE_RUNNING: "Running",
            win32service.SERVICE_CONTINUE_PENDING: "Continue Pending",
            win32service.SERVICE_PAUSE_PENDING: "Pause Pending",
            win32service.SERVICE_PAUSED: "Paused"
        }

        current_state = status_map.get(status[1], "Unknown")
        print(f"Service '{PythonLoggingAgentServiceWin32._svc_display_name_}' status: {current_state}")
        return current_state

    except Exception as e:
        print(f"Error getting service status: {e}")
        return None


def run_service_debug():
    """Run the service in debug mode (console)."""
    if not PYWIN32_AVAILABLE:
        print("Error: pywin32 is required for Windows service functionality")
        return False

    try:
        # Set debug flag
        win32serviceutil.HandleCommandLine(
            PythonLoggingAgentServiceWin32,
            argv=['', 'debug']
        )
        return True

    except Exception as e:
        print(f"Error running service in debug mode: {e}")
        return False


def main():
    """Main entry point for service management."""
    if len(sys.argv) == 1:
        # No arguments - try to start as service
        if PYWIN32_AVAILABLE:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(PythonLoggingAgentServiceWin32)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            print("Error: pywin32 is required for Windows service functionality")
    else:
        # Handle command line arguments
        command = sys.argv[1].lower()

        if command == 'install':
            install_service()
        elif command == 'remove':
            remove_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'status':
            get_service_status()
        elif command == 'debug':
            run_service_debug()
        else:
            print("Usage: python windows_service.py [install|remove|start|stop|status|debug]")


if __name__ == '__main__':
    main()
