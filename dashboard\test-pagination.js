const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

// Test pagination with API key (since JWT tokens expire)
async function testPagination() {
  try {
    console.log('Testing alerts pagination...');
    
    // Test 1: Get first page with 5 alerts per page
    console.log('\n1. Testing page 1, limit 5');
    const response1 = await axios.get(`${API_BASE}/alerts?page=1&limit=5`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    console.log('✓ Success:', {
      alertsCount: response1.data.data.alerts.length,
      pagination: response1.data.data.pagination
    });
    
    // Test 2: Get second page with 5 alerts per page
    console.log('\n2. Testing page 2, limit 5');
    const response2 = await axios.get(`${API_BASE}/alerts?page=2&limit=5`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    console.log('✓ Success:', {
      alertsCount: response2.data.data.alerts.length,
      pagination: response2.data.data.pagination
    });
    
    // Test 3: Get all alerts with larger page size
    console.log('\n3. Testing page 1, limit 25');
    const response3 = await axios.get(`${API_BASE}/alerts?page=1&limit=25`, {
      headers: {
        'X-API-Key': 'test-api-key-12345'
      }
    });
    console.log('✓ Success:', {
      alertsCount: response3.data.data.alerts.length,
      pagination: response3.data.data.pagination
    });
    
    console.log('\n✅ Pagination API is working correctly!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testPagination();
