"""
Linux ExLog Dashboard API Client

This module provides a simple, synchronous API client for sending logs to the ExLog dashboard.
It follows the same successful pattern as the Windows version's simple API client.
"""

import json
import logging
import time
import uuid
import socket
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import threading
import queue

try:
    import requests
except ImportError:
    requests = None


class LinuxExLogAPIClient:
    """
    Simple synchronous API client for sending logs to ExLog dashboard from Linux.
    Uses the same proven pattern as the Windows version.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the API client with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Check for required dependencies
        if not requests:
            raise ImportError("Required dependency missing. Please install: pip3 install requests")
        
        # API configuration
        self.endpoint = config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 5)
        
        # Batch configuration
        self.batch_size = config.get('batch_size', 100)
        self.max_batch_wait_time = config.get('max_batch_wait_time', 5)
        
        # Validation configuration
        validation_config = config.get('validation', {})
        self.fix_missing_fields = validation_config.get('fix_missing_fields', True)
        self.default_source = validation_config.get('default_source', 'System')
        self.default_source_type = validation_config.get('default_source_type', 'syslog')
        self.default_log_level = validation_config.get('default_log_level', 'info')
        
        # Offline buffer configuration
        buffer_config = config.get('offline_buffer', {})
        self.buffer_enabled = buffer_config.get('enabled', True)
        self.buffer_file = buffer_config.get('buffer_file', '/tmp/linux_log_agent_buffer.json')
        self.max_buffer_size = buffer_config.get('max_size', 10000)
        self.retry_interval = buffer_config.get('retry_interval', 60)
        
        # Threading and queuing
        self._running = False
        self._batch_queue = queue.Queue()
        self._batch_thread = None
        self._batch_lock = threading.Lock()
        
        # Statistics
        self.stats = {
            'logs_sent': 0,
            'logs_failed': 0,
            'batches_sent': 0,
            'batches_failed': 0,
            'api_errors': 0,
            'last_success': None,
            'last_error': None,
            'buffer_size': 0
        }
        
        # Get hostname for logs
        self.hostname = socket.gethostname()
        
        self.logger.info(f"Linux ExLog API Client initialized - Endpoint: {self.endpoint}")
    
    def start(self) -> None:
        """Start the API client and background processing."""
        if self._running:
            return
        
        self._running = True
        
        # Create buffer directory if needed
        if self.buffer_enabled:
            os.makedirs(os.path.dirname(self.buffer_file), exist_ok=True)
        
        # Start batch processing thread
        self._batch_thread = threading.Thread(target=self._batch_processor, daemon=True)
        self._batch_thread.start()
        
        # Load any buffered logs
        self._load_buffered_logs()
        
        self.logger.info("Linux ExLog API Client started")
    
    def stop(self) -> None:
        """Stop the API client and clean up."""
        if not self._running:
            return
        
        self._running = False
        
        # Process remaining batches
        self._process_remaining_batches()
        
        # Save any remaining logs to buffer
        if self.buffer_enabled:
            self._save_buffered_logs()
        
        # Wait for batch thread to finish
        if self._batch_thread and self._batch_thread.is_alive():
            self._batch_thread.join(timeout=5)
        
        self.logger.info("Linux ExLog API Client stopped")
    
    def send_logs(self, logs: List[Dict[str, Any]]) -> bool:
        """
        Send logs to the API.
        
        Args:
            logs: List of log entries to send
            
        Returns:
            True if queued successfully, False otherwise
        """
        if not logs:
            return True
        
        if not self._running:
            self.logger.warning("API client not running, cannot send logs")
            return False
        
        try:
            # Validate and fix logs
            validated_logs = []
            for log in logs:
                validated_log = self._validate_and_fix_log(log)
                if validated_log:
                    validated_logs.append(validated_log)
            
            if not validated_logs:
                self.logger.warning("No valid logs to send after validation")
                return False
            
            # Add logs to batch queue
            for log in validated_logs:
                self._batch_queue.put(log)
            
            self.logger.debug(f"Queued {len(validated_logs)} logs for sending")
            return True
            
        except Exception as e:
            self.logger.error(f"Error queuing logs: {e}")
            self.stats['api_errors'] += 1
            return False
    
    def _validate_and_fix_log(self, log: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Validate and fix a log entry to match ExLog API schema exactly like Windows agent."""
        try:
            # Create a copy to avoid modifying original
            fixed_log = log.copy()

            # Ensure required fields exist
            if not fixed_log.get('log_id'):
                if self.fix_missing_fields:
                    fixed_log['log_id'] = str(uuid.uuid4())
                else:
                    self.logger.warning("Log missing required field 'log_id'")
                    return None

            if not fixed_log.get('timestamp'):
                if self.fix_missing_fields:
                    # Use same format as Windows agent (no Z suffix)
                    fixed_log['timestamp'] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                else:
                    self.logger.warning("Log missing required field 'timestamp'")
                    return None

            if not fixed_log.get('source'):
                if self.fix_missing_fields:
                    fixed_log['source'] = self.default_source
                else:
                    self.logger.warning("Log missing required field 'source'")
                    return None

            if not fixed_log.get('source_type'):
                if self.fix_missing_fields:
                    fixed_log['source_type'] = self.default_source_type
                else:
                    self.logger.warning("Log missing required field 'source_type'")
                    return None

            if not fixed_log.get('host'):
                if self.fix_missing_fields:
                    fixed_log['host'] = self.hostname
                else:
                    self.logger.warning("Log missing required field 'host'")
                    return None

            if not fixed_log.get('log_level'):
                if self.fix_missing_fields:
                    fixed_log['log_level'] = self.default_log_level
                else:
                    self.logger.warning("Log missing required field 'log_level'")
                    return None

            if not fixed_log.get('message'):
                if self.fix_missing_fields:
                    fixed_log['message'] = "No message provided"
                else:
                    self.logger.warning("Log missing required field 'message'")
                    return None

            # Ensure additional_fields exists (can be empty)
            if 'additional_fields' not in fixed_log:
                fixed_log['additional_fields'] = {}

            # Ensure raw_data exists (can be None)
            if 'raw_data' not in fixed_log:
                fixed_log['raw_data'] = None

            # Additional validation checks for common issues
            # Check for very long strings that might cause validation errors
            if len(fixed_log.get('message', '')) > 10000:
                self.logger.warning(f"Message too long ({len(fixed_log['message'])} chars), truncating")
                fixed_log['message'] = fixed_log['message'][:10000] + "... [truncated]"

            # Check for invalid characters in log_id
            log_id = fixed_log.get('log_id', '')
            if not log_id.replace('-', '').replace('_', '').isalnum():
                self.logger.warning(f"Invalid log_id format: {log_id}, generating new one")
                fixed_log['log_id'] = str(uuid.uuid4())

            # Ensure timestamp is in proper format (match Windows agent)
            timestamp = fixed_log.get('timestamp', '')
            if timestamp:
                # Remove Z suffix if present to match Windows format
                if timestamp.endswith('Z'):
                    fixed_log['timestamp'] = timestamp[:-1]
                # Remove timezone info if present
                if '+' in timestamp:
                    fixed_log['timestamp'] = timestamp.split('+')[0]

            # Clean up message field - remove problematic Unicode characters (same as Windows)
            message = fixed_log.get('message', '')
            if message:
                # Replace common Unicode characters that might cause issues
                message = message.replace('\u00ae', '(R)')  # Registered trademark
                message = message.replace('\u2122', '(TM)')  # Trademark
                message = message.replace('\u00a9', '(C)')   # Copyright
                # Remove other non-ASCII characters that might cause issues
                message = message.encode('ascii', 'ignore').decode('ascii')
                fixed_log['message'] = message

            # Clean up additional_fields to remove problematic data
            if isinstance(fixed_log.get('additional_fields'), dict):
                additional_fields = fixed_log['additional_fields']

                # Clean string_inserts if they contain Unicode characters
                if 'string_inserts' in additional_fields and isinstance(additional_fields['string_inserts'], list):
                    cleaned_inserts = []
                    for insert in additional_fields['string_inserts']:
                        if isinstance(insert, str):
                            # Clean Unicode characters from string inserts
                            cleaned_insert = insert.replace('\u00ae', '(R)').replace('\u2122', '(TM)').replace('\u00a9', '(C)')
                            cleaned_insert = cleaned_insert.encode('ascii', 'ignore').decode('ascii')
                            cleaned_inserts.append(cleaned_insert)
                        else:
                            cleaned_inserts.append(insert)
                    additional_fields['string_inserts'] = cleaned_inserts

            return fixed_log

        except Exception as e:
            self.logger.error(f"Error validating log: {e}")
            return None
    
    def _batch_processor(self) -> None:
        """Background thread that processes batches of logs."""
        current_batch = []
        last_batch_time = time.time()
        
        while self._running:
            try:
                # Try to get a log from the queue
                try:
                    log = self._batch_queue.get(timeout=1)
                    current_batch.append(log)
                except queue.Empty:
                    pass
                
                # Check if we should send the current batch
                should_send = (
                    len(current_batch) >= self.batch_size or
                    (current_batch and time.time() - last_batch_time >= self.max_batch_wait_time)
                )
                
                if should_send and current_batch:
                    if self._send_batch(current_batch):
                        self.stats['batches_sent'] += 1
                        self.stats['logs_sent'] += len(current_batch)
                        self.stats['last_success'] = datetime.now().isoformat()
                    else:
                        self.stats['batches_failed'] += 1
                        self.stats['logs_failed'] += len(current_batch)
                        
                        # Buffer failed logs if enabled
                        if self.buffer_enabled:
                            self._buffer_logs(current_batch)
                    
                    current_batch = []
                    last_batch_time = time.time()
                
            except Exception as e:
                self.logger.error(f"Error in batch processor: {e}")
                self.stats['api_errors'] += 1
                time.sleep(1)
        
        # Process final batch
        if current_batch:
            self._send_batch(current_batch)
    
    def _send_batch(self, logs: List[Dict[str, Any]]) -> bool:
        """Send a batch of logs to the API using the same method as the Windows version."""
        if not logs:
            return False
        
        # Debug: Log the first few log IDs being sent
        log_ids = [log.get('log_id', 'unknown') for log in logs[:3]]
        self.logger.debug(f"Sending batch of {len(logs)} logs, first few IDs: {log_ids}")
        
        # Prepare payload (exactly like the Windows test file)
        payload = {"logs": logs}
        
        # Prepare headers (exactly like the Windows test file)
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key
        }
        
        for attempt in range(self.max_retries + 1):
            try:
                # Send request (exactly like the Windows test file)
                response = requests.post(
                    self.endpoint, 
                    json=payload, 
                    headers=headers, 
                    timeout=self.timeout
                )
                
                if response.status_code in [200, 201]:
                    self.logger.debug(f"Successfully sent batch of {len(logs)} logs")
                    return True
                else:
                    self.logger.warning(f"API returned status {response.status_code}: {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"Request timeout on attempt {attempt + 1}")
            except requests.exceptions.ConnectionError:
                self.logger.warning(f"Connection error on attempt {attempt + 1}")
            except Exception as e:
                self.logger.error(f"Unexpected error sending batch: {e}")
            
            # Wait before retry (except on last attempt)
            if attempt < self.max_retries:
                wait_time = self.retry_delay * (2 ** attempt)  # Exponential backoff
                self.logger.debug(f"Waiting {wait_time}s before retry")
                time.sleep(wait_time)
        
        self.logger.error(f"Failed to send batch after {self.max_retries + 1} attempts")
        self.stats['last_error'] = datetime.now().isoformat()
        return False
    
    def _buffer_logs(self, logs: List[Dict[str, Any]]) -> None:
        """Buffer logs to file for later retry."""
        if not self.buffer_enabled or not logs:
            return
        
        try:
            # Load existing buffer
            buffered_logs = []
            if os.path.exists(self.buffer_file):
                with open(self.buffer_file, 'r') as f:
                    buffered_logs = json.load(f)
            
            # Add new logs
            buffered_logs.extend(logs)
            
            # Limit buffer size
            if len(buffered_logs) > self.max_buffer_size:
                buffered_logs = buffered_logs[-self.max_buffer_size:]
                self.logger.warning(f"Buffer size exceeded, keeping last {self.max_buffer_size} logs")
            
            # Save buffer
            with open(self.buffer_file, 'w') as f:
                json.dump(buffered_logs, f)
            
            self.stats['buffer_size'] = len(buffered_logs)
            self.logger.debug(f"Buffered {len(logs)} logs, total buffer size: {len(buffered_logs)}")
            
        except Exception as e:
            self.logger.error(f"Error buffering logs: {e}")
    
    def _load_buffered_logs(self) -> None:
        """Load and retry buffered logs."""
        if not self.buffer_enabled or not os.path.exists(self.buffer_file):
            return
        
        try:
            with open(self.buffer_file, 'r') as f:
                buffered_logs = json.load(f)
            
            if buffered_logs:
                self.logger.info(f"Loading {len(buffered_logs)} buffered logs")
                
                # Add to queue for processing
                for log in buffered_logs:
                    self._batch_queue.put(log)
                
                # Clear buffer file
                os.remove(self.buffer_file)
                self.stats['buffer_size'] = 0
                
        except Exception as e:
            self.logger.error(f"Error loading buffered logs: {e}")
    
    def _save_buffered_logs(self) -> None:
        """Save remaining queued logs to buffer."""
        if not self.buffer_enabled:
            return
        
        remaining_logs = []
        while not self._batch_queue.empty():
            try:
                log = self._batch_queue.get_nowait()
                remaining_logs.append(log)
            except queue.Empty:
                break
        
        if remaining_logs:
            self._buffer_logs(remaining_logs)
    
    def _process_remaining_batches(self) -> None:
        """Process any remaining batches in the queue."""
        remaining_logs = []
        
        # Collect all remaining logs
        while not self._batch_queue.empty():
            try:
                log = self._batch_queue.get_nowait()
                remaining_logs.append(log)
            except queue.Empty:
                break
        
        # Send in batches
        if remaining_logs:
            for i in range(0, len(remaining_logs), self.batch_size):
                batch = remaining_logs[i:i + self.batch_size]
                self._send_batch(batch)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get API client statistics."""
        stats = self.stats.copy()
        stats['queue_size'] = self._batch_queue.qsize()
        stats['running'] = self._running
        return stats
