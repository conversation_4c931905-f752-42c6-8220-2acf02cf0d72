"""
ExLog Dashboard API Client

This module provides functionality to send logs to the ExLog dashboard API
using a simple, synchronous approach that matches the working test file.
"""

import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import threading
import queue

try:
    import requests
except ImportError:
    requests = None


class ExLogAPIClient:
    """
    Simple synchronous API client for sending logs to ExLog dashboard.

    Features:
    - Batch processing with configurable batch sizes
    - Retry logic with exponential backoff
    - Offline buffering when API is unavailable
    - Log validation and field fixing
    - Simple synchronous requests (like the working test file)
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the API client with configuration.

        Args:
            config: Configuration dictionary for the API client
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Check for required dependencies
        if not requests:
            raise ImportError(
                "Required dependency missing. Please install: pip install requests"
            )
        
        # API configuration
        self.endpoint = config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 5)

        # Batch configuration
        self.batch_size = config.get('batch_size', 10)
        self.max_batch_wait_time = config.get('max_batch_wait_time', 5)

        # Validation configuration
        validation_config = config.get('validation', {})
        self.fix_missing_fields = validation_config.get('fix_missing_fields', True)
        self.default_source = validation_config.get('default_source', 'System')
        self.default_source_type = validation_config.get('default_source_type', 'event')
        self.default_log_level = validation_config.get('default_log_level', 'info')

        # Internal state
        self._running = False
        self._batch_queue = queue.Queue()
        self._send_thread = None

        # Statistics
        self.stats = {
            'logs_sent': 0,
            'logs_failed': 0,
            'batches_sent': 0,
            'batches_failed': 0,
            'api_errors': 0,
            'last_successful_send': None,
            'last_error': None
        }

        self.logger.info(f"Simple API client configured: {self.endpoint}")

    def start(self) -> None:
        """Start the API client and background processing."""
        if self._running:
            return
            
        self._running = True
        
        # Create aiohttp session
        connector = aiohttp.TCPConnector(
            limit=self.config.get('connection_pool_size', 10),
            limit_per_host=5
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': self.api_key
        }
        
        self._session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=headers
        )
        
        # Start background threads
        self._send_thread = threading.Thread(target=self._batch_sender_loop, daemon=True)
        self._send_thread.start()
        
        if self.buffer_enabled:
            self._retry_thread = threading.Thread(target=self._retry_buffer_loop, daemon=True)
            self._retry_thread.start()
            
            # Load existing buffer
            self._load_offline_buffer()
        
        self.logger.info("Async API client started")
        
        # Test connection
        await self._test_connection()
    
    async def stop(self) -> None:
        """Stop the API client and cleanup resources."""
        if not self._running:
            return
            
        self._running = False
        
        # Process remaining batches
        await self._process_remaining_batches()
        
        # Save offline buffer
        if self.buffer_enabled:
            self._save_offline_buffer()
        
        # Close session
        if self._session:
            await self._session.close()
            self._session = None
        
        self.logger.info("Async API client stopped")
    
    def send_logs(self, logs: List[Dict[str, Any]]) -> None:
        """
        Queue logs for sending to the API.
        
        Args:
            logs: List of log entries to send
        """
        if not logs:
            return
            
        # Validate and fix logs
        validated_logs = []
        for log in logs:
            validated_log = self._validate_and_fix_log(log)
            if validated_log:
                validated_logs.append(validated_log)
        
        if not validated_logs:
            return
        
        # Add to batch queue
        for log in validated_logs:
            try:
                self._batch_queue.put_nowait(log)
            except queue.Full:
                self.logger.warning("Batch queue is full, dropping log")
                self.stats['logs_failed'] += 1
    
    def _validate_and_fix_log(self, log: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Validate and fix a log entry to match ExLog API schema.
        
        Args:
            log: Original log entry
            
        Returns:
            Fixed log entry or None if unfixable
        """
        try:
            # Create a copy to avoid modifying original
            fixed_log = log.copy()
            
            # Ensure required fields exist
            if not fixed_log.get('log_id'):
                if self.fix_missing_fields:
                    import uuid
                    fixed_log['log_id'] = str(uuid.uuid4())
                else:
                    self.logger.warning("Log missing required field 'log_id'")
                    return None
            
            if not fixed_log.get('timestamp'):
                if self.fix_missing_fields:
                    fixed_log['timestamp'] = datetime.now().isoformat()
                else:
                    self.logger.warning("Log missing required field 'timestamp'")
                    return None
            
            if not fixed_log.get('source'):
                if self.fix_missing_fields:
                    fixed_log['source'] = self.default_source
                else:
                    self.logger.warning("Log missing required field 'source'")
                    return None
            
            if not fixed_log.get('source_type'):
                if self.fix_missing_fields:
                    fixed_log['source_type'] = self.default_source_type
                else:
                    self.logger.warning("Log missing required field 'source_type'")
                    return None
            
            if not fixed_log.get('host'):
                if self.fix_missing_fields:
                    import socket
                    fixed_log['host'] = socket.gethostname()
                else:
                    self.logger.warning("Log missing required field 'host'")
                    return None
            
            if not fixed_log.get('log_level'):
                if self.fix_missing_fields:
                    fixed_log['log_level'] = self.default_log_level
                else:
                    self.logger.warning("Log missing required field 'log_level'")
                    return None
            
            if not fixed_log.get('message'):
                if self.fix_missing_fields:
                    fixed_log['message'] = "No message provided"
                else:
                    self.logger.warning("Log missing required field 'message'")
                    return None
            
            # Ensure additional_fields exists (can be empty)
            if 'additional_fields' not in fixed_log:
                fixed_log['additional_fields'] = {}
            
            # Ensure raw_data exists (can be None)
            if 'raw_data' not in fixed_log:
                fixed_log['raw_data'] = None
            
            return fixed_log

        except Exception as e:
            self.logger.error(f"Error validating log: {e}")
            return None

    def _batch_sender_loop(self) -> None:
        """Background thread loop for sending batches."""
        batch = []
        last_batch_time = time.time()

        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            while self._running:
                try:
                    # Try to get a log from the queue
                    try:
                        log = self._batch_queue.get(timeout=1.0)
                        batch.append(log)
                    except queue.Empty:
                        pass

                    current_time = time.time()

                    # Send batch if it's full or max wait time exceeded
                    should_send = (
                        len(batch) >= self.batch_size or
                        (batch and (current_time - last_batch_time) >= self.max_batch_wait_time)
                    )

                    if should_send and batch:
                        loop.run_until_complete(self._send_batch(batch))
                        batch = []
                        last_batch_time = current_time

                except Exception as e:
                    self.logger.error(f"Error in batch sender loop: {e}")
                    time.sleep(1)

            # Send any remaining logs
            if batch:
                loop.run_until_complete(self._send_batch(batch))

        finally:
            # Clean up the event loop
            try:
                loop.close()
            except Exception as e:
                self.logger.error(f"Error closing event loop: {e}")

    async def _send_batch(self, logs: List[Dict[str, Any]]) -> bool:
        """
        Send a batch of logs to the API.

        Args:
            logs: List of log entries to send

        Returns:
            True if successful, False otherwise
        """
        if not logs or not self._session:
            return False

        # Prepare payload
        payload = {"logs": logs}

        # Debug: Log the first few log IDs being sent
        log_ids = [log.get('log_id', 'unknown') for log in logs[:3]]
        self.logger.debug(f"Sending batch of {len(logs)} logs, first few IDs: {log_ids}")

        for attempt in range(self.max_retries + 1):
            try:
                async with self._session.post(self.endpoint, json=payload) as response:
                    response_text = await response.text()

                    if response.status == 200 or response.status == 201:
                        # Success
                        self.stats['logs_sent'] += len(logs)
                        self.stats['batches_sent'] += 1
                        self.stats['last_successful_send'] = datetime.now()

                        self.logger.info(f"Successfully sent batch of {len(logs)} logs")

                        # Debug: Log response details
                        try:
                            import json
                            response_data = json.loads(response_text)
                            processed = response_data.get('data', {}).get('processed', 0)
                            failed = response_data.get('data', {}).get('failed', 0)
                            self.logger.info(f"API response: processed={processed}, failed={failed}")

                            if failed > 0:
                                failed_logs = response_data.get('data', {}).get('results', {}).get('failed', [])
                                for failed_log in failed_logs[:3]:  # Show first 3 failures
                                    self.logger.warning(f"Failed log: {failed_log.get('logId')} - {failed_log.get('error')}")
                        except:
                            pass

                        return True

                    elif response.status == 400:
                        # Bad request - don't retry
                        response_text = await response.text()
                        self.logger.error(f"API request failed with status {response.status}: {response_text}")
                        self.stats['api_errors'] += 1
                        self.stats['logs_failed'] += len(logs)
                        self.stats['batches_failed'] += 1
                        self.stats['last_error'] = f"HTTP {response.status}: {response_text}"
                        return False

                    else:
                        # Server error - retry
                        response_text = await response.text()
                        self.logger.warning(f"API request failed with status {response.status}: {response_text}")

                        if attempt < self.max_retries:
                            delay = self.retry_delay * (2 ** attempt)  # Exponential backoff
                            self.logger.info(f"Retrying in {delay} seconds (attempt {attempt + 1}/{self.max_retries})")
                            await asyncio.sleep(delay)
                        else:
                            # Final attempt failed
                            self.stats['api_errors'] += 1
                            self.stats['logs_failed'] += len(logs)
                            self.stats['batches_failed'] += 1
                            self.stats['last_error'] = f"HTTP {response.status}: {response_text}"

                            # Add to offline buffer if enabled
                            if self.buffer_enabled:
                                self._add_to_offline_buffer(logs)

                            return False

            except Exception as e:
                self.logger.error(f"Error sending batch (attempt {attempt + 1}): {e}")

                if attempt < self.max_retries:
                    delay = self.retry_delay * (2 ** attempt)
                    self.logger.info(f"Retrying in {delay} seconds")
                    await asyncio.sleep(delay)
                else:
                    # Final attempt failed
                    self.stats['api_errors'] += 1
                    self.stats['logs_failed'] += len(logs)
                    self.stats['batches_failed'] += 1
                    self.stats['last_error'] = str(e)

                    # Add to offline buffer if enabled
                    if self.buffer_enabled:
                        self._add_to_offline_buffer(logs)

                    return False

        return False

    def _add_to_offline_buffer(self, logs: List[Dict[str, Any]]) -> None:
        """Add logs to offline buffer."""
        try:
            # Check buffer size limit
            if len(self._offline_buffer) + len(logs) > self.max_buffer_size:
                # Remove oldest logs to make space
                overflow = len(self._offline_buffer) + len(logs) - self.max_buffer_size
                self._offline_buffer = self._offline_buffer[overflow:]
                self.logger.warning(f"Offline buffer overflow, removed {overflow} oldest logs")

            # Add new logs with timestamp
            for log in logs:
                buffered_log = {
                    'log': log,
                    'buffered_at': datetime.now().isoformat()
                }
                self._offline_buffer.append(buffered_log)

            self.logger.info(f"Added {len(logs)} logs to offline buffer (total: {len(self._offline_buffer)})")

        except Exception as e:
            self.logger.error(f"Error adding logs to offline buffer: {e}")

    def _retry_buffer_loop(self) -> None:
        """Background thread loop for retrying buffered logs."""
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            while self._running:
                try:
                    current_time = time.time()

                    # Check if it's time to retry
                    if (current_time - self._last_retry_time) >= self.retry_interval and self._offline_buffer:
                        self._last_retry_time = current_time

                        # Try to send buffered logs
                        logs_to_retry = [item['log'] for item in self._offline_buffer[:self.batch_size]]

                        if logs_to_retry:
                            success = loop.run_until_complete(self._send_batch(logs_to_retry))

                            if success:
                                # Remove successfully sent logs from buffer
                                self._offline_buffer = self._offline_buffer[len(logs_to_retry):]
                                self.logger.info(f"Successfully sent {len(logs_to_retry)} buffered logs")
                            else:
                                self.logger.warning("Failed to send buffered logs, will retry later")

                    time.sleep(10)  # Check every 10 seconds

                except Exception as e:
                    self.logger.error(f"Error in retry buffer loop: {e}")
                    time.sleep(30)

        finally:
            # Clean up the event loop
            try:
                loop.close()
            except Exception as e:
                self.logger.error(f"Error closing retry buffer event loop: {e}")

    def _load_offline_buffer(self) -> None:
        """Load offline buffer from file."""
        try:
            buffer_path = Path(self.buffer_file)
            if buffer_path.exists():
                with open(buffer_path, 'r', encoding='utf-8') as f:
                    self._offline_buffer = json.load(f)

                self.logger.info(f"Loaded {len(self._offline_buffer)} logs from offline buffer")

        except Exception as e:
            self.logger.error(f"Error loading offline buffer: {e}")
            self._offline_buffer = []

    def _save_offline_buffer(self) -> None:
        """Save offline buffer to file."""
        try:
            if not self._offline_buffer:
                return

            buffer_path = Path(self.buffer_file)
            buffer_path.parent.mkdir(parents=True, exist_ok=True)

            with open(buffer_path, 'w', encoding='utf-8') as f:
                json.dump(self._offline_buffer, f, ensure_ascii=False, indent=2)

            self.logger.info(f"Saved {len(self._offline_buffer)} logs to offline buffer")

        except Exception as e:
            self.logger.error(f"Error saving offline buffer: {e}")

    async def _test_connection(self) -> bool:
        """Test API connection."""
        try:
            test_log = {
                "log_id": "test-connection-001",
                "timestamp": datetime.now().isoformat(),
                "source": "System",
                "source_type": "event",
                "host": "test-host",
                "log_level": "info",
                "message": "API connection test",
                "raw_data": None,
                "additional_fields": {}
            }

            payload = {"logs": [test_log]}

            async with self._session.post(self.endpoint, json=payload) as response:
                if response.status in [200, 201, 400]:  # 400 is OK for connection test
                    self.logger.info("API connection test successful")
                    return True
                else:
                    response_text = await response.text()
                    self.logger.warning(f"API connection test failed: {response.status} - {response_text}")
                    return False

        except Exception as e:
            self.logger.error(f"API connection test failed: {e}")
            return False

    async def _process_remaining_batches(self) -> None:
        """Process any remaining batches in the queue."""
        remaining_logs = []

        # Collect all remaining logs
        while not self._batch_queue.empty():
            try:
                log = self._batch_queue.get_nowait()
                remaining_logs.append(log)
            except queue.Empty:
                break

        # Send in batches
        if remaining_logs:
            for i in range(0, len(remaining_logs), self.batch_size):
                batch = remaining_logs[i:i + self.batch_size]
                await self._send_batch(batch)

    def get_stats(self) -> Dict[str, Any]:
        """Get API client statistics."""
        stats = self.stats.copy()
        stats['offline_buffer_size'] = len(self._offline_buffer)
        stats['queue_size'] = self._batch_queue.qsize()
        stats['running'] = self._running
        return stats
