const mongoose = require('mongoose');
const logger = require('../utils/logger');

class DatabaseManager {
  constructor() {
    this.mongodb = null;
  }

  async connectMongoDB() {
    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://mongodb:27017/exlog';

      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });

      this.mongodb = mongoose.connection;

      this.mongodb.on('error', (error) => {
        logger.error('MongoDB connection error:', error);
      });

      this.mongodb.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
      });

      logger.info('MongoDB connected successfully');
      return this.mongodb;
    } catch (error) {
      logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }



  async connectAll() {
    try {
      await this.connectMongoDB();
      logger.info('Database connected successfully');
    } catch (error) {
      logger.error('Database connection failed:', error);
      throw new Error('Database (MongoDB) connection failed');
    }
  }

  async disconnect() {
    try {
      if (this.mongodb) {
        await mongoose.disconnect();
      }

      logger.info('Database connection closed');
    } catch (error) {
      logger.error('Error closing database connection:', error);
      throw error;
    }
  }

  getConnections() {
    return {
      mongodb: this.mongodb,
    };
  }
}

module.exports = new DatabaseManager();
