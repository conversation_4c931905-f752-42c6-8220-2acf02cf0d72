#!/usr/bin/env python3
"""
Test script for Linux ExLog API Client

This script tests the API client functionality by sending sample logs
to the ExLog dashboard API from the Linux agent.
"""

import json
import sys
import time
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from utils.api_client import LinuxExLogAPIClient
    from config.config_manager import ConfigManager
    API_CLIENT_AVAILABLE = True
except ImportError as e:
    print(f"Error importing API client: {e}")
    print("Please install dependencies: pip3 install -r requirements.txt")
    API_CLIENT_AVAILABLE = False


def create_sample_linux_logs(count: int = 5):
    """Create sample Linux logs for testing (match Windows agent format exactly)."""
    logs = []

    sample_messages = [
        "systemd[1]: Started User Manager for UID 1000.",
        "kernel: [12345.678901] USB disconnect, address 1",
        "sshd[1234]: Accepted publickey for user from ************* port 22 ssh2",
        "NetworkManager[567]: <info> device (eth0): link connected",
        "apache2[890]: [error] [client ************] File does not exist: /var/www/html/missing.php"
    ]

    import socket
    hostname = socket.gethostname()

    for i in range(count):
        # Create timestamp in same format as Windows agent (no Z suffix)
        timestamp = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

        log = {
            "log_id": f"linux-test-log-{i+1:03d}",
            "timestamp": timestamp,
            "source": "System",  # Use same source as Windows for consistency
            "source_type": "event",  # Use 'event' like Windows agent
            "host": hostname,  # Use actual hostname
            "log_level": "info",  # Use consistent log level
            "message": sample_messages[i % len(sample_messages)],
            "raw_data": None,
            "additional_fields": {
                "record_number": 70000 + i,
                "computer_name": hostname,
                "string_inserts": ["test", "value", str(i)],
                "data": None,
                "event_id": 1000 + i,
                "event_category": 0,
                "event_type": 4,
                "metadata": {
                    "collection_time": datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f'),
                    "agent_version": "1.0.0",
                    "standardizer_version": "1.0.0",
                    "linux_agent": True,
                    "event_log_source": "System"
                }
            }
        }
        logs.append(log)

    return logs


def test_api_client():
    """Test the Linux API client functionality."""
    if not API_CLIENT_AVAILABLE:
        print("API client not available. Cannot run test.")
        return False
    
    try:
        print("Linux ExLog API Client Test")
        print("=" * 40)
        
        # Load configuration
        print("Loading configuration...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        api_config = config.get('exlog_api', {})
        
        if not api_config.get('enabled', False):
            print("ExLog API is disabled in configuration.")
            print("Please enable it in config/default_config.yaml")
            return False
        
        print(f"API Endpoint: {api_config.get('endpoint')}")
        print(f"API Key: {'*' * len(api_config.get('api_key', ''))}")
        print(f"Batch Size: {api_config.get('batch_size', 100)}")
        print()
        
        # Create API client
        print("Creating Linux API client...")
        client = LinuxExLogAPIClient(api_config)
        
        # Start the client
        print("Starting API client...")
        client.start()
        print("API client started successfully!")
        print()
        
        # Create sample logs
        sample_logs = create_sample_linux_logs(10)
        print(f"Created {len(sample_logs)} sample Linux logs")
        
        # Display first log for verification
        print("\nSample log entry:")
        print(json.dumps(sample_logs[0], indent=2))
        print()
        
        # Send logs
        print("Sending logs to API...")
        success = client.send_logs(sample_logs)
        
        if success:
            print("✓ Logs queued successfully")
        else:
            print("✗ Failed to queue logs")
            return False
        
        # Wait for processing
        print("Waiting for logs to be processed...")
        time.sleep(10)
        
        # Get statistics
        stats = client.get_stats()
        print("\nAPI Client Statistics:")
        print(f"  Logs sent: {stats['logs_sent']}")
        print(f"  Logs failed: {stats['logs_failed']}")
        print(f"  Batches sent: {stats['batches_sent']}")
        print(f"  Batches failed: {stats['batches_failed']}")
        print(f"  API errors: {stats['api_errors']}")
        print(f"  Last success: {stats['last_success']}")
        print(f"  Last error: {stats['last_error']}")
        print(f"  Queue size: {stats['queue_size']}")
        
        # Stop the client
        print("\nStopping API client...")
        client.stop()
        print("API client stopped")
        
        # Determine success
        if stats['logs_sent'] > 0:
            print("\n✓ Test completed successfully!")
            print(f"Successfully sent {stats['logs_sent']} logs to ExLog dashboard")
            return True
        else:
            print("\n✗ Test failed!")
            print("No logs were successfully sent to the API")
            return False
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        return False


def test_synchronous():
    """Test the API client using synchronous requests library."""
    try:
        import requests
        
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        api_config = config.get('exlog_api', {})
        
        endpoint = api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        api_key = api_config.get('api_key', '')
        
        print("\nTesting with synchronous requests...")
        print(f"Endpoint: {endpoint}")
        
        # Create sample logs
        sample_logs = create_sample_linux_logs(3)
        
        # Prepare payload
        payload = {"logs": sample_logs}
        
        # Send request
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        }
        
        print("Sending request...")
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.text:
            print(f"Response Body: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✓ Synchronous test successful!")
            return True
        else:
            print("✗ Synchronous test failed!")
            return False
            
    except ImportError:
        print("requests library not available for synchronous test")
        return False
    except Exception as e:
        print(f"Synchronous test error: {e}")
        return False


def main():
    """Main test function."""
    print("Linux Log Collection Agent - API Client Test")
    print("=" * 50)
    
    # Test main API client
    success1 = test_api_client()
    
    # Test synchronous method
    success2 = test_synchronous()
    
    print("\n" + "=" * 50)
    if success1 or success2:
        print("✓ At least one test method succeeded!")
        print("The Linux agent should be able to send logs to ExLog dashboard.")
        return 0
    else:
        print("✗ All tests failed!")
        print("Please check your configuration and network connectivity.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
