"""
Test script for GUI components
Run this to validate the GUI works before building the MSI
"""

import sys
import os
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import flet as ft
        print("OK Flet imported successfully")
    except ImportError as e:
        print(f"ERROR Flet import failed: {e}")
        return False

    try:
        from gui.utils.theme import AppTheme
        print("OK Theme module imported")
    except ImportError as e:
        print(f"ERROR Theme import failed: {e}")
        return False

    try:
        from gui.utils.logger import setup_gui_logger
        print("OK Logger module imported")
    except ImportError as e:
        print(f"ERROR Logger import failed: {e}")
        return False

    try:
        from gui.components.main_window import MainWindow
        print("OK Main window component imported")
    except ImportError as e:
        print(f"ERROR Main window import failed: {e}")
        return False

    try:
        from config.config_manager import ConfigManager
        print("OK Config manager imported")
    except ImportError as e:
        print(f"ERROR Config manager import failed: {e}")
        return False

    try:
        from service.windows_service import get_service_status
        print("OK Service module imported")
    except ImportError as e:
        print(f"ERROR Service import failed: {e}")
        return False
    
    print("All imports successful!")
    return True

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        config = config_manager.load_config()

        print("OK Configuration loaded successfully")
        print(f"  - API endpoint: {config.get('exlog_api', {}).get('endpoint', 'Not set')}")
        print(f"  - Log level: {config.get('general', {}).get('log_level', 'Not set')}")

        return True

    except Exception as e:
        print(f"ERROR Configuration test failed: {e}")
        return False

def test_logging():
    """Test logging setup"""
    print("\nTesting logging...")
    
    try:
        from gui.utils.logger import setup_gui_logger
        
        logger = setup_gui_logger("test")
        logger.info("Test log message")
        logger.warning("Test warning message")
        logger.error("Test error message")

        print("OK Logging setup successful")
        return True

    except Exception as e:
        print(f"ERROR Logging test failed: {e}")
        return False

def test_theme():
    """Test theme configuration"""
    print("\nTesting theme...")
    
    try:
        from gui.utils.theme import AppTheme
        
        theme = AppTheme()
        flet_theme = theme.get_theme()
        card_style = theme.get_card_style()
        button_style = theme.get_button_style()

        print("OK Theme configuration successful")
        print(f"  - Primary color: {theme.PRIMARY_COLOR}")
        print(f"  - Card style keys: {list(card_style.keys())}")

        return True

    except Exception as e:
        print(f"ERROR Theme test failed: {e}")
        return False

def test_service_status():
    """Test service status checking"""
    print("\nTesting service status...")
    
    try:
        from service.windows_service import get_service_status
        
        status = get_service_status()
        print(f"OK Service status check completed: {status}")

        return True

    except Exception as e:
        print(f"ERROR Service status test failed: {e}")
        return False

def test_gui_components():
    """Test GUI component creation (without running the app)"""
    print("\nTesting GUI components...")
    
    try:
        import flet as ft
        from gui.components.dashboard import Dashboard
        from gui.components.configuration import Configuration
        from gui.components.service_control import ServiceControl
        from gui.utils.logger import setup_gui_logger
        
        # Create a mock page
        logger = setup_gui_logger("test")
        
        # Test component creation (without initialization)
        dashboard = Dashboard(None, logger)
        config_comp = Configuration(None, logger)
        service_comp = ServiceControl(None, logger)

        print("OK GUI components created successfully")
        return True

    except Exception as e:
        print(f"ERROR GUI components test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Python Logging Agent - GUI Test Suite")
    print("="*50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("Logging Test", test_logging),
        ("Theme Test", test_theme),
        ("Service Status Test", test_service_status),
        ("GUI Components Test", test_gui_components)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"ERROR {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "="*50)
    print("TEST RESULTS")
    print("="*50)
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    if failed == 0:
        print("\nOK All tests passed! GUI is ready for building.")
        return True
    else:
        print(f"\nERROR {failed} test(s) failed. Please fix issues before building.")
        return False

if __name__ == "__main__":
    success = main()
    
    print("\nTo run the GUI manually:")
    print("python launch_gui.py")
    
    print("\nTo build the MSI installer:")
    print("python build_msi.py")
    
    sys.exit(0 if success else 1)
