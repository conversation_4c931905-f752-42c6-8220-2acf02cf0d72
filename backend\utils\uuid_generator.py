"""
UUID Generator Utility

This module provides UUID generation functionality for log entries,
similar to Elasticsearch's document IDs.
"""

import uuid
import time
import hashlib
from typing import Optional, Dict, Any
from enum import Enum


class UUIDFormat(Enum):
    """Supported UUID formats for log entries."""
    UUID4 = "uuid4"  # Random UUID (default, similar to Elasticsearch)
    UUID1 = "uuid1"  # Time-based UUID
    UUID3 = "uuid3"  # Name-based UUID using MD5
    UUID5 = "uuid5"  # Name-based UUID using SHA-1
    CUSTOM = "custom"  # Custom format with timestamp and random components


class UUIDGenerator:
    """
    UUID generator for log entries with support for multiple formats.
    
    This class provides Elasticsearch-like UUID generation for log entries,
    ensuring each log has a unique identifier for tracking and correlation.
    """
    
    def __init__(self, uuid_format: UUIDFormat = UUIDFormat.UUID4, namespace: Optional[str] = None):
        """
        Initialize the UUID generator.
        
        Args:
            uuid_format: The UUID format to use
            namespace: Namespace for name-based UUIDs (UUID3/UUID5)
        """
        self.uuid_format = uuid_format
        self.namespace = namespace
        self._namespace_uuid = None
        
        # Create namespace UUID for name-based generation
        if namespace and uuid_format in [UUIDFormat.UUID3, UUIDFormat.UUID5]:
            self._namespace_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, namespace)
    
    def generate_log_id(self, log_entry: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a unique identifier for a log entry.
        
        Args:
            log_entry: Optional log entry data for name-based UUIDs
            
        Returns:
            Unique identifier string
        """
        if self.uuid_format == UUIDFormat.UUID4:
            return str(uuid.uuid4())
        
        elif self.uuid_format == UUIDFormat.UUID1:
            return str(uuid.uuid1())
        
        elif self.uuid_format == UUIDFormat.UUID3:
            if not self._namespace_uuid:
                # Fallback to UUID4 if no namespace
                return str(uuid.uuid4())
            name = self._create_name_from_log(log_entry)
            return str(uuid.uuid3(self._namespace_uuid, name))
        
        elif self.uuid_format == UUIDFormat.UUID5:
            if not self._namespace_uuid:
                # Fallback to UUID4 if no namespace
                return str(uuid.uuid4())
            name = self._create_name_from_log(log_entry)
            return str(uuid.uuid5(self._namespace_uuid, name))
        
        elif self.uuid_format == UUIDFormat.CUSTOM:
            return self._generate_custom_id(log_entry)
        
        else:
            # Default fallback
            return str(uuid.uuid4())
    
    def _create_name_from_log(self, log_entry: Optional[Dict[str, Any]]) -> str:
        """
        Create a name string from log entry for name-based UUIDs.
        
        Args:
            log_entry: Log entry data
            
        Returns:
            Name string for UUID generation
        """
        if not log_entry:
            return f"log-{time.time()}-{uuid.uuid4().hex[:8]}"
        
        # Create a deterministic name from key log fields
        components = [
            str(log_entry.get('timestamp', '')),
            str(log_entry.get('source', '')),
            str(log_entry.get('host', '')),
            str(log_entry.get('message', ''))[:100],  # Limit message length
        ]
        
        return '-'.join(filter(None, components))
    
    def _generate_custom_id(self, log_entry: Optional[Dict[str, Any]]) -> str:
        """
        Generate a custom format ID with timestamp and random components.
        
        This creates IDs in the format: timestamp_random_hash
        Similar to some Elasticsearch configurations.
        
        Args:
            log_entry: Log entry data
            
        Returns:
            Custom format ID string
        """
        # Get current timestamp in milliseconds
        timestamp_ms = int(time.time() * 1000)
        
        # Generate random component
        random_component = uuid.uuid4().hex[:8]
        
        # Create hash component from log data if available
        if log_entry:
            log_str = str(log_entry.get('message', '')) + str(log_entry.get('source', ''))
            hash_component = hashlib.md5(log_str.encode()).hexdigest()[:8]
        else:
            hash_component = uuid.uuid4().hex[:8]
        
        return f"{timestamp_ms}_{random_component}_{hash_component}"
    
    @staticmethod
    def is_valid_uuid(uuid_string: str) -> bool:
        """
        Validate if a string is a valid UUID.
        
        Args:
            uuid_string: String to validate
            
        Returns:
            True if valid UUID, False otherwise
        """
        try:
            uuid.UUID(uuid_string)
            return True
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def create_generator_from_config(config: Dict[str, Any]) -> 'UUIDGenerator':
        """
        Create a UUID generator from configuration.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Configured UUIDGenerator instance
        """
        uuid_format_str = config.get('format', 'uuid4').lower()
        namespace = config.get('namespace')
        
        # Map string to enum
        format_mapping = {
            'uuid4': UUIDFormat.UUID4,
            'uuid1': UUIDFormat.UUID1,
            'uuid3': UUIDFormat.UUID3,
            'uuid5': UUIDFormat.UUID5,
            'custom': UUIDFormat.CUSTOM
        }
        
        uuid_format = format_mapping.get(uuid_format_str, UUIDFormat.UUID4)
        
        return UUIDGenerator(uuid_format=uuid_format, namespace=namespace)


# Default generator instance for convenience
default_generator = UUIDGenerator()


def generate_log_id(log_entry: Optional[Dict[str, Any]] = None) -> str:
    """
    Convenience function to generate a log ID using the default generator.
    
    Args:
        log_entry: Optional log entry data
        
    Returns:
        Unique identifier string
    """
    return default_generator.generate_log_id(log_entry)
