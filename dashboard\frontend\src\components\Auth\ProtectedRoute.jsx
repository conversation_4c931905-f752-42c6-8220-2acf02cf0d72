import React from 'react'
import { useSelector } from 'react-redux'
import { Navigate, useLocation } from 'react-router-dom'
import { Box, Typography, Alert } from '@mui/material'
import LoadingSpinner from '../Common/LoadingSpinner'
import { getUserPermissions, canAccessRoute } from '../../utils/permissions'

const ProtectedRoute = ({ children, requiresPermission = null }) => {
  const { isAuthenticated, isLoading, user } = useSelector((state) => state.auth)
  const { roles } = useSelector((state) => state.users)
  const location = useLocation()

  if (isLoading) {
    return <LoadingSpinner message="Checking authentication..." />
  }

  if (!isAuthenticated) {
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Check route-specific permissions
  if (requiresPermission || location.pathname !== '/') {
    const userPermissions = getUserPermissions(user, roles)
    const hasAccess = canAccessRoute(userPermissions, location.pathname)

    if (!hasAccess) {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            <Typography variant="h6" gutterBottom>
              Access Denied
            </Typography>
            <Typography variant="body1">
              You don't have permission to access this section. Please contact your administrator if you believe this is an error.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Required permission: {requiresPermission || 'Navigation access'}
            </Typography>
          </Alert>
        </Box>
      )
    }
  }

  return children
}

export default ProtectedRoute
