# Linux Log Agent Systemd Service Fixes

This document outlines the fixes applied to resolve the systemd service failure issues.

## Issues Identified

1. **Path Mismatches**: Service configuration referenced incorrect paths
2. **Insufficient Error Handling**: Limited error reporting in service wrapper
3. **Security Restrictions**: Systemd security settings prevented log file access
4. **Missing Debugging Tools**: No diagnostic capabilities for troubleshooting

## Fixes Applied

### 1. Path Corrections

**Files Modified:**
- `linux-agent/etc/systemd/system/linux-agent.service`
- `linux-agent/install/install.sh`

**Changes:**
- Updated service file to use `/opt/linux-agent` instead of `/opt/linux-log-agent`
- Updated config path to `/opt/linux-agent/config/default_config.yaml`
- Updated service name to `linux-agent`

### 2. Enhanced Error Handling

**Files Modified:**
- `linux-agent/service/systemd_service.py`

**Changes:**
- Added comprehensive error handling in service initialization
- Added environment validation before starting
- Enhanced logging with debug information
- Added try-catch blocks around agent initialization and startup
- Added pre-flight checks for permissions and file access

### 3. Security Configuration Updates

**Files Modified:**
- `linux-agent/etc/systemd/system/linux-agent.service`

**Changes:**
- Changed `ProtectSystem=strict` to `ProtectSystem=read-only`
- Added `ReadOnlyPaths` for system logs and agent directory
- Added `PrivateDevices=false` to allow log access
- Maintained security while enabling necessary file access

### 4. Debugging Tools

**Files Created:**
- `linux-agent/scripts/debug_service.py` - Comprehensive debug script
- `linux-agent/scripts/systemd_debug.sh` - System diagnostic script
- `linux-agent/scripts/test_service.sh` - Service validation script

## Installation Instructions

1. **Update the existing installation:**
   ```bash
   sudo systemctl stop linux-agent
   sudo systemctl disable linux-agent
   
   # Copy updated files to /opt/linux-agent
   sudo cp linux-agent/etc/systemd/system/linux-agent.service /etc/systemd/system/
   sudo cp -r linux-agent/service/* /opt/linux-agent/service/
   sudo cp -r linux-agent/scripts/* /opt/linux-agent/scripts/
   
   # Set permissions
   sudo chmod +x /opt/linux-agent/scripts/*.py
   sudo chmod +x /opt/linux-agent/scripts/*.sh
   sudo chown -R linux-log-agent:linux-log-agent /opt/linux-agent
   
   # Reload systemd
   sudo systemctl daemon-reload
   sudo systemctl enable linux-agent
   ```

2. **Test the service:**
   ```bash
   # Run diagnostic script
   sudo /opt/linux-agent/scripts/systemd_debug.sh
   
   # Run debug test
   sudo -u linux-log-agent python3 /opt/linux-agent/scripts/debug_service.py
   
   # Test service functionality
   sudo /opt/linux-agent/scripts/test_service.sh
   ```

3. **Start the service:**
   ```bash
   sudo systemctl start linux-agent
   sudo systemctl status linux-agent
   ```

## Troubleshooting

### If the service still fails:

1. **Check environment:**
   ```bash
   sudo /opt/linux-agent/scripts/systemd_debug.sh
   ```

2. **Run debug script:**
   ```bash
   sudo -u linux-log-agent python3 /opt/linux-agent/scripts/debug_service.py
   ```

3. **Check detailed logs:**
   ```bash
   journalctl -u linux-agent -f
   tail -f /var/log/linux-log-agent/service.log
   tail -f /var/log/linux-log-agent/service_errors.log
   ```

4. **Manual test:**
   ```bash
   sudo -u linux-log-agent /usr/bin/python3 /opt/linux-agent/service/systemd_service.py --config /opt/linux-agent/config/default_config.yaml
   ```

### Common Issues and Solutions:

1. **Permission Denied Errors:**
   - Ensure linux-log-agent user is in adm, systemd-journal, and syslog groups
   - Check file permissions on config and log directories

2. **Config File Not Found:**
   - Verify config file exists at `/opt/linux-agent/config/default_config.yaml`
   - Check file permissions and ownership

3. **Import Errors:**
   - Ensure Python dependencies are installed
   - Check Python path in systemd service

4. **Log Access Issues:**
   - Verify user has access to system log files
   - Check systemd security restrictions

## Key Changes Summary

- **Service Name**: `linux-log-agent` → `linux-agent`
- **Install Path**: `/opt/linux-log-agent` → `/opt/linux-agent`
- **Config Path**: `/etc/linux-log-agent/config.yaml` → `/opt/linux-agent/config/default_config.yaml`
- **Enhanced Error Handling**: Comprehensive logging and validation
- **Security**: Balanced security with functionality
- **Debugging**: Multiple diagnostic tools available

The service should now start successfully and remain stable under systemd management.
