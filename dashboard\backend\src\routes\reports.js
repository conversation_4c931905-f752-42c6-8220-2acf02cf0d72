const express = require('express');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize } = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');
const Report = require('../models/Report');
const Schedule = require('../models/Schedule');
const analyticsService = require('../services/analyticsService');
const reportService = require('../services/reportService');
const reportTemplateService = require('../services/reportTemplateService');
const logger = require('../config/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/reports
 * @desc    Get all reports for the user
 * @access  Private
 */
router.get('/', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { page = 1, limit = 20, type, folder, search, status } = req.query;
  const userId = req.user.id;

  // Build query - show user's own reports (any status) and public published reports
  const query = {
    $or: [
      { owner: userId }, // User's own reports (any status)
      { 'sharing.isPublic': true, status: 'published' }, // Public published reports
      { 'sharing.sharedWith.user': userId }, // Shared reports
    ],
  };

  if (type) query.type = type;
  if (folder) query.folder = folder;
  // Only apply status filter if explicitly provided
  if (status) {
    // If status filter is provided, apply it to all reports
    query.status = status;
  }
  if (search) {
    query.$text = { $search: search };
  }

  const reports = await Report.find(query)
    .populate('owner', 'username firstName lastName')
    .sort({ updatedAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .lean();

  const total = await Report.countDocuments(query);

  res.json({
    status: 'success',
    data: {
      reports,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    },
  });
}));

/**
 * @route   GET /api/v1/reports/templates
 * @desc    Get available report templates
 * @access  Private
 */
router.get('/templates', authorize(['view_reports']), catchAsync(async (req, res) => {
  const { category } = req.query;

  try {
    const templates = await reportTemplateService.getTemplatesByCategory(category);

    res.json({
      status: 'success',
      data: { templates }
    });
  } catch (error) {
    logger.error('Failed to get templates:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to load templates',
      data: { templates: [] }
    });
  }
}));

/**
 * @route   POST /api/v1/reports/templates/initialize
 * @desc    Initialize default report templates
 * @access  Private (Admin only)
 */
router.post('/templates/initialize', authorize(['manage_system']), catchAsync(async (req, res) => {
  await reportTemplateService.initializeDefaultTemplates(req.user.id);

  res.json({
    status: 'success',
    message: 'Default templates initialized successfully'
  });
}));

/**
 * @route   GET /api/v1/reports/:id
 * @desc    Get specific report
 * @access  Private
 */
router.get('/:id', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id)
    .populate('owner', 'username firstName lastName')
    .populate('sharing.sharedWith.user', 'username firstName lastName');

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  // Check access permissions
  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  res.json({
    status: 'success',
    data: { report },
  });
}));

/**
 * @route   POST /api/v1/reports
 * @desc    Create new report
 * @access  Private
 */
router.post('/', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const { name, description, type, content, tags, folder, sharing } = req.body;

  const report = new Report({
    reportId: uuidv4(),
    name,
    description,
    type,
    owner: req.user.id,
    content: content || {},
    tags: tags || [],
    folder: folder || 'My Reports',
    sharing: sharing || { isPublic: false, sharedWith: [] },
  });

  await report.save();

  res.status(201).json({
    status: 'success',
    data: { report },
  });
}));

/**
 * @route   PUT /api/v1/reports/:id
 * @desc    Update existing report
 * @access  Private
 */
router.put('/:id', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  // Check edit permissions
  if (!report.canEdit(req.user.id)) {
    throw new AppError('Edit access denied', 403);
  }

  const { name, description, content, tags, folder, sharing, status } = req.body;

  if (name) report.name = name;
  if (description !== undefined) report.description = description;
  if (content) report.content = content;
  if (tags) report.tags = tags;
  if (folder) report.folder = folder;
  if (sharing) report.sharing = sharing;
  if (status) report.status = status;

  await report.save();

  res.json({
    status: 'success',
    data: { report },
  });
}));

/**
 * @route   DELETE /api/v1/reports/:id
 * @desc    Delete report
 * @access  Private
 */
router.delete('/:id', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  // Only owner can delete
  if (report.owner.toString() !== req.user.id) {
    throw new AppError('Only the owner can delete this report', 403);
  }

  await Report.findByIdAndDelete(req.params.id);

  res.json({
    status: 'success',
    message: 'Report deleted successfully',
  });
}));

/**
 * @route   POST /api/v1/reports/:id/execute
 * @desc    Execute report and return data
 * @access  Private
 */
router.post('/:id/execute', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  const { parameters = {} } = req.body;
  const startTime = Date.now();

  try {
    let data;

    // Execute based on report type using reportService
    switch (report.type) {
      case 'analytics':
        data = await reportService.executeAnalyticsReport(report, parameters);
        break;
      case 'compliance':
        data = await reportService.executeComplianceReport(report, parameters);
        break;
      case 'custom':
        data = await reportService.executeCustomReport(report, parameters);
        break;
      default:
        throw new AppError('Unknown report type', 400);
    }

    const executionTime = Date.now() - startTime;

    // Update report metadata
    report.lastRunAt = new Date();
    report.metadata.executionCount += 1;
    report.metadata.avgExecutionTime =
      (report.metadata.avgExecutionTime * (report.metadata.executionCount - 1) + executionTime) /
      report.metadata.executionCount;

    await report.save();

    res.json({
      status: 'success',
      data: {
        reportData: data,
        executionTime,
        generatedAt: new Date(),
      },
    });
  } catch (error) {
    report.metadata.lastError = error.message;
    await report.save();
    throw error;
  }
}));

/**
 * @route   POST /api/v1/reports/:id/export/pdf
 * @desc    Export report as PDF
 * @access  Private
 */
router.post('/:id/export/pdf', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  const { parameters = {} } = req.body;

  try {
    // Execute report to get data
    let reportData;
    switch (report.type) {
      case 'analytics':
        reportData = await reportService.executeAnalyticsReport(report, parameters);
        break;
      case 'compliance':
        reportData = await reportService.executeComplianceReport(report, parameters);
        break;
      case 'custom':
        reportData = await reportService.executeCustomReport(report, parameters);
        break;
      default:
        throw new AppError('Unknown report type', 400);
    }

    // Generate PDF
    const pdfBuffer = await reportService.generatePDF(reportData, {
      title: report.name,
      format: parameters.format || 'A4'
    });

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${report.name.replace(/[^a-zA-Z0-9]/g, '_')}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    res.send(pdfBuffer);
  } catch (error) {
    logger.error('PDF export failed:', error);
    throw new AppError('Failed to generate PDF', 500);
  }
}));

/**
 * @route   POST /api/v1/reports/:id/export/csv
 * @desc    Export report as CSV
 * @access  Private
 */
router.post('/:id/export/csv', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  const { parameters = {} } = req.body;

  try {
    // Execute report to get data
    let reportData;
    switch (report.type) {
      case 'analytics':
        reportData = await reportService.executeAnalyticsReport(report, parameters);
        break;
      case 'compliance':
        reportData = await reportService.executeComplianceReport(report, parameters);
        break;
      case 'custom':
        reportData = await reportService.executeCustomReport(report, parameters);
        break;
      default:
        throw new AppError('Unknown report type', 400);
    }

    // Generate CSV
    const csvData = await reportService.generateCSV(reportData);

    // Set response headers
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${report.name.replace(/[^a-zA-Z0-9]/g, '_')}.csv"`);

    res.send(csvData);
  } catch (error) {
    logger.error('CSV export failed:', error);
    throw new AppError('Failed to generate CSV', 500);
  }
}));

/**
 * @route   POST /api/v1/reports/:id/preview
 * @desc    Generate report preview (HTML)
 * @access  Private
 */
router.post('/:id/preview', authorize(['view_reports']), catchAsync(async (req, res) => {
  const report = await Report.findById(req.params.id);

  if (!report) {
    throw new AppError('Report not found', 404);
  }

  if (!report.canAccess(req.user.id)) {
    throw new AppError('Access denied', 403);
  }

  const { parameters = {} } = req.body;

  try {
    // Execute report to get data
    let reportData;
    switch (report.type) {
      case 'analytics':
        reportData = await reportService.executeAnalyticsReport(report, parameters);
        break;
      case 'compliance':
        reportData = await reportService.executeComplianceReport(report, parameters);
        break;
      case 'custom':
        reportData = await reportService.executeCustomReport(report, parameters);
        break;
      default:
        throw new AppError('Unknown report type', 400);
    }

    // Generate HTML preview
    const htmlContent = reportService.generateHTMLReport(reportData, {
      title: report.name,
      includeCharts: true
    });

    res.json({
      status: 'success',
      data: {
        html: htmlContent,
        reportData,
        generatedAt: new Date()
      }
    });
  } catch (error) {
    logger.error('Report preview failed:', error);
    throw new AppError('Failed to generate preview', 500);
  }
}));



/**
 * @route   POST /api/v1/reports/:id/create-template
 * @desc    Create template from existing report
 * @access  Private
 */
router.post('/:id/create-template', authorize(['generate_reports']), catchAsync(async (req, res) => {
  const { name, description, folder, sharing } = req.body;

  const template = await reportTemplateService.createTemplateFromReport(
    req.params.id,
    req.user.id,
    { name, description, folder, sharing }
  );

  res.status(201).json({
    status: 'success',
    data: { template }
  });
}));

module.exports = router;
