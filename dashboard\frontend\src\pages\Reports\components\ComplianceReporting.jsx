import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material'
import {
  CheckCircle,
  Error,
  Warning,
  Schedule,
  Assessment,
  Download,
  Visibility,
  ExpandMore,
  Refresh,
  Security,
  Shield,
} from '@mui/icons-material'
import { useTheme } from '@mui/material/styles'
import {
  fetchComplianceAnalytics,
  exportReportPDF,
  exportReportCSV
} from '../../../store/slices/reportingSlice'
// import ChartComponents from './ChartComponents'

const ComplianceReporting = () => {
  const theme = useTheme()
  const dispatch = useDispatch()
  const { complianceAnalytics, loading, error } = useSelector(state => state.reporting)
  const [selectedFramework, setSelectedFramework] = useState('')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    loadComplianceData()
  }, [dispatch, selectedFramework])

  const loadComplianceData = async () => {
    setRefreshing(true)
    try {
      await dispatch(fetchComplianceAnalytics(selectedFramework || null))
    } finally {
      setRefreshing(false)
    }
  }

  const handleFrameworkChange = (event) => {
    setSelectedFramework(event.target.value)
  }

  const handleRefresh = () => {
    loadComplianceData()
  }

  const handleExportPDF = async () => {
    try {
      await dispatch(exportReportPDF({
        reportId: 'compliance-temp',
        parameters: { framework: selectedFramework }
      }))
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const handleExportCompliance = async (framework) => {
    try {
      // Create a temporary compliance report
      const reportData = {
        name: `${framework.name} Compliance Report`,
        type: 'compliance',
        content: {
          framework: framework.name.toLowerCase(),
          analyticsType: 'compliance'
        }
      }

      // Create and execute the report
      const response = await fetch('/api/v1/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(reportData)
      })

      if (response.ok) {
        const { data } = await response.json()
        const reportId = data.report._id

        // Export as PDF
        const exportResponse = await fetch(`/api/v1/reports/${reportId}/export/pdf`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ parameters: { framework: framework.name.toLowerCase() } })
        })

        if (exportResponse.ok) {
          const blob = await exportResponse.blob()
          const url = window.URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `${framework.name}_Compliance_Report.pdf`
          document.body.appendChild(a)
          a.click()
          window.URL.revokeObjectURL(url)
          document.body.removeChild(a)
        }
      }
    } catch (error) {
      console.error('Failed to export compliance report:', error)
    }
  }

  const getComplianceColor = (score) => {
    if (score >= 90) return theme.palette.success.main
    if (score >= 75) return theme.palette.warning.main
    return theme.palette.error.main
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'passed':
        return <CheckCircle color="success" />
      case 'failed':
        return <Error color="error" />
      case 'warning':
        return <Warning color="warning" />
      default:
        return <Schedule color="info" />
    }
  }

  const renderFrameworkOverview = () => {
    if (loading.complianceAnalytics) {
      return (
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          </CardContent>
        </Card>
      )
    }

    if (error.complianceAnalytics) {
      return (
        <Card>
          <CardContent>
            <Alert severity="error">{error.complianceAnalytics}</Alert>
          </CardContent>
        </Card>
      )
    }

    if (!complianceAnalytics) return null

    return (
      <Grid container spacing={3}>
        {complianceAnalytics.frameworks?.map((framework, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">{framework.name}</Typography>
                  <Chip
                    label={`v${framework.version}`}
                    size="small"
                    variant="outlined"
                  />
                </Box>
                
                <Box sx={{ textAlign: 'center', mb: 2 }}>
                  <Typography
                    variant="h3"
                    sx={{ color: getComplianceColor(framework.complianceScore) }}
                  >
                    {framework.complianceScore}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Compliance Score
                  </Typography>
                </Box>
                
                <LinearProgress
                  variant="determinate"
                  value={framework.complianceScore}
                  sx={{
                    mb: 2,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: theme.palette.grey[200],
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getComplianceColor(framework.complianceScore),
                    },
                  }}
                />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Controls Passed</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {framework.controlsPassed}/{framework.controlsTotal}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Last Assessment</Typography>
                  <Typography variant="body2">
                    {new Date(framework.lastAssessment).toLocaleDateString()}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    startIcon={<Visibility />}
                    onClick={() => setSelectedFramework(framework.name.toLowerCase())}
                  >
                    View Details
                  </Button>
                  <Button
                    size="small"
                    startIcon={<Download />}
                    variant="outlined"
                    onClick={() => handleExportCompliance(framework)}
                  >
                    Export
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    )
  }

  const renderAuditReadiness = () => {
    if (!complianceAnalytics?.auditReadiness) return null

    const { auditReadiness } = complianceAnalytics

    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Audit Readiness
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {auditReadiness.daysUntilNextAudit}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Days Until Next Audit
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info">
                  {auditReadiness.evidenceCollectionProgress}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Evidence Collection Progress
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={auditReadiness.evidenceCollectionProgress}
                  sx={{ mt: 1, height: 6, borderRadius: 3 }}
                />
              </Box>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning">
                  {auditReadiness.outstandingItems}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Outstanding Items
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  const renderComplianceDetails = () => {
    if (!selectedFramework || !complianceAnalytics) return null

    const frameworkData = complianceAnalytics.results || {}
    const controls = Object.entries(frameworkData).map(([ruleId, result]) => ({
      id: ruleId,
      name: result.ruleName || ruleId,
      status: result.status,
      lastTested: new Date(result.lastChecked),
      evidence: result.details,
      score: result.score,
      category: result.ruleCategory
    }))

    const renderComplianceMetrics = () => {
      if (!complianceAnalytics.metrics) return null

      const metrics = complianceAnalytics.metrics
      const metricsData = [
        { name: 'Authentication Events', value: metrics.authenticationEvents },
        { name: 'Access Control Events', value: metrics.accessControlEvents },
        { name: 'Data Protection Events', value: metrics.dataProtectionEvents },
        { name: 'Audit Events', value: metrics.auditEvents },
        { name: 'Configuration Changes', value: metrics.configurationChanges },
        { name: 'Failed Logins', value: metrics.failedLogins },
        { name: 'Privilege Escalations', value: metrics.privilegeEscalations }
      ]

      return (
        <Grid item xs={12} md={6}>
          <ChartComponents.BarChart
            data={metricsData}
            title="Compliance Metrics"
            height={300}
            config={{
              bars: [
                { key: 'value', color: theme.palette.primary.main, name: 'Events' }
              ]
            }}
          />
        </Grid>
      )
    }

    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  {selectedFramework.toUpperCase()} Compliance Details
                </Typography>
                <Box>
                  <Button
                    startIcon={<Refresh />}
                    onClick={handleRefresh}
                    disabled={refreshing}
                    sx={{ mr: 1 }}
                  >
                    Refresh
                  </Button>
                  <Button
                    startIcon={<Download />}
                    onClick={handleExportPDF}
                    variant="outlined"
                    sx={{ mr: 1 }}
                  >
                    Export PDF
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setSelectedFramework('')}
                  >
                    Back to Overview
                  </Button>
                </Box>
              </Box>

              {/* Compliance Score Summary */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.light', borderRadius: 2 }}>
                    <Typography variant="h4" color="primary.contrastText" gutterBottom>
                      {complianceAnalytics.complianceScore}%
                    </Typography>
                    <Typography variant="body2" color="primary.contrastText">
                      Compliance Score
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.light', borderRadius: 2 }}>
                    <Typography variant="h4" color="success.contrastText" gutterBottom>
                      {complianceAnalytics.passedRules}
                    </Typography>
                    <Typography variant="body2" color="success.contrastText">
                      Passed Rules
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'error.light', borderRadius: 2 }}>
                    <Typography variant="h4" color="error.contrastText" gutterBottom>
                      {complianceAnalytics.failedRules}
                    </Typography>
                    <Typography variant="body2" color="error.contrastText">
                      Failed Rules
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'info.light', borderRadius: 2 }}>
                    <Typography variant="h4" color="info.contrastText" gutterBottom>
                      {complianceAnalytics.totalRules}
                    </Typography>
                    <Typography variant="body2" color="info.contrastText">
                      Total Rules
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Rule ID</TableCell>
                      <TableCell>Rule Name</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Score</TableCell>
                      <TableCell>Last Checked</TableCell>
                      <TableCell>Details</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {controls.map((control) => (
                      <TableRow key={control.id}>
                        <TableCell>{control.id}</TableCell>
                        <TableCell>{control.name}</TableCell>
                        <TableCell>{control.category}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getStatusIcon(control.status === 'pass' ? 'passed' : 'failed')}
                            <Chip
                              label={control.status}
                              size="small"
                              color={control.status === 'pass' ? 'success' : 'error'}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>{control.score}%</TableCell>
                        <TableCell>
                          {control.lastTested.toLocaleDateString()}
                        </TableCell>
                        <TableCell>{control.evidence}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {renderComplianceMetrics()}

        {complianceAnalytics.recommendations && complianceAnalytics.recommendations.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recommendations
                </Typography>
                <List>
                  {complianceAnalytics.recommendations.map((rec, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Warning color="warning" />
                      </ListItemIcon>
                      <ListItemText
                        primary={rec.recommendation}
                        secondary={`Priority: ${rec.priority} - ${rec.impact}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    )
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Compliance Reporting
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Monitor compliance status across multiple regulatory frameworks and generate audit-ready reports.
        </Typography>
        
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Select Framework</InputLabel>
          <Select
            value={selectedFramework}
            label="Select Framework"
            onChange={handleFrameworkChange}
          >
            <MenuItem value="">All Frameworks</MenuItem>
            <MenuItem value="pci">PCI DSS</MenuItem>
            <MenuItem value="hipaa">HIPAA</MenuItem>
            <MenuItem value="soc2">SOC2</MenuItem>
            <MenuItem value="gdpr">GDPR</MenuItem>
            <MenuItem value="iso27001">ISO 27001</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Grid container spacing={3}>
        {!selectedFramework && (
          <>
            <Grid item xs={12}>
              {renderFrameworkOverview()}
            </Grid>
            
            <Grid item xs={12}>
              {renderAuditReadiness()}
            </Grid>
          </>
        )}
        
        {selectedFramework && (
          <Grid item xs={12}>
            {renderComplianceDetails()}
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default ComplianceReporting
