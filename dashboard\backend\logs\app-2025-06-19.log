{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-19 14:45:57:4557"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-19 14:46:02:462"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m<PERSON><PERSON><PERSON> connected successfully\u001b[39m","timestamp":"2025-06-19 14:46:02:462"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 14:46:09:469"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"mongodb:27017":{"$clusterTime":null,"address":"mongodb:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":701757,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to MongoDB: getaddrinfo EAI_AGAIN mongodb\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"mongodb:27017":{"$clusterTime":null,"address":"mongodb:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":701757,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"stack":"MongooseServerSelectionError: getaddrinfo EAI_AGAIN mongodb\n    at _handleConnectionErrors (/app/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/app/node_modules/mongoose/lib/connection.js:1096:11)\n    at async DatabaseManager.connectMongoDB (/app/src/config/database.js:19:7)\n    at async Promise.allSettled (index 0)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 14:46:32:4632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m1 databases connected successfully\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"0":"getaddrinfo EAI_AGAIN mongodb","1":"connect ECONNREFUSED **********:5432","2":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m3 databases failed to connect:\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-19 14:46:32:4632"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:48:35:4835","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:48:54:4854","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:50:27:5027","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:51:20:5120","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:52:23:5223","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:56:14:5614","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-19 14:57:30:5730"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-19 14:57:31:5731"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-19 14:57:31:5731"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-19 14:59:12:5912"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-19 14:59:16:5916"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-19 14:59:16:5916"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-19 14:59:17:5917"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m2 databases connected successfully\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"0":"connect ECONNREFUSED **********:5432","1":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m2 databases failed to connect:\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-19 14:59:23:5923"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-19 15:11:29:1129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-19 15:11:30:1130"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-19 15:11:30:1130"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-19 15:11:30:1130"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-19 15:11:59:1159"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-19 15:12:03:123"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mRedis connected successfully\u001b[39m","timestamp":"2025-06-19 15:12:03:123"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-19 15:12:05:125"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m2 databases connected successfully\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"0":"connect ECONNREFUSED **********:5432","1":"connect ECONNREFUSED **********:9200","level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m2 databases failed to connect:\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-19 15:12:11:1211"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-19 15:19:31:1931"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-19 15:19:31:1931"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mHTTP server closed\u001b[39m","timestamp":"2025-06-19 15:19:31:1931"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33mMongoDB disconnected\u001b[39m","timestamp":"2025-06-19 15:19:31:1931"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections closed\u001b[39m","timestamp":"2025-06-19 15:19:31:1931"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-19 15:24:20:2420"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing alert system...\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing Correlation Engine...\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 0 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation Engine initialized with 0 rules\u001b[39m","timestamp":"2025-06-19 15:24:24:2424"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCreated system user for default rules\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing default alert rules...\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDefault rules initialization complete. Created: 8, Skipped: 0\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDefault alert rules initialized\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert system initialization completed\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-19 15:24:25:2425"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully: <EMAIL> from IP: **********\u001b[39m","timestamp":"2025-06-19 15:24:46:2446"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert rules changed, reloading...\u001b[39m","timestamp":"2025-06-19 15:24:54:2454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 8 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-19 15:24:54:2454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew API key created: Jarel\u001b[39m","timestamp":"2025-06-19 16:25:24:2524","userId":"68542ba6a893eae02269e328"}
