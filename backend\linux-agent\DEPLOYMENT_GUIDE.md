# Linux Log Collection Agent - Deployment Guide

This guide provides detailed instructions for deploying the Linux Log Collection Agent across different Linux distributions.

## Prerequisites

### System Requirements
- Linux distribution (Ubuntu 16.04+, CentOS 7+, Debian 9+, RHEL 7+, Fedora 28+, SUSE 12+)
- Python 3.7 or higher
- systemd (for service installation)
- Root or sudo access
- Network connectivity to ExLog dashboard

### Required Permissions
The agent needs read access to system log files. This typically requires:
- Running as root, or
- Adding the agent user to appropriate groups: `adm`, `systemd-journal`, `syslog`

## Quick Installation

### 1. Download and Extract
```bash
# Download the linux-agent directory to your target system
# Extract to a temporary location
cd /tmp
# Assuming you have the linux-agent directory here
```

### 2. Run Installation Script
```bash
cd linux-agent
sudo ./install/install.sh
```

The installation script will:
- Detect your Linux distribution
- Install required dependencies
- Create a dedicated user and group
- Install the agent files
- Configure systemd service
- Set up log rotation

### 3. Configure the Agent
```bash
sudo nano /etc/linux-log-agent/config.yaml
```

Update the ExLog API configuration:
```yaml
exlog_api:
  enabled: true
  endpoint: "http://your-exlog-server:5000/api/v1/logs"
  api_key: "your-api-key-here"
```

### 4. Start the Service
```bash
sudo systemctl start linux-log-agent
sudo systemctl status linux-log-agent
```

## Manual Installation

### 1. Install Dependencies

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install python3 python3-pip python3-venv systemd
```

#### CentOS/RHEL/Fedora
```bash
# CentOS/RHEL 8+, Fedora
sudo dnf install python3 python3-pip systemd

# CentOS/RHEL 7
sudo yum install python3 python3-pip systemd
```

#### SUSE/openSUSE
```bash
sudo zypper install python3 python3-pip systemd
```

### 2. Create User and Directories
```bash
# Create user and group
sudo groupadd --system linux-log-agent
sudo useradd --system --gid linux-log-agent --home-dir /opt/linux-log-agent \
             --shell /bin/false --comment "Linux Log Agent" linux-log-agent

# Add to required groups
sudo usermod -a -G adm linux-log-agent
sudo usermod -a -G systemd-journal linux-log-agent
sudo usermod -a -G syslog linux-log-agent

# Create directories
sudo mkdir -p /opt/linux-log-agent
sudo mkdir -p /etc/linux-log-agent
sudo mkdir -p /var/log/linux-log-agent

# Set ownership
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent
sudo chown -R linux-log-agent:linux-log-agent /etc/linux-log-agent
sudo chown -R linux-log-agent:linux-log-agent /var/log/linux-log-agent
```

### 3. Install Agent Files
```bash
# Copy agent files
sudo cp -r linux-agent/* /opt/linux-log-agent/
sudo chown -R linux-log-agent:linux-log-agent /opt/linux-log-agent

# Install Python dependencies
sudo -u linux-log-agent python3 -m pip install --user -r /opt/linux-log-agent/requirements.txt
```

### 4. Install Configuration
```bash
sudo cp /opt/linux-log-agent/config/default_config.yaml /etc/linux-log-agent/config.yaml
sudo chown linux-log-agent:linux-log-agent /etc/linux-log-agent/config.yaml
sudo chmod 640 /etc/linux-log-agent/config.yaml
```

### 5. Install Systemd Service
```bash
sudo cp /opt/linux-log-agent/install/systemd/linux-log-agent.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable linux-log-agent
```

## Configuration

### Basic Configuration
Edit `/etc/linux-log-agent/config.yaml`:

```yaml
# ExLog API settings
exlog_api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: "your-api-key-here"
  batch_size: 100
  timeout: 30

# Log collection settings
collection:
  syslog:
    enabled: true
    paths:
      - /var/log/syslog
      - /var/log/messages
  
  auth_logs:
    enabled: true
    paths:
      - /var/log/auth.log
      - /var/log/secure
  
  kernel_logs:
    enabled: true
    paths:
      - /var/log/kern.log
  
  journalctl:
    enabled: true
    units: []  # Empty for all units
  
  application_logs:
    enabled: true
    paths:
      - /var/log/apache2/
      - /var/log/nginx/
```

### Distribution-Specific Settings

#### Ubuntu/Debian
- Syslog: `/var/log/syslog`
- Auth logs: `/var/log/auth.log`
- Kernel logs: `/var/log/kern.log`

#### CentOS/RHEL/Fedora
- Syslog: `/var/log/messages`
- Auth logs: `/var/log/secure`
- Kernel logs: Usually in `/var/log/messages`

#### SUSE/openSUSE
- Syslog: `/var/log/messages`
- Auth logs: `/var/log/secure` or `/var/log/auth.log`

## Service Management

### Start/Stop/Restart
```bash
sudo systemctl start linux-log-agent
sudo systemctl stop linux-log-agent
sudo systemctl restart linux-log-agent
```

### Enable/Disable Auto-start
```bash
sudo systemctl enable linux-log-agent
sudo systemctl disable linux-log-agent
```

### Check Status
```bash
sudo systemctl status linux-log-agent
```

### View Logs
```bash
# Service logs
sudo journalctl -u linux-log-agent -f

# Agent logs
sudo tail -f /var/log/linux-log-agent/agent.log

# Error logs
sudo tail -f /var/log/linux-log-agent/errors.log
```

## Testing

### Test Configuration
```bash
cd /opt/linux-log-agent
sudo -u linux-log-agent python3 main.py test
```

### Test API Connectivity
```bash
cd /opt/linux-log-agent
sudo -u linux-log-agent python3 test_api_client.py
```

### Run in Console Mode (for debugging)
```bash
cd /opt/linux-log-agent
sudo python3 main.py console
```

## Troubleshooting

### Common Issues

#### Permission Denied Errors
```bash
# Check if user is in required groups
groups linux-log-agent

# Add to missing groups
sudo usermod -a -G adm linux-log-agent
sudo usermod -a -G systemd-journal linux-log-agent
sudo usermod -a -G syslog linux-log-agent
```

#### Service Won't Start
```bash
# Check service status
sudo systemctl status linux-log-agent

# Check service logs
sudo journalctl -u linux-log-agent --no-pager

# Check configuration
sudo -u linux-log-agent python3 /opt/linux-log-agent/main.py test
```

#### No Logs Being Collected
1. Check log file permissions
2. Verify log file paths in configuration
3. Check collector status in agent logs
4. Ensure log files exist and are being written to

#### API Connection Issues
1. Verify ExLog dashboard is running
2. Check network connectivity
3. Verify API endpoint and key in configuration
4. Check firewall settings

### Log Locations
- Agent logs: `/var/log/linux-log-agent/agent.log`
- Error logs: `/var/log/linux-log-agent/errors.log`
- Service logs: `journalctl -u linux-log-agent`

## Uninstallation

### Automated Uninstall
```bash
sudo /opt/linux-log-agent/install/uninstall.sh
```

### Manual Uninstall
```bash
# Stop and disable service
sudo systemctl stop linux-log-agent
sudo systemctl disable linux-log-agent

# Remove service file
sudo rm /etc/systemd/system/linux-log-agent.service
sudo systemctl daemon-reload

# Remove directories
sudo rm -rf /opt/linux-log-agent
sudo rm -rf /etc/linux-log-agent
sudo rm -rf /var/log/linux-log-agent

# Remove user and group
sudo userdel linux-log-agent
sudo groupdel linux-log-agent

# Remove logrotate config
sudo rm -f /etc/logrotate.d/linux-log-agent
```

## Security Considerations

### File Permissions
- Configuration files: 640 (readable by agent user only)
- Log directories: 750 (writable by agent user only)
- Service runs as non-root user

### Network Security
- Use HTTPS for ExLog API endpoint when possible
- Secure API key storage
- Consider firewall rules for API communication

### Log Access
- Agent requires read access to system logs
- Minimal required group memberships
- No write access to system log files

## Performance Tuning

### Resource Limits
The systemd service includes resource limits:
- Memory: 256MB max
- CPU: 10% max
- File descriptors: 65536

### Configuration Tuning
```yaml
# Adjust batch size for network conditions
exlog_api:
  batch_size: 50  # Smaller for slow networks

# Adjust processing interval
general:
  processing_interval: 10  # Seconds between collection cycles

# Limit buffer size
general:
  buffer_size: 500  # Reduce memory usage
```

## Monitoring

### Health Checks
```bash
# Check if service is running
systemctl is-active linux-log-agent

# Check recent logs for errors
sudo journalctl -u linux-log-agent --since "1 hour ago" | grep ERROR

# Check agent statistics
sudo -u linux-log-agent python3 /opt/linux-log-agent/main.py test
```

### Metrics
The agent provides statistics on:
- Logs collected per collector
- Logs sent to API
- API success/failure rates
- Buffer sizes
- Error counts

## Support

For issues and support:
1. Check the troubleshooting section
2. Review agent and service logs
3. Test configuration and connectivity
4. Verify permissions and file access
