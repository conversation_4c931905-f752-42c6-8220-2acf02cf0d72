"""
Base Log Collector for Linux Log Collection Agent

This module provides the base class for all Linux log collectors.
"""

import logging
import os
import re
import socket
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Set
from pathlib import Path


class BaseLogCollector(ABC):
    """Base class for all Linux log collectors."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the base collector.
        
        Args:
            config: Collector configuration
            logger: Logger instance
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.hostname = socket.gethostname()
        
        # Track processed files and positions
        self._file_positions = {}
        self._processed_files = set()
        
        # Statistics
        self.stats = {
            'logs_collected': 0,
            'files_processed': 0,
            'errors': 0,
            'last_collection': None,
            'start_time': datetime.now()
        }
        
        self.logger.debug(f"{self.__class__.__name__} initialized")
    
    @abstractmethod
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from the source.
        
        Returns:
            List of collected log entries
        """
        pass
    
    def start(self) -> None:
        """Start the collector (override if needed)."""
        self.logger.debug(f"{self.__class__.__name__} started")
    
    def stop(self) -> None:
        """Stop the collector (override if needed)."""
        self.logger.debug(f"{self.__class__.__name__} stopped")
    
    def _get_log_paths(self) -> List[str]:
        """Get list of log file paths from configuration."""
        paths = self.config.get('paths', [])
        existing_paths = []
        
        for path in paths:
            if os.path.exists(path):
                if os.access(path, os.R_OK):
                    existing_paths.append(path)
                else:
                    self.logger.warning(f"No read access to log file: {path}")
            else:
                self.logger.debug(f"Log file does not exist: {path}")
        
        return existing_paths
    
    def _read_file_from_position(self, file_path: str) -> List[str]:
        """
        Read new lines from a file since last position.
        
        Args:
            file_path: Path to the log file
            
        Returns:
            List of new lines
        """
        try:
            # Get file stats
            stat = os.stat(file_path)
            current_size = stat.st_size
            current_mtime = stat.st_mtime
            
            # Check if file was rotated or truncated
            last_position = self._file_positions.get(file_path, {})
            last_size = last_position.get('size', 0)
            last_mtime = last_position.get('mtime', 0)
            last_pos = last_position.get('position', 0)
            
            # If file is smaller or has different mtime, it might have been rotated
            if current_size < last_size or current_mtime != last_mtime:
                self.logger.debug(f"File {file_path} appears to have been rotated or modified")
                last_pos = 0
            
            # Read new content
            new_lines = []
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(last_pos)
                new_lines = f.readlines()
                new_position = f.tell()
            
            # Update position tracking
            self._file_positions[file_path] = {
                'position': new_position,
                'size': current_size,
                'mtime': current_mtime
            }
            
            # Remove trailing newlines
            new_lines = [line.rstrip('\n\r') for line in new_lines if line.strip()]
            
            if new_lines:
                self.logger.debug(f"Read {len(new_lines)} new lines from {file_path}")
            
            return new_lines
            
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            self.stats['errors'] += 1
            return []
    
    def _parse_syslog_line(self, line: str, source: str = "System") -> Optional[Dict[str, Any]]:
        """
        Parse a syslog format line.
        
        Args:
            line: Log line to parse
            source: Log source name
            
        Returns:
            Parsed log entry or None if parsing failed
        """
        if not line.strip():
            return None
        
        try:
            # Try to parse standard syslog format
            # Example: Jan 15 10:30:45 hostname program[pid]: message
            syslog_pattern = r'^(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+([^:\[\]]+)(?:\[(\d+)\])?\s*:\s*(.*)$'
            match = re.match(syslog_pattern, line)
            
            if match:
                timestamp_str, hostname, program, pid, message = match.groups()
                
                # Parse timestamp (add current year)
                current_year = datetime.now().year
                try:
                    timestamp = datetime.strptime(f"{current_year} {timestamp_str}", "%Y %b %d %H:%M:%S")
                except ValueError:
                    timestamp = datetime.now()
                
                # Determine log level from message content
                log_level = self._determine_log_level(message)
                
                # Extract facility and priority if present
                facility, priority = self._extract_facility_priority(line)
                
                log_entry = {
                    'timestamp': timestamp.strftime('%Y-%m-%dT%H:%M:%S'),  # Match Windows format
                    'source': source,
                    'source_type': 'event',  # Use 'event' like Windows agent
                    'host': hostname or self.hostname,
                    'log_level': log_level,
                    'message': message.strip(),
                    'raw_data': line,
                    'additional_fields': {
                        'program': program,
                        'pid': int(pid) if pid else None,
                        'facility': facility,
                        'priority': priority,
                        'computer_name': hostname or self.hostname,
                        'record_number': int(time.time() * 1000) % 100000,  # Simulate record number
                        'event_id': 1000,
                        'event_category': 0,
                        'event_type': 4
                    }
                }
                
                return log_entry
            
            else:
                # Fallback: treat as generic log line
                return {
                    'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S'),  # Match Windows format
                    'source': source,
                    'source_type': 'event',  # Use 'event' like Windows agent
                    'host': self.hostname,
                    'log_level': 'info',
                    'message': line.strip(),
                    'raw_data': line,
                    'additional_fields': {
                        'computer_name': self.hostname,
                        'record_number': int(time.time() * 1000) % 100000,
                        'event_id': 1000,
                        'event_category': 0,
                        'event_type': 4
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Error parsing syslog line: {e}")
            return None
    
    def _determine_log_level(self, message: str) -> str:
        """
        Determine log level from message content.
        
        Args:
            message: Log message
            
        Returns:
            Log level string
        """
        message_lower = message.lower()
        
        # Critical/Emergency keywords
        if any(keyword in message_lower for keyword in ['panic', 'emergency', 'fatal', 'critical']):
            return 'critical'
        
        # Error keywords
        if any(keyword in message_lower for keyword in ['error', 'err', 'failed', 'failure', 'exception']):
            return 'error'
        
        # Warning keywords
        if any(keyword in message_lower for keyword in ['warning', 'warn', 'deprecated', 'invalid']):
            return 'warning'
        
        # Debug keywords
        if any(keyword in message_lower for keyword in ['debug', 'trace', 'verbose']):
            return 'debug'
        
        # Default to info
        return 'info'
    
    def _extract_facility_priority(self, line: str) -> tuple:
        """
        Extract syslog facility and priority from line.
        
        Args:
            line: Log line
            
        Returns:
            Tuple of (facility, priority)
        """
        # Look for priority value at start of line (e.g., <34>)
        priority_pattern = r'^<(\d+)>'
        match = re.match(priority_pattern, line)
        
        if match:
            priority_value = int(match.group(1))
            facility = priority_value >> 3  # Facility is upper 3 bits
            priority = priority_value & 0x07  # Priority is lower 3 bits
            
            # Map facility numbers to names
            facility_names = {
                0: 'kernel', 1: 'user', 2: 'mail', 3: 'daemon',
                4: 'auth', 5: 'syslog', 6: 'lpr', 7: 'news',
                8: 'uucp', 9: 'cron', 10: 'authpriv', 11: 'ftp',
                16: 'local0', 17: 'local1', 18: 'local2', 19: 'local3',
                20: 'local4', 21: 'local5', 22: 'local6', 23: 'local7'
            }
            
            facility_name = facility_names.get(facility, f'unknown({facility})')
            
            return facility_name, priority
        
        return None, None
    
    def _sanitize_message(self, message: str) -> str:
        """
        Sanitize log message by removing problematic characters.
        
        Args:
            message: Original message
            
        Returns:
            Sanitized message
        """
        if not isinstance(message, str):
            return str(message)
        
        # Remove or replace problematic Unicode characters
        message = message.replace('\u00ae', '(R)')  # Registered trademark
        message = message.replace('\u2122', '(TM)')  # Trademark
        
        # Remove other non-printable characters except common whitespace
        sanitized = ''.join(
            char for char in message 
            if char.isprintable() or char in ['\t', '\n', '\r']
        )
        
        # Limit message length
        if len(sanitized) > 1000:
            sanitized = sanitized[:997] + '...'
        
        return sanitized
    
    def _is_file_accessible(self, file_path: str) -> bool:
        """
        Check if a file is accessible for reading.
        
        Args:
            file_path: Path to check
            
        Returns:
            True if accessible, False otherwise
        """
        try:
            return os.path.exists(file_path) and os.access(file_path, os.R_OK)
        except Exception:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get collector statistics."""
        stats = self.stats.copy()
        stats['uptime_seconds'] = (datetime.now() - stats['start_time']).total_seconds()
        stats['files_tracked'] = len(self._file_positions)
        return stats
