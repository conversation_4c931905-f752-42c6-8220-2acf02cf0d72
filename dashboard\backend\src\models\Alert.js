const mongoose = require('mongoose');

const alertSchema = new mongoose.Schema({
  ruleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AlertRule',
    required: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
  },
  severity: {
    type: String,
    required: true,
    enum: ['critical', 'high', 'medium', 'low', 'informational'],
    index: true,
  },
  status: {
    type: String,
    required: true,
    enum: ['new', 'acknowledged', 'investigating', 'resolved', 'false_positive'],
    default: 'new',
    index: true,
  },
  triggeredAt: {
    type: Date,
    required: true,
    default: Date.now,
    index: true,
  },
  acknowledgedAt: {
    type: Date,
    default: null,
  },
  resolvedAt: {
    type: Date,
    default: null,
  },
  acknowledgedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  resolvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  triggerData: {
    type: mongoose.Schema.Types.Mixed,
    required: true,
  },
  relatedLogs: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Log',
  }],
  notes: [{
    content: {
      type: String,
      required: true,
      trim: true,
      maxlength: 2000,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
  tags: [{
    type: String,
    trim: true,
    maxlength: 50,
  }],
  priority: {
    type: Number,
    min: 1,
    max: 10,
    default: 5,
    index: true,
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  escalationLevel: {
    type: Number,
    min: 0,
    max: 5,
    default: 0,
  },
  lastEscalatedAt: {
    type: Date,
    default: null,
  },
  suppressedUntil: {
    type: Date,
    default: null,
  },
  metadata: {
    correlationId: String,
    sourceHost: String,
    sourceAgent: String,
    eventCount: {
      type: Number,
      default: 1,
    },
    firstOccurrence: {
      type: Date,
      default: Date.now,
    },
    lastOccurrence: {
      type: Date,
      default: Date.now,
    },
    additionalData: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for efficient querying
alertSchema.index({ status: 1, severity: 1, triggeredAt: -1 });
alertSchema.index({ ruleId: 1, triggeredAt: -1 });
alertSchema.index({ acknowledgedBy: 1, acknowledgedAt: -1 });
alertSchema.index({ resolvedBy: 1, resolvedAt: -1 });
alertSchema.index({ assignedTo: 1, status: 1 });
alertSchema.index({ 'metadata.sourceHost': 1, triggeredAt: -1 });
alertSchema.index({ tags: 1, triggeredAt: -1 });
alertSchema.index({ suppressedUntil: 1 });

// Virtual for alert age
alertSchema.virtual('ageInMinutes').get(function() {
  return Math.floor((Date.now() - this.triggeredAt.getTime()) / (1000 * 60));
});

// Virtual for resolution time
alertSchema.virtual('resolutionTimeMinutes').get(function() {
  if (this.resolvedAt && this.triggeredAt) {
    return Math.floor((this.resolvedAt.getTime() - this.triggeredAt.getTime()) / (1000 * 60));
  }
  return null;
});

// Virtual for acknowledgment time
alertSchema.virtual('acknowledgmentTimeMinutes').get(function() {
  if (this.acknowledgedAt && this.triggeredAt) {
    return Math.floor((this.acknowledgedAt.getTime() - this.triggeredAt.getTime()) / (1000 * 60));
  }
  return null;
});

// Virtual for severity priority mapping
alertSchema.virtual('severityPriority').get(function() {
  const severityMap = {
    'critical': 5,
    'high': 4,
    'medium': 3,
    'low': 2,
    'informational': 1,
  };
  return severityMap[this.severity] || 1;
});

// Virtual for status display
alertSchema.virtual('statusDisplay').get(function() {
  const statusMap = {
    'new': 'New',
    'acknowledged': 'Acknowledged',
    'investigating': 'Under Investigation',
    'resolved': 'Resolved',
    'false_positive': 'False Positive',
  };
  return statusMap[this.status] || this.status;
});

// Pre-save middleware to update metadata
alertSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    if (this.status === 'acknowledged' && !this.acknowledgedAt) {
      this.acknowledgedAt = new Date();
    }
    if (this.status === 'resolved' && !this.resolvedAt) {
      this.resolvedAt = new Date();
    }
  }
  
  // Update last occurrence
  this.metadata.lastOccurrence = new Date();
  
  next();
});

// Static method to get alert statistics
alertSchema.statics.getStatistics = async function(timeRange = 24) {
  const startTime = new Date(Date.now() - (timeRange * 60 * 60 * 1000));
  
  const stats = await this.aggregate([
    {
      $match: {
        triggeredAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        critical: { $sum: { $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0] } },
        high: { $sum: { $cond: [{ $eq: ['$severity', 'high'] }, 1, 0] } },
        medium: { $sum: { $cond: [{ $eq: ['$severity', 'medium'] }, 1, 0] } },
        low: { $sum: { $cond: [{ $eq: ['$severity', 'low'] }, 1, 0] } },
        new: { $sum: { $cond: [{ $eq: ['$status', 'new'] }, 1, 0] } },
        acknowledged: { $sum: { $cond: [{ $eq: ['$status', 'acknowledged'] }, 1, 0] } },
        resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } },
        avgResolutionTime: {
          $avg: {
            $cond: [
              { $and: [{ $ne: ['$resolvedAt', null] }, { $ne: ['$triggeredAt', null] }] },
              { $divide: [{ $subtract: ['$resolvedAt', '$triggeredAt'] }, 1000 * 60] },
              null
            ]
          }
        }
      }
    }
  ]);
  
  return stats[0] || {
    total: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    new: 0,
    acknowledged: 0,
    resolved: 0,
    avgResolutionTime: 0
  };
};

// Static method to get trending data
alertSchema.statics.getTrendingData = async function(days = 7) {
  const startTime = new Date(Date.now() - (days * 24 * 60 * 60 * 1000));
  
  return await this.aggregate([
    {
      $match: {
        triggeredAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$triggeredAt' } },
          severity: '$severity'
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.date': 1 }
    }
  ]);
};

module.exports = mongoose.model('Alert', alertSchema);
