// Script to update API key permissions for system user to include manage_alerts
db = db.getSiblingDB('exlog');

const result = db.users.updateOne(
  { username: 'system', 'apiKeys.key': 'test-api-key-12345' },
  {
    $set: {
      'apiKeys.$.permissions': ['view_logs', 'view_alerts', 'manage_alerts']
    }
  }
);

print('API key permissions updated:', JSON.stringify(result));

// Verify the key was updated
const user = db.users.findOne({ username: 'system' }, { apiKeys: 1 });
print('System user API keys:', JSON.stringify(user.apiKeys, null, 2));
