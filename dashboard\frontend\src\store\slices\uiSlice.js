import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  theme: 'light',
  notifications: [],
  loading: false,
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action) => {
      state.theme = action.payload
    },
    addNotification: (state, action) => {
      state.notifications.push({
        id: Date.now(),
        ...action.payload,
      })
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      )
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
  },
})

export const {
  setTheme,
  addNotification,
  removeNotification,
  setLoading,
} = uiSlice.actions

export default uiSlice.reducer
