import React, { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Switch,
  Box,
  Typography,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Stack,
  Divider,
} from '@mui/material'
import {
  MoreVert,
  PlayArrow,
  Pause,
  Edit,
  Delete,
  Visibility,
  BugReport,
  Security,
  Speed,
  Computer,
  NetworkCheck,
  Assignment,
} from '@mui/icons-material'
import { format } from 'date-fns'
import { useDispatch } from 'react-redux'
import { updateAlertRule, deleteAlertRule, testAlertRule } from '../../../store/slices/alertsSlice'

const RulesList = ({ rules = [], loading = false, onShowSnackbar }) => {
  const dispatch = useDispatch()
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(25)
  const [selectedRule, setSelectedRule] = useState(null)
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'security': return <Security />
      case 'performance': return <Speed />
      case 'system': return <Computer />
      case 'network': return <NetworkCheck />
      case 'application': return <Assignment />
      case 'compliance': return <Assignment />
      default: return <Assignment />
    }
  }

  const getCategoryColor = (category) => {
    switch (category) {
      case 'security': return 'error'
      case 'performance': return 'warning'
      case 'system': return 'info'
      case 'network': return 'primary'
      case 'application': return 'secondary'
      case 'compliance': return 'success'
      default: return 'default'
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'error'
      case 'high': return 'warning'
      case 'medium': return 'info'
      case 'low': return 'success'
      default: return 'default'
    }
  }

  const getRuleTypeDisplay = (ruleType) => {
    switch (ruleType) {
      case 'threshold': return 'Threshold'
      case 'pattern': return 'Pattern'
      case 'correlation': return 'Correlation'
      case 'anomaly': return 'Anomaly'
      case 'sequence': return 'Sequence'
      default: return ruleType
    }
  }

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleActionClick = (event, rule) => {
    setSelectedRule(rule)
    setActionMenuAnchor(event.currentTarget)
  }

  const handleActionClose = () => {
    setActionMenuAnchor(null)
    setSelectedRule(null)
  }

  const handleToggleRule = async (rule) => {
    try {
      await dispatch(updateAlertRule({
        id: rule._id,
        updates: { enabled: !rule.enabled },
      })).unwrap()
      
      onShowSnackbar(
        `Rule ${rule.enabled ? 'disabled' : 'enabled'} successfully`,
        'success'
      )
    } catch (error) {
      onShowSnackbar('Failed to update rule', 'error')
    }
  }

  const handleViewRule = () => {
    setViewDialogOpen(true)
    setActionMenuAnchor(null) // Close menu but keep selectedRule
  }

  const handleTestRule = async () => {
    try {
      const testData = {
        logLevel: 'error',
        message: 'Test log message',
        source: 'System',
        host: 'test-host',
      }
      
      await dispatch(testAlertRule({
        id: selectedRule._id,
        testData,
      })).unwrap()
      
      onShowSnackbar('Rule test completed successfully', 'success')
    } catch (error) {
      onShowSnackbar('Rule test failed', 'error')
    }
    handleActionClose()
  }

  const handleDeleteRule = async () => {
    try {
      await dispatch(deleteAlertRule(selectedRule._id)).unwrap()
      onShowSnackbar('Rule deleted successfully', 'success')
    } catch (error) {
      onShowSnackbar('Failed to delete rule', 'error')
    }
    handleActionClose()
  }

  const formatTimestamp = (timestamp) => {
    return format(new Date(timestamp), 'MMM dd, yyyy HH:mm')
  }

  const paginatedRules = rules.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)

  if (rules.length === 0 && !loading) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Assignment sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Alert Rules Found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Create your first alert rule to start monitoring your logs.
        </Typography>
      </Box>
    )
  }

  return (
    <>
      <TableContainer component={Paper} variant="outlined">
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Rule</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Severity</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Statistics</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedRules.map((rule) => (
              <TableRow key={rule._id} hover>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {rule.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {rule.description}
                    </Typography>
                    {rule.isDefault && (
                      <Chip
                        label="Default"
                        size="small"
                        color="primary"
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getCategoryIcon(rule.category)}
                    label={rule.category.toUpperCase()}
                    color={getCategoryColor(rule.category)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {getRuleTypeDisplay(rule.ruleType)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={rule.severity.toUpperCase()}
                    color={getSeverityColor(rule.severity)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Switch
                      checked={rule.enabled}
                      onChange={() => handleToggleRule(rule)}
                      size="small"
                    />
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {rule.enabled ? 'Enabled' : 'Disabled'}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2">
                      Triggers: {rule.statistics?.totalTriggers || 0}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {rule.statistics?.lastTriggered 
                        ? `Last: ${formatTimestamp(rule.statistics.lastTriggered)}`
                        : 'Never triggered'
                      }
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={(e) => handleActionClick(e, rule)}
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={rules.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={handleViewRule}>
          <Visibility sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={handleTestRule}>
          <BugReport sx={{ mr: 1 }} />
          Test Rule
        </MenuItem>
        <MenuItem 
          onClick={() => handleToggleRule(selectedRule)}
        >
          {selectedRule?.enabled ? (
            <>
              <Pause sx={{ mr: 1 }} />
              Disable Rule
            </>
          ) : (
            <>
              <PlayArrow sx={{ mr: 1 }} />
              Enable Rule
            </>
          )}
        </MenuItem>
        {!selectedRule?.isDefault && (
          <MenuItem onClick={handleDeleteRule} sx={{ color: 'error.main' }}>
            <Delete sx={{ mr: 1 }} />
            Delete Rule
          </MenuItem>
        )}
      </Menu>

      {/* View Rule Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => {
          setViewDialogOpen(false)
          setSelectedRule(null)
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Rule Details</DialogTitle>
        <DialogContent>
          {selectedRule ? (
            <Stack spacing={3}>
              <Box>
                <Typography variant="h6">{selectedRule.name || 'Unknown Rule'}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedRule.description || 'No description available'}
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip
                  icon={getCategoryIcon(selectedRule.category)}
                  label={selectedRule.category.toUpperCase()}
                  color={getCategoryColor(selectedRule.category)}
                  variant="outlined"
                />
                <Chip
                  label={selectedRule.severity.toUpperCase()}
                  color={getSeverityColor(selectedRule.severity)}
                />
                <Chip
                  label={getRuleTypeDisplay(selectedRule.ruleType)}
                  variant="outlined"
                />
                <Chip
                  label={selectedRule.enabled ? 'Enabled' : 'Disabled'}
                  color={selectedRule.enabled ? 'success' : 'default'}
                />
              </Box>

              <Divider />

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Time Window
                </Typography>
                <Typography variant="body2">
                  {selectedRule.timeWindow?.value} {selectedRule.timeWindow?.unit}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Threshold
                </Typography>
                <Typography variant="body2">
                  {selectedRule.threshold?.operator} {selectedRule.threshold?.value}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Conditions
                </Typography>
                <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                  <pre style={{ margin: 0, fontSize: '0.875rem', overflow: 'auto' }}>
                    {JSON.stringify(selectedRule.conditions, null, 2)}
                  </pre>
                </Paper>
              </Box>

              {selectedRule.actions && selectedRule.actions.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Actions
                  </Typography>
                  {selectedRule.actions.map((action, index) => (
                    <Chip
                      key={index}
                      label={action.type.replace('_', ' ').toUpperCase()}
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                      color={action.enabled ? 'primary' : 'default'}
                    />
                  ))}
                </Box>
              )}

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Statistics
                </Typography>
                <Typography variant="body2">
                  Total Triggers: {selectedRule.statistics?.totalTriggers || 0}
                </Typography>
                <Typography variant="body2">
                  False Positives: {selectedRule.statistics?.falsePositives || 0}
                </Typography>
                {selectedRule.statistics?.lastTriggered && (
                  <Typography variant="body2">
                    Last Triggered: {formatTimestamp(selectedRule.statistics.lastTriggered)}
                  </Typography>
                )}
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Created
                </Typography>
                <Typography variant="body2">
                  {formatTimestamp(selectedRule.createdAt)}
                  {selectedRule.createdBy && (
                    <> by {selectedRule.createdBy.firstName} {selectedRule.createdBy.lastName}</>
                  )}
                </Typography>
              </Box>
            </Stack>
          ) : (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                No rule data available
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setViewDialogOpen(false)
            setSelectedRule(null)
          }}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default RulesList

