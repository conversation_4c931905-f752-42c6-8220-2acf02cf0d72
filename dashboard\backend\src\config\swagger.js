try {
  const swaggerJsdoc = require('swagger-jsdoc');
  const config = require('./index');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'ExLog API',
      version: '1.0.0',
      description: 'ExLog Cybersecurity Log Management Dashboard API',
      contact: {
        name: 'ExLog Development Team',
        email: '<EMAIL>',
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT',
      },
    },
    servers: [
      {
        url: `/`,
        description: 'Current server (relative URL)',
      },
      {
        url: `http://localhost:${config.port}`,
        description: 'Development server (localhost)',
      },
      {
        url: 'https://api.exlog.local',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token for user authentication',
        },
        apiKeyAuth: {
          type: 'api<PERSON>ey',
          in: 'header',
          name: 'X-API-Key',
          description: 'API key for agent authentication',
        },
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Error type',
            },
            message: {
              type: 'string',
              description: 'Error message',
            },
            details: {
              type: 'object',
              description: 'Additional error details',
            },
          },
        },
        SuccessResponse: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['success'],
            },
            message: {
              type: 'string',
              description: 'Success message',
            },
            data: {
              type: 'object',
              description: 'Response data',
            },
          },
        },
        Log: {
          type: 'object',
          required: ['timestamp', 'source', 'host', 'message'],
          description: 'Log entry schema. Supports both camelCase and snake_case field names for compatibility.',
          properties: {
            logId: {
              type: 'string',
              description: 'Unique identifier for the log entry (camelCase)',
              example: 'log_12345_20240101_120000',
            },
            log_id: {
              type: 'string',
              description: 'Unique identifier for the log entry (snake_case, alternative to logId)',
              example: '5b72c7cb-8b97-4b0a-84b4-d59173d3bd7e',
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Log timestamp in ISO8601 format',
              example: '2025-05-26T16:21:38',
            },
            source: {
              type: 'string',
              enum: ['System', 'Application', 'Security', 'Network', 'Custom'],
              description: 'Log source category',
            },
            sourceType: {
              type: 'string',
              enum: ['event', 'application', 'security', 'network', 'audit', 'performance'],
              description: 'Specific source type (camelCase)',
            },
            source_type: {
              type: 'string',
              enum: ['event', 'application', 'security', 'network', 'audit', 'performance'],
              description: 'Specific source type (snake_case, alternative to sourceType)',
            },
            host: {
              type: 'string',
              description: 'Host or system that generated the log',
              example: 'DESKTOP-PLUAU4C',
            },
            logLevel: {
              type: 'string',
              enum: ['critical', 'error', 'warning', 'info', 'debug'],
              description: 'Log severity level (camelCase)',
            },
            log_level: {
              type: 'string',
              enum: ['critical', 'error', 'warning', 'info', 'debug'],
              description: 'Log severity level (snake_case, alternative to logLevel)',
            },
            message: {
              type: 'string',
              description: 'Log message content',
              example: 'The time provider VMICTimeProvider has indicated that the current hardware and operating environment is not supported',
            },
            rawData: {
              type: ['string', 'null'],
              description: 'Original raw log data (camelCase)',
            },
            raw_data: {
              type: ['string', 'null'],
              description: 'Original raw log data (snake_case, alternative to rawData)',
            },
            additionalFields: {
              type: 'object',
              description: 'Additional structured data (camelCase)',
              properties: {
                record_number: { type: 'integer' },
                computer_name: { type: 'string' },
                event_id: { type: 'integer' },
                event_category: { type: 'integer' },
                event_type: { type: 'integer' },
                string_inserts: { type: 'array', items: { type: 'string' } },
                data: { type: ['string', 'null'] },
              },
            },
            additional_fields: {
              type: 'object',
              description: 'Additional structured data (snake_case, alternative to additionalFields)',
              properties: {
                record_number: { type: 'integer' },
                computer_name: { type: 'string' },
                event_id: { type: 'integer' },
                event_category: { type: 'integer' },
                event_type: { type: 'integer' },
                string_inserts: { type: 'array', items: { type: 'string' } },
                data: { type: ['string', 'null'] },
                metadata: {
                  type: 'object',
                  properties: {
                    collection_time: { type: 'string', format: 'date-time' },
                    agent_version: { type: 'string' },
                    standardizer_version: { type: 'string' },
                    windows_event_log: { type: 'boolean' },
                    event_log_source: { type: 'string' },
                  },
                },
              },
            },
            metadata: {
              type: 'object',
              properties: {
                collectionTime: {
                  type: 'string',
                  format: 'date-time',
                },
                agentId: {
                  type: 'string',
                },
                agentVersion: {
                  type: 'string',
                },
                processingTime: {
                  type: 'number',
                },
              },
            },
            tags: {
              type: 'array',
              items: {
                type: 'string',
              },
            },
            severity: {
              type: 'integer',
              minimum: 1,
              maximum: 5,
              description: 'Numeric severity level (1=Low, 5=Emergency)',
            },
          },
        },
        LogIngestionRequest: {
          type: 'object',
          required: ['logs'],
          properties: {
            logs: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/Log',
              },
              minItems: 1,
              description: 'Array of log entries to ingest',
            },
          },
        },
        User: {
          type: 'object',
          properties: {
            _id: {
              type: 'string',
              description: 'User ID',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
            },
            firstName: {
              type: 'string',
              description: 'User first name',
            },
            lastName: {
              type: 'string',
              description: 'User last name',
            },
            role: {
              type: 'string',
              enum: ['admin', 'security_analyst', 'compliance_officer', 'executive'],
              description: 'User role',
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended'],
              description: 'User account status',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        LoginRequest: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
            },
            password: {
              type: 'string',
              description: 'User password',
            },
          },
        },
        LoginResponse: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              enum: ['success'],
            },
            message: {
              type: 'string',
            },
            data: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                  description: 'JWT access token',
                },
                refreshToken: {
                  type: 'string',
                  description: 'JWT refresh token',
                },
                user: {
                  $ref: '#/components/schemas/User',
                },
              },
            },
          },
        },
        PaginationResponse: {
          type: 'object',
          properties: {
            currentPage: {
              type: 'integer',
              minimum: 1,
            },
            totalPages: {
              type: 'integer',
              minimum: 0,
            },
            totalCount: {
              type: 'integer',
              minimum: 0,
            },
            limit: {
              type: 'integer',
              minimum: 1,
            },
            hasNextPage: {
              type: 'boolean',
            },
            hasPrevPage: {
              type: 'boolean',
            },
          },
        },
      },
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization',
      },
      {
        name: 'Logs',
        description: 'Log ingestion and retrieval',
      },
      {
        name: 'Users',
        description: 'User management',
      },
      {
        name: 'Agents',
        description: 'Agent management and monitoring',
      },
      {
        name: 'Alerts',
        description: 'Alert rules and notifications',
      },
      {
        name: 'Reports',
        description: 'Report generation and templates',
      },
      {
        name: 'Dashboards',
        description: 'Dashboard configuration and data',
      },
      {
        name: 'System',
        description: 'System health and monitoring',
      },
    ],
  },
  apis: [
    './src/routes/*.js',
    './src/models/*.js',
  ],
};

  const specs = swaggerJsdoc(options);

  module.exports = specs;

} catch (error) {
  // If swagger dependencies are not available, export a minimal spec
  module.exports = {
    openapi: '3.0.0',
    info: {
      title: 'ExLog API',
      version: '1.0.0',
      description: 'API documentation not available - swagger dependencies missing'
    },
    paths: {}
  };
}
