[Unit]
Description=Linux Log Collection Agent
Documentation=https://github.com/your-org/linux-log-agent
After=network.target
Wants=network.target

[Service]
Type=simple
User=linux-log-agent
Group=linux-log-agent
WorkingDirectory=/opt/linux-agent
ExecStart=/usr/bin/python3 /opt/linux-agent/service/systemd_service.py --config /opt/linux-agent/config/default_config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=linux-agent

# Security settings
NoNewPrivileges=true
PrivateTmp=true
# Use 'read-only' instead of 'strict' to allow reading system logs
ProtectSystem=read-only
ProtectHome=true
# Allow access to log directories and system logs
ReadWritePaths=/var/log/linux-log-agent /tmp
# Allow reading system logs and agent directory
ReadOnlyPaths=/var/log /opt/linux-agent /etc/linux-log-agent
# Add necessary groups for log access
SupplementaryGroups=adm systemd-journal syslog
# Allow access to system logs
PrivateDevices=false
ProtectKernelTunables=false
ProtectControlGroups=false

# Resource limits
LimitNOFILE=65536
MemoryMax=256M
CPUQuota=10%

[Install]
WantedBy=multi-user.target
