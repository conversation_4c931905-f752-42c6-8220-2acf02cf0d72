2025-05-26 16:23:14,535 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-26 16:23:14,536 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-26 16:23:14,553 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:14,559 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:49,604 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-26 16:23:49,605 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-26 16:23:49,625 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:49,633 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 00:40:42,328 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 00:40:42,329 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 00:40:42,511 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 00:43:42,496 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 00:43:42,496 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 00:43:42,515 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:10:59,361 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 01:10:59,361 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 01:10:59,383 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:20:24,405 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 01:20:24,406 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 01:20:24,433 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 01:56:14,165 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 01:56:14,166 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 01:56:14,189 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-30 17:07:46,794 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-30 17:07:46,794 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-30 17:07:46,961 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 09:22:43,544 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 09:22:43,545 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 09:22:43,739 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:08:05,645 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:08:05,645 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:08:05,768 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:13:46,298 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:13:46,299 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:13:46,322 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:18:40,902 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:18:40,903 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:18:40,927 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:20:46,023 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:20:46,024 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:20:46,044 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:25:48,706 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:25:48,706 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:25:48,726 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:35:04,867 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:35:04,868 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:35:04,885 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:38:19,904 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:38:19,905 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:38:19,924 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:45:46,950 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:45:46,950 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:45:46,971 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:54:20,707 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:54:20,708 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:54:20,872 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 10:58:47,922 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 10:58:47,922 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 10:58:48,053 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 11:00:31,179 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 11:00:31,180 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 11:00:31,204 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 11:20:44,576 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 11:20:44,576 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 11:20:44,609 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 11:23:13,855 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 11:23:13,855 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 11:23:13,875 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 11:34:16,045 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 11:34:16,046 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 11:34:16,081 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 22:00:14,593 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 22:00:14,596 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 22:00:14,638 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 22:01:10,882 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 22:01:10,883 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 22:01:10,900 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-02 22:30:51,112 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-02 22:30:51,112 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-02 22:30:51,258 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-03 00:19:44,056 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-03 00:19:44,057 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-03 00:19:44,209 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-03 11:15:51,902 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-03 11:15:51,902 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-03 11:15:52,029 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-03 14:20:07,155 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-03 14:20:07,155 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-03 14:20:07,298 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 16:37:14,578 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 16:37:14,579 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 16:37:14,783 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 17:30:38,140 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 17:30:38,141 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 17:30:38,278 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 17:31:40,469 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 17:31:40,469 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 17:31:40,487 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 17:49:53,834 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 17:49:53,835 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 17:49:53,865 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 19:45:52,714 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 19:45:52,715 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 19:45:52,882 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 21:26:14,715 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 21:26:14,715 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 21:26:14,751 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-06 21:27:36,468 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-06 21:27:36,469 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-06 21:27:36,490 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-09 11:37:43,324 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-09 11:37:43,324 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-09 11:37:43,487 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-09 11:38:39,316 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-09 11:38:39,316 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-09 11:38:39,334 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-09 18:35:09,055 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-09 18:35:09,055 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-09 18:35:09,236 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-09 18:43:01,982 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-09 18:43:01,983 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-09 18:43:02,027 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-09 18:55:55,184 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-09 18:55:55,184 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-09 18:55:55,213 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-09 23:32:45,598 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-09 23:32:45,599 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-09 23:32:45,778 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:15:05,777 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:15:05,778 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:15:05,829 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:27:35,540 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:27:35,540 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:27:35,677 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:29:34,127 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:29:34,127 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:29:34,144 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:34:18,618 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:34:18,619 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:34:18,641 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:38:02,111 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:38:02,111 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:38:02,130 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:41:18,925 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:41:18,925 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:41:18,946 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:42:58,162 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:42:58,162 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:42:58,181 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:44:08,180 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:44:08,181 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:44:08,202 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 00:51:26,861 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 00:51:26,861 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 00:51:26,880 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 01:03:44,530 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 01:03:44,530 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 01:03:44,558 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 01:05:05,248 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-10 01:05:05,249 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-10 01:05:05,265 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-13 17:08:17,630 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-13 17:08:17,630 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-13 17:08:17,781 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-13 17:12:21,833 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-13 17:12:21,834 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-13 17:12:22,555 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-13 17:17:05,762 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-13 17:17:05,763 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-13 17:17:05,784 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-13 17:29:09,808 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-13 17:29:09,808 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-13 17:29:09,839 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-13 18:24:22,372 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-13 18:24:22,373 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-13 18:24:22,584 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-13 18:24:53,443 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-06-13 18:24:53,444 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-06-13 18:24:53,482 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
