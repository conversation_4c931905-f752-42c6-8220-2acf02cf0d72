import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Typography,
  Alert,
  Pagination,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Tabs,
  Tab,
} from '@mui/material'
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Computer as ComputerIcon,
  Smartphone as SmartphoneIcon,
  Tablet as TabletIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  ExitToApp as LogoutIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
} from '@mui/icons-material'
import { settingsService } from '../../../services/settingsService'

const ActivityTab = ({ onSuccess }) => {
  const { user } = useSelector((state) => state.auth)

  const [activeTab, setActiveTab] = useState(0)
  const [loginHistory, setLoginHistory] = useState([])
  const [sessions, setSessions] = useState([])
  const [activityStats, setActivityStats] = useState({})
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  })
  const [isLoading, setIsLoading] = useState(false)
  const [filters, setFilters] = useState({
    success: 'all', // 'all', 'success', 'failed'
    dateRange: 'all', // 'all', 'today', 'week', 'month'
    device: 'all', // 'all', 'desktop', 'mobile', 'tablet'
  })
  const [terminateDialog, setTerminateDialog] = useState({
    open: false,
    sessionId: null,
    isAll: false,
  })

  useEffect(() => {
    if (user?._id) {
      loadActivityData()
    }
  }, [user?._id, pagination.currentPage, filters])

  const loadActivityData = async () => {
    if (!user?._id) {
      onSuccess('User not authenticated', 'error')
      return
    }

    setIsLoading(true)
    try {
      // Load all activity data directly from the API
      const activityResponse = await settingsService.getUserActivity(user._id)
      const activityData = activityResponse.data

      // Set sessions data
      setSessions(activityData.sessions || [])
      setActivityStats(activityData.stats || {})

      // Use the login history directly from the activity response
      let filteredHistory = activityData.loginHistory || []

      // Apply filters
      if (filters.success !== 'all') {
        filteredHistory = filteredHistory.filter(login =>
          filters.success === 'success' ? login.success : !login.success
        )
      }

      if (filters.dateRange !== 'all') {
        const now = new Date()
        const filterDate = new Date()

        switch (filters.dateRange) {
          case 'today':
            filterDate.setHours(0, 0, 0, 0)
            break
          case 'week':
            filterDate.setDate(now.getDate() - 7)
            break
          case 'month':
            filterDate.setMonth(now.getMonth() - 1)
            break
        }

        filteredHistory = filteredHistory.filter(login =>
          new Date(login.timestamp) >= filterDate
        )
      }

      if (filters.device !== 'all') {
        filteredHistory = filteredHistory.filter(login => {
          const deviceType = getDeviceType(login.deviceInfo)
          return deviceType.toLowerCase() === filters.device
        })
      }

      setLoginHistory(filteredHistory)

      // Update pagination based on filtered results
      const totalItems = (activityData.loginHistory || []).length
      const totalPages = Math.ceil(totalItems / pagination.itemsPerPage)

      setPagination(prev => ({
        ...prev,
        totalItems,
        totalPages,
        hasNextPage: pagination.currentPage < totalPages,
        hasPrevPage: pagination.currentPage > 1,
      }))
    } catch (error) {
      onSuccess('Failed to load activity data', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  // Legacy method for refresh button
  const loadLoginHistory = loadActivityData

  const getDeviceType = (deviceInfo) => {
    if (!deviceInfo) return 'Unknown'
    
    const device = deviceInfo.device?.toLowerCase() || ''
    if (device.includes('mobile') || device.includes('phone')) {
      return 'Mobile'
    } else if (device.includes('tablet')) {
      return 'Tablet'
    }
    return 'Desktop'
  }

  const getDeviceIcon = (deviceInfo) => {
    const deviceType = getDeviceType(deviceInfo)
    
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <SmartphoneIcon fontSize="small" />
      case 'tablet':
        return <TabletIcon fontSize="small" />
      default:
        return <ComputerIcon fontSize="small" />
    }
  }

  const getStatusChip = (success, failureReason) => {
    if (success) {
      return (
        <Chip
          icon={<CheckCircleIcon />}
          label="Success"
          color="success"
          size="small"
        />
      )
    } else {
      return (
        <Chip
          icon={<ErrorIcon />}
          label={failureReason || 'Failed'}
          color="error"
          size="small"
        />
      )
    }
  }

  const formatLocation = (location) => {
    return location || 'Unknown Location'
  }

  const handlePageChange = (event, newPage) => {
    setPagination(prev => ({ ...prev, currentPage: newPage }))
  }

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }))
    setPagination(prev => ({ ...prev, currentPage: 1 })) // Reset to first page
  }

  const handleTerminateSession = async (sessionId, isAll = false) => {
    if (!user?._id) {
      onSuccess('User not authenticated', 'error')
      return
    }

    try {
      if (isAll) {
        await settingsService.terminateAllUserSessions(user._id)
        onSuccess('All other sessions terminated successfully', 'success')
      } else {
        await settingsService.terminateUserSession(user._id, sessionId)
        onSuccess('Session terminated successfully', 'success')
      }

      // Reload activity data
      await loadActivityData()
      setTerminateDialog({ open: false, sessionId: null, isAll: false })
    } catch (error) {
      onSuccess('Failed to terminate session', 'error')
    }
  }

  const openTerminateDialog = (sessionId, isAll = false) => {
    setTerminateDialog({ open: true, sessionId, isAll })
  }

  const closeTerminateDialog = () => {
    setTerminateDialog({ open: false, sessionId: null, isAll: false })
  }

  const getActivitySummary = () => {
    // Use stats from API if available, otherwise calculate from visible data
    if (activityStats.totalLogins !== undefined) {
      return {
        total: activityStats.totalLogins + activityStats.failedLogins,
        successful: activityStats.totalLogins,
        failed: activityStats.failedLogins,
        successRate: activityStats.totalLogins > 0 ?
          ((activityStats.totalLogins / (activityStats.totalLogins + activityStats.failedLogins)) * 100).toFixed(1) : 0
      }
    }

    // Fallback to calculating from visible login history
    const total = loginHistory.length
    const successful = loginHistory.filter(login => login.success).length
    const failed = total - successful
    const successRate = total > 0 ? ((successful / total) * 100).toFixed(1) : 0

    return { total, successful, failed, successRate }
  }

  const getSessionStatusChip = (session) => {
    if (session.isActive) {
      return (
        <Chip
          icon={<CheckCircleIcon />}
          label="Active"
          color="success"
          size="small"
        />
      )
    } else {
      return (
        <Chip
          icon={<ErrorIcon />}
          label="Terminated"
          color="default"
          size="small"
        />
      )
    }
  }

  const summary = getActivitySummary()

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Activity Summary */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {summary.total}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Logins
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main">
                    {summary.successful}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Successful
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="error.main">
                    {summary.failed}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Failed
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" color="info.main">
                    {activityStats.activeSessions || sessions.filter(s => s.isActive).length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Sessions
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Tabs for Login History and Sessions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Account Activity"
              action={
                <Button
                  startIcon={<RefreshIcon />}
                  onClick={loadActivityData}
                  disabled={isLoading}
                >
                  Refresh
                </Button>
              }
            />
            <CardContent>
              <Tabs
                value={activeTab}
                onChange={(e, newValue) => setActiveTab(newValue)}
                sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}
              >
                <Tab label="Login History" />
                <Tab label="Active Sessions" />
              </Tabs>

              {/* Tab Content */}
              {activeTab === 0 && (
                <Box>
                  {/* Filters */}
                  <Card variant="outlined" sx={{ mb: 3 }}>
                    <CardHeader title="Filters" />
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <FormControl fullWidth>
                            <InputLabel>Status</InputLabel>
                            <Select
                              value={filters.success}
                              label="Status"
                              onChange={(e) => handleFilterChange('success', e.target.value)}
                            >
                              <MenuItem value="all">All</MenuItem>
                              <MenuItem value="success">Successful Only</MenuItem>
                              <MenuItem value="failed">Failed Only</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <FormControl fullWidth>
                            <InputLabel>Date Range</InputLabel>
                            <Select
                              value={filters.dateRange}
                              label="Date Range"
                              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                            >
                              <MenuItem value="all">All Time</MenuItem>
                              <MenuItem value="today">Today</MenuItem>
                              <MenuItem value="week">Last 7 Days</MenuItem>
                              <MenuItem value="month">Last 30 Days</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <FormControl fullWidth>
                            <InputLabel>Device Type</InputLabel>
                            <Select
                              value={filters.device}
                              label="Device Type"
                              onChange={(e) => handleFilterChange('device', e.target.value)}
                            >
                              <MenuItem value="all">All Devices</MenuItem>
                              <MenuItem value="desktop">Desktop</MenuItem>
                              <MenuItem value="mobile">Mobile</MenuItem>
                              <MenuItem value="tablet">Tablet</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>

                  {/* Login History Table */}
                  {loginHistory.length === 0 ? (
                    <Alert severity="info">
                      No login history found for the selected filters.
                    </Alert>
                  ) : (
                    <>
                      <TableContainer component={Paper} variant="outlined">
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Status</TableCell>
                              <TableCell>Date & Time</TableCell>
                              <TableCell>Location</TableCell>
                              <TableCell>Device</TableCell>
                              <TableCell>Browser</TableCell>
                              <TableCell>IP Address</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {loginHistory.map((login, index) => (
                              <TableRow key={index}>
                                <TableCell>
                                  {getStatusChip(login.success, login.failureReason)}
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">
                                    {settingsService.formatDateTime(login.timestamp)}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">
                                    {formatLocation(login.location)}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {getDeviceIcon(login.deviceInfo)}
                                    <Box>
                                      <Typography variant="body2">
                                        {getDeviceType(login.deviceInfo)}
                                      </Typography>
                                      <Typography variant="caption" color="text.secondary">
                                        {login.deviceInfo?.os || 'Unknown OS'}
                                      </Typography>
                                    </Box>
                                  </Box>
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">
                                    {login.deviceInfo?.browser || 'Unknown Browser'}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2" fontFamily="monospace">
                                    {login.ip || 'Unknown'}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>

                      {/* Pagination */}
                      {pagination.totalPages > 1 && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                          <Pagination
                            count={pagination.totalPages}
                            page={pagination.currentPage}
                            onChange={handlePageChange}
                            color="primary"
                            showFirstButton
                            showLastButton
                          />
                        </Box>
                      )}
                    </>
                  )}
                </Box>
              )}

              {/* Sessions Tab */}
              {activeTab === 1 && (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                    <Typography variant="h6">
                      Active Sessions ({sessions.filter(s => s.isActive).length})
                    </Typography>
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<LogoutIcon />}
                      onClick={() => openTerminateDialog(null, true)}
                      disabled={sessions.filter(s => s.isActive).length <= 1}
                    >
                      Terminate All Other Sessions
                    </Button>
                  </Box>

                  {sessions.length === 0 ? (
                    <Alert severity="info">
                      No session data available.
                    </Alert>
                  ) : (
                    <TableContainer component={Paper} variant="outlined">
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Status</TableCell>
                            <TableCell>Device</TableCell>
                            <TableCell>Browser</TableCell>
                            <TableCell>IP Address</TableCell>
                            <TableCell>Created</TableCell>
                            <TableCell>Last Activity</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {sessions.map((session, index) => (
                            <TableRow key={session.sessionId || index}>
                              <TableCell>
                                {getSessionStatusChip(session)}
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {getDeviceIcon(session.deviceInfo)}
                                  <Box>
                                    <Typography variant="body2">
                                      {getDeviceType(session.deviceInfo)}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {session.deviceInfo?.os || 'Unknown OS'}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {session.deviceInfo?.browser || 'Unknown Browser'}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2" fontFamily="monospace">
                                  {session.deviceInfo?.ip || 'Unknown'}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {settingsService.formatDateTime(session.createdAt)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {settingsService.formatTimeAgo(session.lastActivity)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                {session.isActive && (
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => openTerminateDialog(session.sessionId, false)}
                                    title="Terminate Session"
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Security Tips */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Security Tips" />
            <CardContent>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Monitor Your Activity:</strong> Regularly review your login history and active sessions to detect any unauthorized access attempts.
                </Typography>
              </Alert>
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Suspicious Activity:</strong> If you notice failed login attempts, logins from unknown locations, or unexpected active sessions,
                  change your password immediately and terminate suspicious sessions.
                </Typography>
              </Alert>
              <Alert severity="success">
                <Typography variant="body2">
                  <strong>Best Practices:</strong> Use strong, unique passwords, enable MFA, log out from shared devices, and regularly review your active sessions.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Terminate Session Dialog */}
      <Dialog
        open={terminateDialog.open}
        onClose={closeTerminateDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SecurityIcon color="warning" />
            {terminateDialog.isAll ? 'Terminate All Other Sessions' : 'Terminate Session'}
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {terminateDialog.isAll
              ? 'Are you sure you want to terminate all other active sessions? This will log you out from all other devices and browsers.'
              : 'Are you sure you want to terminate this session? The user will be logged out from that device/browser.'}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeTerminateDialog}>
            Cancel
          </Button>
          <Button
            onClick={() => handleTerminateSession(terminateDialog.sessionId, terminateDialog.isAll)}
            color="error"
            variant="contained"
          >
            {terminateDialog.isAll ? 'Terminate All' : 'Terminate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ActivityTab
