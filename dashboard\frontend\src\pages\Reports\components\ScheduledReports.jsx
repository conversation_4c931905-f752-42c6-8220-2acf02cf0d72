import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Switch,
  FormControlLabel,
} from '@mui/material'
import {
  Add,
  MoreVert,
  PlayArrow,
  Pause,
  Edit,
  Delete,
  Schedule,
  Email,
  CheckCircle,
  Error,
  Warning,
} from '@mui/icons-material'

const ScheduledReports = () => {
  const [schedules, setSchedules] = useState([
    {
      id: 1,
      name: 'Daily Security Summary',
      reportName: 'Security Analytics Dashboard',
      frequency: 'daily',
      nextRun: new Date(Date.now() + 24 * 60 * 60 * 1000),
      lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000),
      status: 'active',
      lastStatus: 'success',
      recipients: ['<EMAIL>', '<EMAIL>'],
      format: 'pdf',
    },
    {
      id: 2,
      name: 'Weekly Compliance Report',
      reportName: 'PCI DSS Compliance',
      frequency: 'weekly',
      nextRun: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: 'active',
      lastStatus: 'success',
      recipients: ['<EMAIL>'],
      format: 'html',
    },
    {
      id: 3,
      name: 'Monthly Executive Summary',
      reportName: 'Executive Dashboard',
      frequency: 'monthly',
      nextRun: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      lastRun: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      status: 'paused',
      lastStatus: 'failed',
      recipients: ['<EMAIL>', '<EMAIL>'],
      format: 'pdf',
    },
  ])

  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedSchedule, setSelectedSchedule] = useState(null)
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  const handleMenuOpen = (event, schedule) => {
    setAnchorEl(event.currentTarget)
    setSelectedSchedule(schedule)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedSchedule(null)
  }

  const handleToggleStatus = (scheduleId) => {
    setSchedules(schedules.map(schedule => 
      schedule.id === scheduleId 
        ? { ...schedule, status: schedule.status === 'active' ? 'paused' : 'active' }
        : schedule
    ))
  }

  const handleRunNow = (scheduleId) => {
    console.log('Running schedule:', scheduleId)
    // TODO: Implement immediate execution
    handleMenuClose()
  }

  const handleDeleteSchedule = () => {
    if (selectedSchedule) {
      setSchedules(schedules.filter(s => s.id !== selectedSchedule.id))
      setDeleteDialogOpen(false)
      setSelectedSchedule(null)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success'
      case 'paused': return 'warning'
      case 'error': return 'error'
      default: return 'default'
    }
  }

  const getLastStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircle color="success" />
      case 'failed': return <Error color="error" />
      case 'warning': return <Warning color="warning" />
      default: return null
    }
  }

  const renderCreateScheduleDialog = () => (
    <Dialog
      open={createDialogOpen}
      onClose={() => setCreateDialogOpen(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>Create Scheduled Report</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Schedule Name"
              placeholder="e.g., Daily Security Summary"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Report</InputLabel>
              <Select label="Report">
                <MenuItem value="security-analytics">Security Analytics Dashboard</MenuItem>
                <MenuItem value="compliance-pci">PCI DSS Compliance Report</MenuItem>
                <MenuItem value="incident-summary">Incident Summary</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Frequency</InputLabel>
              <Select label="Frequency">
                <MenuItem value="daily">Daily</MenuItem>
                <MenuItem value="weekly">Weekly</MenuItem>
                <MenuItem value="monthly">Monthly</MenuItem>
                <MenuItem value="custom">Custom</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Format</InputLabel>
              <Select label="Format">
                <MenuItem value="pdf">PDF</MenuItem>
                <MenuItem value="html">HTML</MenuItem>
                <MenuItem value="csv">CSV</MenuItem>
                <MenuItem value="json">JSON</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Delivery Method</InputLabel>
              <Select label="Delivery Method">
                <MenuItem value="email">Email</MenuItem>
                <MenuItem value="filesystem">File System</MenuItem>
                <MenuItem value="sftp">SFTP</MenuItem>
                <MenuItem value="webhook">Webhook</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Recipients"
              placeholder="Enter email addresses separated by commas"
              multiline
              rows={2}
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Start schedule immediately"
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setCreateDialogOpen(false)}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            // TODO: Implement schedule creation
            setCreateDialogOpen(false)
          }}
        >
          Create Schedule
        </Button>
      </DialogActions>
    </Dialog>
  )

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h5" gutterBottom>
            Scheduled Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Automate report generation and delivery with flexible scheduling options.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Schedule
        </Button>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="primary">
                {schedules.filter(s => s.status === 'active').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Schedules
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="warning">
                {schedules.filter(s => s.status === 'paused').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Paused Schedules
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="success">
                {schedules.filter(s => s.lastStatus === 'success').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Successful Runs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="error">
                {schedules.filter(s => s.lastStatus === 'failed').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Failed Runs
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Schedules Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Schedule Name</TableCell>
              <TableCell>Report</TableCell>
              <TableCell>Frequency</TableCell>
              <TableCell>Next Run</TableCell>
              <TableCell>Last Run</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Recipients</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {schedules.map((schedule) => (
              <TableRow key={schedule.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getLastStatusIcon(schedule.lastStatus)}
                    <Typography sx={{ ml: 1 }}>{schedule.name}</Typography>
                  </Box>
                </TableCell>
                <TableCell>{schedule.reportName}</TableCell>
                <TableCell>
                  <Chip
                    label={schedule.frequency}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  {schedule.nextRun.toLocaleDateString()} {schedule.nextRun.toLocaleTimeString()}
                </TableCell>
                <TableCell>
                  {schedule.lastRun.toLocaleDateString()} {schedule.lastRun.toLocaleTimeString()}
                </TableCell>
                <TableCell>
                  <Chip
                    label={schedule.status}
                    size="small"
                    color={getStatusColor(schedule.status)}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Email sx={{ mr: 1, fontSize: 16 }} />
                    {schedule.recipients.length}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Switch
                      checked={schedule.status === 'active'}
                      onChange={() => handleToggleStatus(schedule.id)}
                      size="small"
                    />
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, schedule)}
                    >
                      <MoreVert />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {schedules.length === 0 && (
        <Alert severity="info" sx={{ mt: 3 }}>
          No scheduled reports found. Create your first schedule to automate report delivery.
        </Alert>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleRunNow(selectedSchedule?.id)}>
          <PlayArrow sx={{ mr: 1 }} />
          Run Now
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Edit sx={{ mr: 1 }} />
          Edit Schedule
        </MenuItem>
        <MenuItem onClick={() => {
          setDeleteDialogOpen(true)
          handleMenuClose()
        }}>
          <Delete sx={{ mr: 1 }} />
          Delete Schedule
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Schedule</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the schedule "{selectedSchedule?.name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteSchedule}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {renderCreateScheduleDialog()}
    </Box>
  )
}

export default ScheduledReports
