const AlertRule = require('../models/AlertRule');
const logger = require('../utils/logger');

class DefaultRulesService {
  constructor() {
    this.defaultRules = [
      {
        name: 'Multiple Failed Login Attempts',
        description: 'Detects multiple failed login attempts from the same IP address within a short time window',
        severity: 'high',
        category: 'security',
        ruleType: 'threshold',
        conditions: {
          field: 'logLevel',
          value: 'error',
          pattern: 'login failed|authentication failed|invalid credentials',
        },
        timeWindow: {
          value: 15,
          unit: 'minutes',
        },
        threshold: {
          operator: '>=',
          value: 5,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
          {
            type: 'send_email',
            config: {
              template: 'security_alert',
              priority: 'high',
            },
            enabled: true,
          },
        ],
        suppressionRules: {
          enabled: true,
          duration: {
            value: 30,
            unit: 'minutes',
          },
        },
        tags: ['authentication', 'brute-force', 'security'],
        priority: 8,
      },
      {
        name: 'Privilege Escalation Attempt',
        description: 'Detects attempts to escalate privileges or access restricted resources',
        severity: 'critical',
        category: 'security',
        ruleType: 'pattern',
        conditions: {
          pattern: 'privilege escalation|sudo|runas|elevation|administrator access',
          logLevel: 'warning',
        },
        timeWindow: {
          value: 5,
          unit: 'minutes',
        },
        threshold: {
          operator: '>=',
          value: 1,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
          {
            type: 'send_email',
            config: {
              template: 'critical_security_alert',
              priority: 'critical',
            },
            enabled: true,
          },
        ],
        escalationRules: {
          enabled: true,
          levels: [
            {
              level: 1,
              delay: {
                value: 5,
                unit: 'minutes',
              },
              actions: [
                {
                  type: 'send_email',
                  config: {
                    recipients: ['<EMAIL>'],
                  },
                },
              ],
            },
          ],
        },
        tags: ['privilege-escalation', 'security', 'critical'],
        priority: 10,
      },
      {
        name: 'Unusual Access Pattern',
        description: 'Detects unusual access patterns that may indicate compromised accounts',
        severity: 'medium',
        category: 'security',
        ruleType: 'correlation',
        conditions: {
          events: [
            {
              type: 'login_success',
              field: 'source',
              threshold: 1,
            },
            {
              type: 'access_denied',
              field: 'source',
              threshold: 3,
            },
          ],
        },
        timeWindow: {
          value: 1,
          unit: 'hours',
        },
        threshold: {
          operator: '>=',
          value: 1,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
        ],
        tags: ['access-pattern', 'security', 'behavioral'],
        priority: 6,
      },
      {
        name: 'System Error Spike',
        description: 'Detects unusual spikes in system errors that may indicate system compromise or failure',
        severity: 'high',
        category: 'system',
        ruleType: 'threshold',
        conditions: {
          field: 'logLevel',
          value: 'error',
        },
        timeWindow: {
          value: 10,
          unit: 'minutes',
        },
        threshold: {
          operator: '>',
          value: 20,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
          {
            type: 'send_email',
            config: {
              template: 'system_alert',
              priority: 'high',
            },
            enabled: true,
          },
        ],
        suppressionRules: {
          enabled: true,
          duration: {
            value: 15,
            unit: 'minutes',
          },
        },
        tags: ['system-errors', 'performance', 'reliability'],
        priority: 7,
      },
      {
        name: 'Critical Service Failure',
        description: 'Detects critical service failures or crashes',
        severity: 'critical',
        category: 'system',
        ruleType: 'pattern',
        conditions: {
          pattern: 'service stopped|service failed|critical error|system crash|fatal error',
          logLevel: 'critical',
        },
        timeWindow: {
          value: 1,
          unit: 'minutes',
        },
        threshold: {
          operator: '>=',
          value: 1,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
          {
            type: 'send_email',
            config: {
              template: 'critical_system_alert',
              priority: 'critical',
            },
            enabled: true,
          },
        ],
        escalationRules: {
          enabled: true,
          levels: [
            {
              level: 1,
              delay: {
                value: 2,
                unit: 'minutes',
              },
              actions: [
                {
                  type: 'send_email',
                  config: {
                    recipients: ['<EMAIL>'],
                  },
                },
              ],
            },
          ],
        },
        tags: ['service-failure', 'critical', 'system'],
        priority: 10,
      },
      {
        name: 'Suspicious Network Activity',
        description: 'Detects suspicious network activity patterns',
        severity: 'medium',
        category: 'network',
        ruleType: 'threshold',
        conditions: {
          field: 'source',
          value: 'Network',
          pattern: 'connection refused|timeout|suspicious traffic',
        },
        timeWindow: {
          value: 30,
          unit: 'minutes',
        },
        threshold: {
          operator: '>',
          value: 10,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
        ],
        tags: ['network', 'security', 'suspicious'],
        priority: 5,
      },
      {
        name: 'Application Performance Degradation',
        description: 'Detects application performance issues and slow response times',
        severity: 'medium',
        category: 'performance',
        ruleType: 'threshold',
        conditions: {
          field: 'source',
          value: 'Application',
          pattern: 'slow response|timeout|performance|latency',
        },
        timeWindow: {
          value: 20,
          unit: 'minutes',
        },
        threshold: {
          operator: '>',
          value: 15,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
        ],
        suppressionRules: {
          enabled: true,
          duration: {
            value: 20,
            unit: 'minutes',
          },
        },
        tags: ['performance', 'application', 'latency'],
        priority: 4,
      },
      {
        name: 'Data Access Violation',
        description: 'Detects unauthorized data access attempts',
        severity: 'high',
        category: 'security',
        ruleType: 'pattern',
        conditions: {
          pattern: 'access denied|unauthorized access|permission denied|forbidden',
          source: 'Security',
        },
        timeWindow: {
          value: 5,
          unit: 'minutes',
        },
        threshold: {
          operator: '>=',
          value: 3,
        },
        actions: [
          {
            type: 'create_alert',
            config: {},
            enabled: true,
          },
          {
            type: 'send_email',
            config: {
              template: 'security_alert',
              priority: 'high',
            },
            enabled: true,
          },
        ],
        tags: ['data-access', 'security', 'unauthorized'],
        priority: 8,
      },
    ];
  }

  async initializeDefaultRules(createdBy) {
    try {
      logger.info('Initializing default alert rules...');
      
      const existingRules = await AlertRule.find({ isDefault: true });
      const existingRuleNames = existingRules.map(rule => rule.name);
      
      let createdCount = 0;
      let skippedCount = 0;
      
      for (const ruleData of this.defaultRules) {
        if (existingRuleNames.includes(ruleData.name)) {
          logger.debug(`Skipping existing default rule: ${ruleData.name}`);
          skippedCount++;
          continue;
        }
        
        const rule = new AlertRule({
          ...ruleData,
          isDefault: true,
          enabled: true,
          createdBy: createdBy,
          lastModifiedBy: createdBy,
          metadata: {
            version: '1.0.0',
            source: 'system',
          },
        });
        
        await rule.save();
        createdCount++;
        logger.debug(`Created default rule: ${rule.name}`);
      }
      
      logger.info(`Default rules initialization complete. Created: ${createdCount}, Skipped: ${skippedCount}`);
      
      return {
        created: createdCount,
        skipped: skippedCount,
        total: this.defaultRules.length,
      };
      
    } catch (error) {
      logger.error('Failed to initialize default rules:', error);
      throw error;
    }
  }

  async updateDefaultRules(createdBy) {
    try {
      logger.info('Updating default alert rules...');
      
      let updatedCount = 0;
      
      for (const ruleData of this.defaultRules) {
        const existingRule = await AlertRule.findOne({ 
          name: ruleData.name, 
          isDefault: true 
        });
        
        if (existingRule) {
          // Update existing rule with new configuration
          Object.assign(existingRule, {
            ...ruleData,
            lastModifiedBy: createdBy,
            updatedAt: new Date(),
          });
          
          await existingRule.save();
          updatedCount++;
          logger.debug(`Updated default rule: ${existingRule.name}`);
        }
      }
      
      logger.info(`Default rules update complete. Updated: ${updatedCount} rules`);
      
      return {
        updated: updatedCount,
        total: this.defaultRules.length,
      };
      
    } catch (error) {
      logger.error('Failed to update default rules:', error);
      throw error;
    }
  }

  async resetDefaultRules(createdBy) {
    try {
      logger.info('Resetting default alert rules...');
      
      // Remove all existing default rules
      const deleteResult = await AlertRule.deleteMany({ isDefault: true });
      logger.info(`Removed ${deleteResult.deletedCount} existing default rules`);
      
      // Recreate all default rules
      const createResult = await this.initializeDefaultRules(createdBy);
      
      logger.info('Default rules reset complete');
      
      return {
        deleted: deleteResult.deletedCount,
        ...createResult,
      };
      
    } catch (error) {
      logger.error('Failed to reset default rules:', error);
      throw error;
    }
  }

  getDefaultRulesInfo() {
    return {
      count: this.defaultRules.length,
      categories: [...new Set(this.defaultRules.map(rule => rule.category))],
      severities: [...new Set(this.defaultRules.map(rule => rule.severity))],
      ruleTypes: [...new Set(this.defaultRules.map(rule => rule.ruleType))],
      rules: this.defaultRules.map(rule => ({
        name: rule.name,
        description: rule.description,
        severity: rule.severity,
        category: rule.category,
        ruleType: rule.ruleType,
        tags: rule.tags,
      })),
    };
  }

  async validateDefaultRules() {
    const validationResults = [];
    
    for (const ruleData of this.defaultRules) {
      const validation = {
        name: ruleData.name,
        valid: true,
        errors: [],
      };
      
      // Validate required fields
      if (!ruleData.name || !ruleData.description) {
        validation.valid = false;
        validation.errors.push('Missing required fields');
      }
      
      // Validate conditions based on rule type
      if (!this.validateRuleConditions(ruleData)) {
        validation.valid = false;
        validation.errors.push('Invalid conditions for rule type');
      }
      
      // Validate time window
      if (!ruleData.timeWindow || !ruleData.timeWindow.value || !ruleData.timeWindow.unit) {
        validation.valid = false;
        validation.errors.push('Invalid time window configuration');
      }
      
      validationResults.push(validation);
    }
    
    return validationResults;
  }

  validateRuleConditions(ruleData) {
    const { ruleType, conditions } = ruleData;
    
    switch (ruleType) {
      case 'threshold':
        return conditions.field && (conditions.value !== undefined || conditions.pattern);
      case 'pattern':
        return conditions.pattern || conditions.regex;
      case 'correlation':
        return conditions.events && Array.isArray(conditions.events) && conditions.events.length >= 2;
      case 'anomaly':
        return conditions.field && conditions.baseline;
      case 'sequence':
        return conditions.sequence && Array.isArray(conditions.sequence) && conditions.sequence.length >= 2;
      default:
        return false;
    }
  }
}

// Singleton instance
let defaultRulesServiceInstance = null;

function getDefaultRulesService() {
  if (!defaultRulesServiceInstance) {
    defaultRulesServiceInstance = new DefaultRulesService();
  }
  return defaultRulesServiceInstance;
}

module.exports = {
  DefaultRulesService,
  getDefaultRulesService,
};
