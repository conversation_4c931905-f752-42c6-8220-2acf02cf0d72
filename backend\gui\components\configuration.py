"""
Configuration component for editing API keys, endpoints, and settings
"""

import flet as ft
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from gui.utils.theme import AppTheme
from config.config_manager import ConfigManager

class Configuration(ft.Control):
    """Configuration management component"""
    
    def __init__(self, page: ft.Page, logger):
        super().__init__()
        self.page = page
        self.logger = logger
        self.config_manager = ConfigManager()
        self.config_data: Dict[str, Any] = {}
        
        # Form controls
        self.api_key_field: Optional[ft.TextField] = None
        self.endpoint_field: Optional[ft.TextField] = None
        self.batch_size_field: Optional[ft.TextField] = None
        self.timeout_field: Optional[ft.TextField] = None
        self.log_level_dropdown: Optional[ft.Dropdown] = None
        self.processing_interval_field: Optional[ft.TextField] = None
        
        # Status
        self.status_text: Optional[ft.Text] = None
        
    async def initialize(self):
        """Initialize the configuration component"""
        try:
            self.logger.info("Initializing configuration component")
            
            # Load current configuration
            await self._load_configuration()
            
            # Create form controls
            self._create_form_controls()
            
            self.logger.info("Configuration component initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing configuration component: {e}")
            raise
    
    async def _load_configuration(self):
        """Load current configuration"""
        try:
            self.config_data = self.config_manager.load_config()
            self.logger.info("Configuration loaded successfully")
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            # Use default configuration
            self.config_data = {}
    
    def _create_form_controls(self):
        """Create form input controls"""
        # API Configuration
        self.api_key_field = ft.TextField(
            label="API Key",
            value=self.config_data.get('exlog_api', {}).get('api_key', ''),
            password=True,
            can_reveal_password=True,
            width=400,
            helper_text="Your ExLog API key for authentication"
        )
        
        self.endpoint_field = ft.TextField(
            label="API Endpoint",
            value=self.config_data.get('exlog_api', {}).get('endpoint', ''),
            width=400,
            helper_text="ExLog API endpoint URL"
        )
        
        self.batch_size_field = ft.TextField(
            label="Batch Size",
            value=str(self.config_data.get('exlog_api', {}).get('batch_size', 10)),
            width=200,
            helper_text="Number of logs to send in each batch"
        )
        
        self.timeout_field = ft.TextField(
            label="Timeout (seconds)",
            value=str(self.config_data.get('exlog_api', {}).get('timeout', 30)),
            width=200,
            helper_text="API request timeout"
        )
        
        # General Configuration
        self.log_level_dropdown = ft.Dropdown(
            label="Log Level",
            value=self.config_data.get('general', {}).get('log_level', 'INFO'),
            width=200,
            options=[
                ft.dropdown.Option("DEBUG"),
                ft.dropdown.Option("INFO"),
                ft.dropdown.Option("WARNING"),
                ft.dropdown.Option("ERROR"),
                ft.dropdown.Option("CRITICAL")
            ],
            helper_text="Application logging level"
        )
        
        self.processing_interval_field = ft.TextField(
            label="Processing Interval (seconds)",
            value=str(self.config_data.get('general', {}).get('processing_interval', 5)),
            width=200,
            helper_text="How often to process logs"
        )
        
        # Status text
        self.status_text = ft.Text(
            "Ready to save configuration",
            size=12,
            color=AppTheme.ON_BACKGROUND
        )
    
    async def _save_configuration(self, e):
        """Save configuration changes"""
        try:
            self.logger.info("Saving configuration changes")
            
            # Update configuration data
            if 'exlog_api' not in self.config_data:
                self.config_data['exlog_api'] = {}
            if 'general' not in self.config_data:
                self.config_data['general'] = {}
            
            # API settings
            self.config_data['exlog_api']['api_key'] = self.api_key_field.value
            self.config_data['exlog_api']['endpoint'] = self.endpoint_field.value
            self.config_data['exlog_api']['batch_size'] = int(self.batch_size_field.value or 10)
            self.config_data['exlog_api']['timeout'] = int(self.timeout_field.value or 30)
            
            # General settings
            self.config_data['general']['log_level'] = self.log_level_dropdown.value
            self.config_data['general']['processing_interval'] = int(self.processing_interval_field.value or 5)
            
            # Save to file
            self.config_manager.config = self.config_data
            self.config_manager.save_config()
            
            # Update status
            self.status_text.value = "Configuration saved successfully!"
            self.status_text.color = AppTheme.SUCCESS_COLOR
            
            self.logger.info("Configuration saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            self.status_text.value = f"Error saving configuration: {str(e)}"
            self.status_text.color = AppTheme.ERROR_COLOR
        
        await self.status_text.update_async()
    
    async def _test_connection(self, e):
        """Test API connection"""
        try:
            self.logger.info("Testing API connection")
            
            # Update status
            self.status_text.value = "Testing connection..."
            self.status_text.color = AppTheme.INFO_COLOR
            await self.status_text.update_async()
            
            # TODO: Implement actual connection test
            # For now, just simulate a test
            import asyncio
            await asyncio.sleep(2)
            
            self.status_text.value = "Connection test successful!"
            self.status_text.color = AppTheme.SUCCESS_COLOR
            
        except Exception as e:
            self.logger.error(f"Error testing connection: {e}")
            self.status_text.value = f"Connection test failed: {str(e)}"
            self.status_text.color = AppTheme.ERROR_COLOR
        
        await self.status_text.update_async()
    
    async def _reset_to_defaults(self, e):
        """Reset configuration to defaults"""
        try:
            # Show confirmation dialog
            def close_dialog(e):
                confirm_dialog.open = False
                self.page.update()

            def confirm_reset(e):
                confirm_dialog.open = False
                self.page.update()
                import asyncio
                asyncio.create_task(self._perform_reset())

            confirm_dialog = ft.AlertDialog(
                modal=True,
                title=ft.Text("Reset Configuration"),
                content=ft.Text("Are you sure you want to reset all settings to defaults? This cannot be undone."),
                actions=[
                    ft.TextButton("Cancel", on_click=close_dialog),
                    ft.TextButton("Reset", on_click=confirm_reset, style=ft.ButtonStyle(color=AppTheme.ERROR_COLOR))
                ]
            )

            self.page.dialog = confirm_dialog
            confirm_dialog.open = True
            await self.page.update_async()

        except Exception as e:
            self.logger.error(f"Error showing reset dialog: {e}")
    
    async def _perform_reset(self):
        """Perform the actual reset"""
        try:
            # Load default configuration
            default_config_path = Path("config/default_config.yaml")
            if default_config_path.exists():
                with open(default_config_path, 'r') as f:
                    self.config_data = yaml.safe_load(f)
            else:
                self.config_data = {}
            
            # Update form controls
            self._update_form_values()
            
            self.status_text.value = "Configuration reset to defaults"
            self.status_text.color = AppTheme.INFO_COLOR
            
            await self.update_async()
            
        except Exception as e:
            self.logger.error(f"Error performing reset: {e}")
            self.status_text.value = f"Error resetting configuration: {str(e)}"
            self.status_text.color = AppTheme.ERROR_COLOR
            await self.status_text.update_async()
    
    def _update_form_values(self):
        """Update form control values from config data"""
        self.api_key_field.value = self.config_data.get('exlog_api', {}).get('api_key', '')
        self.endpoint_field.value = self.config_data.get('exlog_api', {}).get('endpoint', '')
        self.batch_size_field.value = str(self.config_data.get('exlog_api', {}).get('batch_size', 10))
        self.timeout_field.value = str(self.config_data.get('exlog_api', {}).get('timeout', 30))
        self.log_level_dropdown.value = self.config_data.get('general', {}).get('log_level', 'INFO')
        self.processing_interval_field.value = str(self.config_data.get('general', {}).get('processing_interval', 5))
    
    def build(self):
        """Build the configuration interface"""
        return ft.Container(
            content=ft.Column(
                controls=[
                    # Header
                    ft.Container(
                        content=ft.Text(
                            "Configuration",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=AppTheme.ON_SURFACE
                        ),
                        padding=20
                    ),
                    
                    # Configuration form
                    ft.Container(
                        content=ft.Column(
                            controls=[
                                # API Configuration section
                                ft.Container(
                                    content=ft.Column(
                                        controls=[
                                            ft.Text(
                                                "API Configuration",
                                                size=18,
                                                weight=ft.FontWeight.W_600,
                                                color=AppTheme.ON_SURFACE
                                            ),
                                            ft.Row(
                                                controls=[
                                                    ft.Column(
                                                        controls=[
                                                            self.api_key_field,
                                                            self.endpoint_field
                                                        ],
                                                        spacing=16
                                                    )
                                                ]
                                            ),
                                            ft.Row(
                                                controls=[
                                                    self.batch_size_field,
                                                    self.timeout_field
                                                ],
                                                spacing=16
                                            )
                                        ],
                                        spacing=16
                                    ),
                                    **AppTheme.get_card_style()
                                ),
                                
                                # General Configuration section
                                ft.Container(
                                    content=ft.Column(
                                        controls=[
                                            ft.Text(
                                                "General Settings",
                                                size=18,
                                                weight=ft.FontWeight.W_600,
                                                color=AppTheme.ON_SURFACE
                                            ),
                                            ft.Row(
                                                controls=[
                                                    self.log_level_dropdown,
                                                    self.processing_interval_field
                                                ],
                                                spacing=16
                                            )
                                        ],
                                        spacing=16
                                    ),
                                    **AppTheme.get_card_style()
                                ),
                                
                                # Action buttons
                                ft.Container(
                                    content=ft.Column(
                                        controls=[
                                            ft.Row(
                                                controls=[
                                                    ft.ElevatedButton(
                                                        "Save Configuration",
                                                        icon=ft.icons.SAVE,
                                                        on_click=self._save_configuration,
                                                        **AppTheme.get_button_style("primary")
                                                    ),
                                                    ft.ElevatedButton(
                                                        "Test Connection",
                                                        icon=ft.icons.WIFI_PROTECTED_SETUP,
                                                        on_click=self._test_connection,
                                                        **AppTheme.get_button_style("secondary")
                                                    ),
                                                    ft.ElevatedButton(
                                                        "Reset to Defaults",
                                                        icon=ft.icons.RESTORE,
                                                        on_click=self._reset_to_defaults,
                                                        **AppTheme.get_button_style("outline")
                                                    )
                                                ],
                                                spacing=16
                                            ),
                                            self.status_text
                                        ],
                                        spacing=16
                                    ),
                                    **AppTheme.get_card_style()
                                )
                            ],
                            spacing=20
                        ),
                        padding=20,
                        expand=True
                    )
                ],
                spacing=0,
                expand=True,
                scroll=ft.ScrollMode.AUTO
            ),
            expand=True,
            bgcolor=AppTheme.BACKGROUND_COLOR
        )
