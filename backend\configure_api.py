#!/usr/bin/env python3
"""
ExLog API Configuration Helper

This script helps configure the ExLog API settings in the agent configuration.
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config.config_manager import Config<PERSON>ana<PERSON>


def configure_api():
    """Interactive configuration of ExLog API settings."""
    print("ExLog API Configuration Helper")
    print("=" * 40)
    
    try:
        # Load current configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # Get current API configuration
        api_config = config.get('exlog_api', {})
        
        print("\nCurrent API Configuration:")
        print(f"  Enabled: {api_config.get('enabled', False)}")
        print(f"  Endpoint: {api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')}")
        print(f"  API Key: {'*' * len(api_config.get('api_key', '')) if api_config.get('api_key') else 'Not set'}")
        print(f"  Batch Size: {api_config.get('batch_size', 100)}")
        print(f"  Timeout: {api_config.get('timeout', 30)} seconds")
        print()
        
        # Ask for new settings
        print("Configure new settings (press Enter to keep current value):")
        
        # Enable/disable
        enabled_input = input(f"Enable API client? (y/n) [{api_config.get('enabled', True)}]: ").strip().lower()
        if enabled_input:
            enabled = enabled_input in ['y', 'yes', 'true', '1']
        else:
            enabled = api_config.get('enabled', True)
        
        # Endpoint
        endpoint_input = input(f"API Endpoint [{api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')}]: ").strip()
        if endpoint_input:
            endpoint = endpoint_input
        else:
            endpoint = api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        
        # API Key
        api_key_input = input(f"API Key [{'*' * len(api_config.get('api_key', '')) if api_config.get('api_key') else 'your-api-key-here'}]: ").strip()
        if api_key_input:
            api_key = api_key_input
        else:
            api_key = api_config.get('api_key', 'your-api-key-here')
        
        # Batch size
        batch_size_input = input(f"Batch Size [{api_config.get('batch_size', 100)}]: ").strip()
        if batch_size_input:
            try:
                batch_size = int(batch_size_input)
            except ValueError:
                print("Invalid batch size, using default")
                batch_size = api_config.get('batch_size', 100)
        else:
            batch_size = api_config.get('batch_size', 100)
        
        # Timeout
        timeout_input = input(f"Timeout (seconds) [{api_config.get('timeout', 30)}]: ").strip()
        if timeout_input:
            try:
                timeout = int(timeout_input)
            except ValueError:
                print("Invalid timeout, using default")
                timeout = api_config.get('timeout', 30)
        else:
            timeout = api_config.get('timeout', 30)
        
        # Update configuration
        config['exlog_api']['enabled'] = enabled
        config['exlog_api']['endpoint'] = endpoint
        config['exlog_api']['api_key'] = api_key
        config['exlog_api']['batch_size'] = batch_size
        config['exlog_api']['timeout'] = timeout
        
        # Save configuration
        config_manager.config = config
        config_manager.save_config()
        
        print("\n✅ Configuration updated successfully!")
        print("\nNew API Configuration:")
        print(f"  Enabled: {enabled}")
        print(f"  Endpoint: {endpoint}")
        print(f"  API Key: {'*' * len(api_key) if api_key else 'Not set'}")
        print(f"  Batch Size: {batch_size}")
        print(f"  Timeout: {timeout} seconds")
        
        print("\n📝 Note: Restart the logging agent for changes to take effect.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error configuring API: {e}")
        return False


def show_current_config():
    """Show current API configuration."""
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        api_config = config.get('exlog_api', {})
        
        print("Current ExLog API Configuration:")
        print("=" * 40)
        print(f"Enabled: {api_config.get('enabled', False)}")
        print(f"Endpoint: {api_config.get('endpoint', 'Not set')}")
        print(f"API Key: {'*' * len(api_config.get('api_key', '')) if api_config.get('api_key') else 'Not set'}")
        print(f"Batch Size: {api_config.get('batch_size', 100)}")
        print(f"Max Batch Wait Time: {api_config.get('max_batch_wait_time', 30)} seconds")
        print(f"Timeout: {api_config.get('timeout', 30)} seconds")
        print(f"Max Retries: {api_config.get('max_retries', 3)}")
        print(f"Retry Delay: {api_config.get('retry_delay', 5)} seconds")
        
        buffer_config = api_config.get('offline_buffer', {})
        print(f"\nOffline Buffer:")
        print(f"  Enabled: {buffer_config.get('enabled', True)}")
        print(f"  Max Size: {buffer_config.get('max_size', 10000)} logs")
        print(f"  Buffer File: {buffer_config.get('buffer_file', 'logs/api_buffer.json')}")
        print(f"  Retry Interval: {buffer_config.get('retry_interval', 60)} seconds")
        
        validation_config = api_config.get('validation', {})
        print(f"\nValidation:")
        print(f"  Fix Missing Fields: {validation_config.get('fix_missing_fields', True)}")
        print(f"  Default Source: {validation_config.get('default_source', 'System')}")
        print(f"  Default Source Type: {validation_config.get('default_source_type', 'event')}")
        print(f"  Default Log Level: {validation_config.get('default_log_level', 'info')}")
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")


def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == 'show':
        show_current_config()
    else:
        configure_api()


if __name__ == "__main__":
    main()
