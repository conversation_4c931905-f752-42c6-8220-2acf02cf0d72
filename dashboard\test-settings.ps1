# ExLog Settings Functionality Test Script
# This script tests the comprehensive Settings page implementation

Write-Host "=== ExLog Settings Functionality Test ===" -ForegroundColor Green
Write-Host "Testing comprehensive Settings page with all features" -ForegroundColor Yellow
Write-Host ""

# Configuration
$baseUrl = "http://localhost:5000/api/v1"
$frontendUrl = "http://localhost:3000"

# Test credentials
$testUser = @{
    username = "admin"
    password = "admin123"
    email = "<EMAIL>"
}

# Function to make API requests
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Uri,
        [hashtable]$Headers = @{},
        [object]$Body = $null
    )
    
    try {
        $params = @{
            Method = $Method
            Uri = $Uri
            Headers = $Headers
            ContentType = "application/json"
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
        }
        
        $response = Invoke-RestMethod @params
        return $response
    }
    catch {
        Write-Host "API Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response: $responseBody" -ForegroundColor Red
        }
        return $null
    }
}

# Test 1: Authentication
Write-Host "1. Testing Authentication..." -ForegroundColor Cyan
$loginResponse = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/auth/login" -Body $testUser

if ($loginResponse -and $loginResponse.token) {
    Write-Host "✓ Authentication successful" -ForegroundColor Green
    $authHeaders = @{ "Authorization" = "Bearer $($loginResponse.token)" }
} else {
    Write-Host "✗ Authentication failed" -ForegroundColor Red
    exit 1
}

# Test 2: Profile Management
Write-Host "`n2. Testing Profile Management..." -ForegroundColor Cyan

# Get profile
$profile = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/settings/profile" -Headers $authHeaders
if ($profile) {
    Write-Host "✓ Profile retrieval successful" -ForegroundColor Green
    Write-Host "  User: $($profile.data.profile.username) ($($profile.data.profile.email))" -ForegroundColor Gray
} else {
    Write-Host "✗ Profile retrieval failed" -ForegroundColor Red
}

# Update profile
$profileUpdate = @{
    firstName = "Test"
    lastName = "Administrator"
}
$updateResponse = Invoke-ApiRequest -Method "PUT" -Uri "$baseUrl/settings/profile" -Headers $authHeaders -Body $profileUpdate
if ($updateResponse -and $updateResponse.status -eq "success") {
    Write-Host "✓ Profile update successful" -ForegroundColor Green
} else {
    Write-Host "✗ Profile update failed" -ForegroundColor Red
}

# Test 3: Preferences Management
Write-Host "`n3. Testing Preferences Management..." -ForegroundColor Cyan

$preferencesUpdate = @{
    theme = "dark"
    language = "en"
    timezone = "America/New_York"
    dashboardRefreshInterval = 60
    notifications = @{
        email = $true
        inApp = $true
        alerts = @{
            critical = $true
            high = $true
            medium = $false
            low = $false
        }
    }
}

$prefResponse = Invoke-ApiRequest -Method "PUT" -Uri "$baseUrl/settings/preferences" -Headers $authHeaders -Body $preferencesUpdate
if ($prefResponse -and $prefResponse.status -eq "success") {
    Write-Host "✓ Preferences update successful" -ForegroundColor Green
} else {
    Write-Host "✗ Preferences update failed" -ForegroundColor Red
}

# Test 4: API Key Management
Write-Host "`n4. Testing API Key Management..." -ForegroundColor Cyan

# Get existing API keys
$apiKeys = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/settings/api-keys" -Headers $authHeaders
if ($apiKeys) {
    Write-Host "✓ API keys retrieval successful" -ForegroundColor Green
    Write-Host "  Current API keys: $($apiKeys.data.total)" -ForegroundColor Gray
} else {
    Write-Host "✗ API keys retrieval failed" -ForegroundColor Red
}

# Create new API key
$newApiKey = @{
    name = "Test API Key"
    description = "Test key for settings functionality"
    permissions = @("view_logs", "search_logs")
    expiresAt = (Get-Date).AddDays(30).ToString("yyyy-MM-dd")
}

$createKeyResponse = Invoke-ApiRequest -Method "POST" -Uri "$baseUrl/settings/api-keys" -Headers $authHeaders -Body $newApiKey
if ($createKeyResponse -and $createKeyResponse.status -eq "success") {
    Write-Host "✓ API key creation successful" -ForegroundColor Green
    $createdKeyId = $createKeyResponse.data.apiKey._id
    Write-Host "  Created key: $($createKeyResponse.data.apiKey.name)" -ForegroundColor Gray
} else {
    Write-Host "✗ API key creation failed" -ForegroundColor Red
}

# Update API key (if created successfully)
if ($createdKeyId) {
    $updateKey = @{
        description = "Updated test key description"
        isActive = $true
    }
    
    $updateKeyResponse = Invoke-ApiRequest -Method "PUT" -Uri "$baseUrl/settings/api-keys/$createdKeyId" -Headers $authHeaders -Body $updateKey
    if ($updateKeyResponse -and $updateKeyResponse.status -eq "success") {
        Write-Host "✓ API key update successful" -ForegroundColor Green
    } else {
        Write-Host "✗ API key update failed" -ForegroundColor Red
    }
}

# Test 5: Session Management
Write-Host "`n5. Testing Session Management..." -ForegroundColor Cyan

$sessions = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/settings/sessions" -Headers $authHeaders
if ($sessions) {
    Write-Host "✓ Sessions retrieval successful" -ForegroundColor Green
    Write-Host "  Active sessions: $($sessions.data.total)" -ForegroundColor Gray
} else {
    Write-Host "✗ Sessions retrieval failed" -ForegroundColor Red
}

# Test 6: Login History
Write-Host "`n6. Testing Login History..." -ForegroundColor Cyan

$loginHistory = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/settings/login-history?page=1&limit=5" -Headers $authHeaders
if ($loginHistory) {
    Write-Host "✓ Login history retrieval successful" -ForegroundColor Green
    Write-Host "  History entries: $($loginHistory.data.pagination.totalItems)" -ForegroundColor Gray
} else {
    Write-Host "✗ Login history retrieval failed" -ForegroundColor Red
}

# Test 7: System Settings (Admin only)
Write-Host "`n7. Testing System Settings..." -ForegroundColor Cyan

$systemSettings = Invoke-ApiRequest -Method "GET" -Uri "$baseUrl/settings/system" -Headers $authHeaders
if ($systemSettings) {
    Write-Host "✓ System settings retrieval successful" -ForegroundColor Green
    Write-Host "  Settings version: $($systemSettings.data.settings.settingsVersion)" -ForegroundColor Gray
} else {
    Write-Host "✗ System settings retrieval failed (may require admin role)" -ForegroundColor Yellow
}

# Test 8: Log Retention Settings
Write-Host "`n8. Testing Log Retention Settings..." -ForegroundColor Cyan

$retentionUpdate = @{
    defaultRetentionDays = 120
    autoArchiveEnabled = $true
    archiveCompressionEnabled = $true
}

$retentionResponse = Invoke-ApiRequest -Method "PUT" -Uri "$baseUrl/settings/system/log-retention" -Headers $authHeaders -Body $retentionUpdate
if ($retentionResponse -and $retentionResponse.status -eq "success") {
    Write-Host "✓ Log retention settings update successful" -ForegroundColor Green
} else {
    Write-Host "✗ Log retention settings update failed (may require admin role)" -ForegroundColor Yellow
}

# Test 9: Frontend Accessibility
Write-Host "`n9. Testing Frontend Accessibility..." -ForegroundColor Cyan

try {
    $frontendResponse = Invoke-WebRequest -Uri "$frontendUrl/settings" -Method GET -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "✓ Settings page accessible" -ForegroundColor Green
    } else {
        Write-Host "✗ Settings page not accessible" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Frontend not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 10: Password Change
Write-Host "`n10. Testing Password Change..." -ForegroundColor Cyan

$passwordChange = @{
    currentPassword = "admin123"
    newPassword = "newAdmin123!"
    confirmPassword = "newAdmin123!"
}

$passwordResponse = Invoke-ApiRequest -Method "PUT" -Uri "$baseUrl/settings/password" -Headers $authHeaders -Body $passwordChange
if ($passwordResponse -and $passwordResponse.status -eq "success") {
    Write-Host "✓ Password change successful" -ForegroundColor Green
    
    # Change it back
    $passwordChangeBack = @{
        currentPassword = "newAdmin123!"
        newPassword = "admin123"
        confirmPassword = "admin123"
    }
    
    $passwordBackResponse = Invoke-ApiRequest -Method "PUT" -Uri "$baseUrl/settings/password" -Headers $authHeaders -Body $passwordChangeBack
    if ($passwordBackResponse -and $passwordBackResponse.status -eq "success") {
        Write-Host "✓ Password restored successfully" -ForegroundColor Green
    }
} else {
    Write-Host "✗ Password change failed" -ForegroundColor Red
}

# Cleanup: Delete test API key
if ($createdKeyId) {
    Write-Host "`n11. Cleaning up test data..." -ForegroundColor Cyan
    $deleteResponse = Invoke-ApiRequest -Method "DELETE" -Uri "$baseUrl/settings/api-keys/$createdKeyId" -Headers $authHeaders
    if ($deleteResponse -and $deleteResponse.status -eq "success") {
        Write-Host "✓ Test API key deleted successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Failed to delete test API key" -ForegroundColor Red
    }
}

# Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "Settings functionality testing completed!" -ForegroundColor Yellow
Write-Host ""
Write-Host "Key Features Tested:" -ForegroundColor Cyan
Write-Host "• Profile Management (view, update)" -ForegroundColor White
Write-Host "• User Preferences (theme, notifications, etc.)" -ForegroundColor White
Write-Host "• API Key Management (CRUD operations)" -ForegroundColor White
Write-Host "• Session Management (view active sessions)" -ForegroundColor White
Write-Host "• Login History (security audit trail)" -ForegroundColor White
Write-Host "• System Settings (admin configuration)" -ForegroundColor White
Write-Host "• Password Change (security)" -ForegroundColor White
Write-Host "• Frontend Accessibility" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Open http://localhost:3000/settings to test the UI" -ForegroundColor White
Write-Host "2. Test all tabs and functionality manually" -ForegroundColor White
Write-Host "3. Verify role-based access control" -ForegroundColor White
Write-Host "4. Test with different user roles" -ForegroundColor White
Write-Host ""
