#!/usr/bin/env python3
"""
Debug script for Linux Log Agent Service

This script helps diagnose issues with the systemd service by running
the same initialization process with detailed debugging output.
"""

import os
import sys
import logging
import traceback
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

def setup_debug_logging():
    """Set up detailed debug logging."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('/tmp/debug_service.log')
        ]
    )
    return logging.getLogger('DebugService')

def check_environment(logger):
    """Check the environment for common issues."""
    logger.info("=== Environment Check ===")
    
    # Check user
    import pwd
    current_user = pwd.getpwuid(os.getuid()).pw_name
    logger.info(f"Running as user: {current_user}")
    
    # Check working directory
    logger.info(f"Working directory: {os.getcwd()}")
    
    # Check Python path
    logger.info(f"Python executable: {sys.executable}")
    logger.info(f"Python path: {sys.path}")
    
    # Check file permissions
    config_path = "/opt/linux-agent/config/default_config.yaml"
    logger.info(f"Config file exists: {os.path.exists(config_path)}")
    if os.path.exists(config_path):
        logger.info(f"Config file readable: {os.access(config_path, os.R_OK)}")
    
    # Check log directory
    log_dir = "/var/log/linux-log-agent"
    logger.info(f"Log directory exists: {os.path.exists(log_dir)}")
    if os.path.exists(log_dir):
        logger.info(f"Log directory writable: {os.access(log_dir, os.W_OK)}")
    
    # Check system logs
    system_logs = ['/var/log/syslog', '/var/log/auth.log', '/var/log/kern.log', '/var/log/messages']
    for log_file in system_logs:
        if os.path.exists(log_file):
            readable = os.access(log_file, os.R_OK)
            logger.info(f"System log {log_file}: readable={readable}")

def test_imports(logger):
    """Test importing required modules."""
    logger.info("=== Import Test ===")
    
    try:
        from logging_agent.agent import LinuxLoggingAgent
        logger.info("✓ Successfully imported LinuxLoggingAgent")
    except Exception as e:
        logger.error(f"✗ Failed to import LinuxLoggingAgent: {e}")
        logger.error(traceback.format_exc())
        return False
    
    try:
        from config.config_manager import ConfigManager
        logger.info("✓ Successfully imported ConfigManager")
    except Exception as e:
        logger.error(f"✗ Failed to import ConfigManager: {e}")
        logger.error(traceback.format_exc())
        return False
    
    return True

def test_config_loading(logger, config_path):
    """Test loading configuration."""
    logger.info("=== Configuration Test ===")
    
    try:
        from config.config_manager import ConfigManager
        config_manager = ConfigManager(config_path)
        config = config_manager.load_config()
        logger.info("✓ Successfully loaded configuration")
        logger.debug(f"Config keys: {list(config.keys())}")
        return True
    except Exception as e:
        logger.error(f"✗ Failed to load configuration: {e}")
        logger.error(traceback.format_exc())
        return False

def test_agent_initialization(logger, config_path):
    """Test agent initialization."""
    logger.info("=== Agent Initialization Test ===")
    
    try:
        from logging_agent.agent import LinuxLoggingAgent
        agent = LinuxLoggingAgent(
            config_path=config_path,
            enable_signals=False
        )
        logger.info("✓ Successfully initialized agent")
        return agent
    except Exception as e:
        logger.error(f"✗ Failed to initialize agent: {e}")
        logger.error(traceback.format_exc())
        return None

def test_agent_start(logger, agent):
    """Test agent start."""
    logger.info("=== Agent Start Test ===")
    
    try:
        if agent.start():
            logger.info("✓ Successfully started agent")
            return True
        else:
            logger.error("✗ Agent start() returned False")
            return False
    except Exception as e:
        logger.error(f"✗ Failed to start agent: {e}")
        logger.error(traceback.format_exc())
        return False

def main():
    """Main debug function."""
    logger = setup_debug_logging()
    logger.info("Starting Linux Log Agent Service Debug")
    
    config_path = sys.argv[1] if len(sys.argv) > 1 else "/opt/linux-agent/config/default_config.yaml"
    logger.info(f"Using config path: {config_path}")
    
    # Run all tests
    check_environment(logger)
    
    if not test_imports(logger):
        logger.error("Import test failed - cannot proceed")
        return 1
    
    if not test_config_loading(logger, config_path):
        logger.error("Configuration test failed - cannot proceed")
        return 1
    
    agent = test_agent_initialization(logger, config_path)
    if not agent:
        logger.error("Agent initialization failed - cannot proceed")
        return 1
    
    if not test_agent_start(logger, agent):
        logger.error("Agent start failed")
        return 1
    
    logger.info("All tests passed! Agent should work in systemd service.")
    
    # Stop the agent
    try:
        agent.stop()
        logger.info("Agent stopped successfully")
    except Exception as e:
        logger.error(f"Error stopping agent: {e}")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
