#!/bin/bash
"""
Test Script for Linux Log Agent Service

This script validates that the systemd service fixes work correctly.
"""

SERVICE_NAME="linux-agent"
LOG_DIR="/var/log/linux-log-agent"
CONFIG_PATH="/opt/linux-agent/config/default_config.yaml"
INSTALL_DIR="/opt/linux-agent"

echo "=== Linux Log Agent Service Test ==="
echo

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "This script should be run as root"
   exit 1
fi

echo "1. Pre-test validation:"

# Check if service file exists
if [ -f "/etc/systemd/system/${SERVICE_NAME}.service" ]; then
    echo "✓ Service file exists"
else
    echo "✗ Service file not found"
    exit 1
fi

# Check if agent directory exists
if [ -d "$INSTALL_DIR" ]; then
    echo "✓ Agent directory exists"
else
    echo "✗ Agent directory not found"
    exit 1
fi

# Check if config file exists
if [ -f "$CONFIG_PATH" ]; then
    echo "✓ Config file exists"
else
    echo "✗ Config file not found"
    exit 1
fi

echo

echo "2. Running debug script:"
if [ -f "$INSTALL_DIR/scripts/debug_service.py" ]; then
    echo "Running debug script as linux-log-agent user..."
    sudo -u linux-log-agent python3 $INSTALL_DIR/scripts/debug_service.py $CONFIG_PATH
    DEBUG_EXIT_CODE=$?
    if [ $DEBUG_EXIT_CODE -eq 0 ]; then
        echo "✓ Debug script passed"
    else
        echo "✗ Debug script failed with exit code $DEBUG_EXIT_CODE"
        echo "Check the debug output above for issues"
        exit 1
    fi
else
    echo "✗ Debug script not found"
    exit 1
fi

echo

echo "3. Testing systemd service:"

# Stop service if running
systemctl stop $SERVICE_NAME 2>/dev/null || true

# Start the service
echo "Starting service..."
systemctl start $SERVICE_NAME

# Wait a moment for startup
sleep 5

# Check service status
if systemctl is-active --quiet $SERVICE_NAME; then
    echo "✓ Service is active"
    
    # Check if it stays running for 30 seconds
    echo "Testing service stability (30 seconds)..."
    sleep 30
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        echo "✓ Service remained stable for 30 seconds"
        
        # Check logs for errors
        echo "Checking logs for errors..."
        ERROR_COUNT=$(journalctl -u $SERVICE_NAME --since "1 minute ago" | grep -i error | wc -l)
        if [ $ERROR_COUNT -eq 0 ]; then
            echo "✓ No errors found in logs"
        else
            echo "⚠ Found $ERROR_COUNT error(s) in logs:"
            journalctl -u $SERVICE_NAME --since "1 minute ago" | grep -i error
        fi
        
        # Check if log files are being created
        if [ -f "$LOG_DIR/service.log" ]; then
            echo "✓ Service log file created"
            LOG_SIZE=$(stat -c%s "$LOG_DIR/service.log")
            if [ $LOG_SIZE -gt 0 ]; then
                echo "✓ Service log file has content ($LOG_SIZE bytes)"
            else
                echo "⚠ Service log file is empty"
            fi
        else
            echo "⚠ Service log file not found"
        fi
        
    else
        echo "✗ Service stopped running after 30 seconds"
        echo "Service status:"
        systemctl status $SERVICE_NAME --no-pager -l
        echo
        echo "Recent logs:"
        journalctl -u $SERVICE_NAME --since "1 minute ago" --no-pager
        exit 1
    fi
    
else
    echo "✗ Service failed to start"
    echo "Service status:"
    systemctl status $SERVICE_NAME --no-pager -l
    echo
    echo "Recent logs:"
    journalctl -u $SERVICE_NAME --since "1 minute ago" --no-pager
    exit 1
fi

echo

echo "4. Testing service restart:"
echo "Restarting service..."
systemctl restart $SERVICE_NAME

sleep 5

if systemctl is-active --quiet $SERVICE_NAME; then
    echo "✓ Service restarted successfully"
else
    echo "✗ Service failed to restart"
    systemctl status $SERVICE_NAME --no-pager -l
    exit 1
fi

echo

echo "5. Testing service reload (SIGHUP):"
echo "Sending reload signal..."
systemctl reload $SERVICE_NAME

sleep 2

if systemctl is-active --quiet $SERVICE_NAME; then
    echo "✓ Service handled reload signal"
else
    echo "✗ Service failed after reload signal"
    systemctl status $SERVICE_NAME --no-pager -l
    exit 1
fi

echo

echo "6. Final status check:"
systemctl status $SERVICE_NAME --no-pager -l

echo

echo "=== Test Results ==="
echo "✓ All tests passed!"
echo "The Linux Log Agent service is working correctly."
echo
echo "Service management:"
echo "  Status:  systemctl status $SERVICE_NAME"
echo "  Logs:    journalctl -u $SERVICE_NAME -f"
echo "  Stop:    systemctl stop $SERVICE_NAME"
echo "  Start:   systemctl start $SERVICE_NAME"
echo
echo "Log files:"
echo "  Service: $LOG_DIR/service.log"
echo "  Errors:  $LOG_DIR/service_errors.log"
echo "  Agent:   $LOG_DIR/agent.log"
