import { configureStore } from '@reduxjs/toolkit'
import authSlice from './slices/authSlice'
import logsSlice from './slices/logsSlice'
import alertsSlice from './slices/alertsSlice'
import agentsSlice from './slices/agentsSlice'
import usersSlice from './slices/usersSlice'
import dashboardSlice from './slices/dashboardSlice'
import uiSlice from './slices/uiSlice'
import settingsSlice from './slices/settingsSlice'
import reportingSlice from './slices/reportingSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    logs: logsSlice,
    alerts: alertsSlice,
    agents: agentsSlice,
    users: usersSlice,
    dashboard: dashboardSlice,
    ui: uiSlice,
    settings: settingsSlice,
    reporting: reportingSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

export default store
