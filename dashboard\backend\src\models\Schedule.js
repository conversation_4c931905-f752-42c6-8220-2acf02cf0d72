const mongoose = require('mongoose');

const scheduleSchema = new mongoose.Schema({
  scheduleId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  reportId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Report',
    required: true,
    index: true,
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  schedule: {
    type: {
      type: String,
      enum: ['once', 'daily', 'weekly', 'monthly', 'custom'],
      required: true,
    },
    cronExpression: {
      type: String,
      required: function() {
        return this.schedule.type === 'custom';
      },
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      default: null,
    },
    timeZone: {
      type: String,
      default: 'UTC',
    },
    dayOfWeek: {
      type: Number,
      min: 0,
      max: 6,
      default: null, // For weekly schedules
    },
    dayOfMonth: {
      type: Number,
      min: 1,
      max: 31,
      default: null, // For monthly schedules
    },
    hour: {
      type: Number,
      min: 0,
      max: 23,
      default: 0,
    },
    minute: {
      type: Number,
      min: 0,
      max: 59,
      default: 0,
    },
  },
  parameters: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },
  delivery: {
    method: {
      type: String,
      enum: ['email', 'filesystem', 'sftp', 's3', 'webhook'],
      required: true,
    },
    config: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
    },
    format: {
      type: String,
      enum: ['pdf', 'csv', 'html', 'json'],
      default: 'pdf',
    },
    recipients: [{
      type: String,
      trim: true,
    }],
  },
  status: {
    type: String,
    enum: ['active', 'paused', 'disabled', 'error'],
    default: 'active',
    index: true,
  },
  lastExecution: {
    executedAt: {
      type: Date,
      default: null,
    },
    status: {
      type: String,
      enum: ['success', 'failed', 'running'],
      default: null,
    },
    error: {
      type: String,
      default: null,
    },
    executionTime: {
      type: Number,
      default: 0,
    },
    outputSize: {
      type: Number,
      default: 0,
    },
  },
  nextExecution: {
    type: Date,
    default: null,
    index: true,
  },
  statistics: {
    totalExecutions: {
      type: Number,
      default: 0,
    },
    successfulExecutions: {
      type: Number,
      default: 0,
    },
    failedExecutions: {
      type: Number,
      default: 0,
    },
    avgExecutionTime: {
      type: Number,
      default: 0,
    },
    lastSuccessfulExecution: {
      type: Date,
      default: null,
    },
  },
  executionHistory: [{
    executedAt: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      enum: ['success', 'failed'],
      required: true,
    },
    error: String,
    executionTime: Number,
    outputSize: Number,
    deliveryStatus: {
      type: String,
      enum: ['delivered', 'failed', 'pending'],
      default: 'pending',
    },
  }],
}, {
  timestamps: true,
});

// Indexes
scheduleSchema.index({ owner: 1, status: 1 });
scheduleSchema.index({ reportId: 1, status: 1 });
scheduleSchema.index({ nextExecution: 1, status: 1 });
scheduleSchema.index({ 'schedule.type': 1, status: 1 });

// Pre-save middleware to calculate next execution
scheduleSchema.pre('save', function(next) {
  if (this.isModified('schedule') || this.isNew) {
    this.nextExecution = this.calculateNextExecution();
  }
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
  }
  next();
});

// Method to calculate next execution time
scheduleSchema.methods.calculateNextExecution = function() {
  const now = new Date();
  const startDate = new Date(this.schedule.startDate);
  
  if (startDate > now) {
    return startDate;
  }
  
  if (this.schedule.endDate && new Date(this.schedule.endDate) < now) {
    return null; // Schedule has ended
  }
  
  const next = new Date(now);
  next.setHours(this.schedule.hour, this.schedule.minute, 0, 0);
  
  switch (this.schedule.type) {
    case 'once':
      return startDate > now ? startDate : null;
    
    case 'daily':
      if (next <= now) {
        next.setDate(next.getDate() + 1);
      }
      return next;
    
    case 'weekly':
      const targetDay = this.schedule.dayOfWeek || 0;
      const currentDay = next.getDay();
      const daysUntilTarget = (targetDay - currentDay + 7) % 7;
      
      if (daysUntilTarget === 0 && next <= now) {
        next.setDate(next.getDate() + 7);
      } else {
        next.setDate(next.getDate() + daysUntilTarget);
      }
      return next;
    
    case 'monthly':
      const targetDate = this.schedule.dayOfMonth || 1;
      next.setDate(targetDate);
      
      if (next <= now) {
        next.setMonth(next.getMonth() + 1);
        next.setDate(targetDate);
      }
      return next;
    
    case 'custom':
      // For custom cron expressions, we'll need a cron parser
      // For now, return null and handle in the scheduler service
      return null;
    
    default:
      return null;
  }
};

// Method to check if schedule should execute
scheduleSchema.methods.shouldExecute = function() {
  if (this.status !== 'active') return false;
  if (!this.nextExecution) return false;
  if (this.schedule.endDate && new Date(this.schedule.endDate) < new Date()) return false;
  
  return new Date() >= this.nextExecution;
};

module.exports = mongoose.model('Schedule', scheduleSchema);
