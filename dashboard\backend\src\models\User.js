const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,})+$/, 'Please enter a valid email'],
  },
  password: {
    type: String,
    required: true,
    minlength: 8,
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  role: {
    type: String,
    enum: ['admin', 'security_analyst', 'compliance_officer', 'executive'],
    default: 'security_analyst',
  },
  permissions: [{
    type: String,
    enum: [
      // Navigation permissions
      'nav_dashboard',
      'nav_logs',
      'nav_alerts',
      'nav_agents',
      'nav_reports',
      'nav_users',
      'nav_settings',
      // Feature permissions
      'view_logs',
      'search_logs',
      'export_logs',
      'view_alerts',
      'manage_alerts',
      'view_agents',
      'manage_agents',
      'view_users',
      'manage_users',
      'view_reports',
      'generate_reports',
      'view_dashboards',
      'manage_dashboards',
      'view_settings',
      'manage_settings',
      'system_admin',
    ],
  }],
  mfaEnabled: {
    type: Boolean,
    default: false,
  },
  mfaSecret: {
    type: String,
    default: null,
  },
  apiKeys: [{
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    key: {
      type: String,
      required: true,
      unique: true,
      sparse: true, // Allow multiple null values
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    expiresAt: {
      type: Date,
      default: null, // null means no expiration
    },
    lastUsed: Date,
    usageCount: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    permissions: [{
      type: String,
      enum: [
        'view_logs',
        'search_logs',
        'export_logs',
        'view_alerts',
        'manage_alerts',
        'view_agents',
        'manage_agents',
        'view_reports',
        'generate_reports',
      ],
    }],
    ipWhitelist: [{
      type: String,
      validate: {
        validator: function(ip) {
          // Basic IP validation (IPv4 and IPv6)
          const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
          const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
          return ipv4Regex.test(ip) || ipv6Regex.test(ip) || ip === 'localhost';
        },
        message: 'Invalid IP address format',
      },
    }],
  }],
  sessions: [{
    sessionId: {
      type: String,
      required: true,
    },
    deviceInfo: {
      userAgent: String,
      ip: String,
      location: String,
      device: String,
      browser: String,
      os: String,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    lastActivity: {
      type: Date,
      default: Date.now,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    rememberMe: {
      type: Boolean,
      default: false,
    },
  }],
  loginHistory: [{
    timestamp: {
      type: Date,
      default: Date.now,
    },
    ip: String,
    userAgent: String,
    location: String,
    success: {
      type: Boolean,
      required: true,
    },
    failureReason: String,
    deviceInfo: {
      device: String,
      browser: String,
      os: String,
    },
  }],
  lastLogin: {
    type: Date,
    default: null,
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'locked'],
    default: 'active',
  },
  loginAttempts: {
    type: Number,
    default: 0,
  },
  lockUntil: Date,
  passwordResetToken: String,
  passwordResetExpires: Date,
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'light',
    },
    timezone: {
      type: String,
      default: 'UTC',
    },
    language: {
      type: String,
      enum: ['en', 'es', 'fr', 'de', 'ja', 'zh'],
      default: 'en',
    },
    dateFormat: {
      type: String,
      enum: ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'DD-MM-YYYY'],
      default: 'MM/DD/YYYY',
    },
    timeFormat: {
      type: String,
      enum: ['12h', '24h'],
      default: '12h',
    },
    dashboardRefreshInterval: {
      type: Number,
      min: 10,
      max: 300,
      default: 30, // seconds
    },
    logsPerPage: {
      type: Number,
      min: 10,
      max: 200,
      default: 50,
    },
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      inApp: {
        type: Boolean,
        default: true,
      },
      alerts: {
        critical: {
          type: Boolean,
          default: true,
        },
        high: {
          type: Boolean,
          default: true,
        },
        medium: {
          type: Boolean,
          default: false,
        },
        low: {
          type: Boolean,
          default: false,
        },
      },
      digest: {
        enabled: {
          type: Boolean,
          default: false,
        },
        frequency: {
          type: String,
          enum: ['daily', 'weekly', 'monthly'],
          default: 'weekly',
        },
        time: {
          type: String,
          default: '09:00',
        },
      },
    },
    dashboard: {
      defaultView: {
        type: String,
        enum: ['overview', 'logs', 'alerts', 'agents'],
        default: 'overview',
      },
      autoRefresh: {
        type: Boolean,
        default: true,
      },
      compactMode: {
        type: Boolean,
        default: false,
      },
    },
    security: {
      sessionTimeout: {
        type: Number,
        min: 15,
        max: 480,
        default: 60, // minutes
      },
      requireMfaForSensitiveActions: {
        type: Boolean,
        default: false,
      },
      loginNotifications: {
        type: Boolean,
        default: true,
      },
      allowMultipleSessions: {
        type: Boolean,
        default: true,
      },
    },
  },
  securitySettings: {
    passwordLastChanged: {
      type: Date,
      default: Date.now,
    },
    passwordChangeRequired: {
      type: Boolean,
      default: false,
    },
    accountLockoutEnabled: {
      type: Boolean,
      default: true,
    },
    maxLoginAttempts: {
      type: Number,
      min: 3,
      max: 10,
      default: 5,
    },
    lockoutDuration: {
      type: Number,
      min: 5,
      max: 60,
      default: 15, // minutes
    },
    ipWhitelist: [{
      ip: String,
      description: String,
      createdAt: {
        type: Date,
        default: Date.now,
      },
    }],
    trustedDevices: [{
      deviceId: String,
      deviceName: String,
      lastUsed: Date,
      createdAt: {
        type: Date,
        default: Date.now,
      },
    }],
  },
}, {
  timestamps: true,
});

// Indexes
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });

// Virtual for account locked
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    });
  }

  const updates = { $inc: { loginAttempts: 1 } };

  // Lock account after 5 failed attempts for 1 minute
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 1 * 60 * 1000 }; // 1 minute
  }

  return this.updateOne(updates);
};

// Method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
  });
};

// Method to get user permissions based on role
userSchema.methods.getEffectivePermissions = function() {
  const rolePermissions = {
    admin: [
      // Navigation permissions
      'nav_dashboard', 'nav_logs', 'nav_alerts', 'nav_agents', 'nav_reports', 'nav_users', 'nav_settings',
      // Feature permissions
      'view_logs', 'search_logs', 'export_logs',
      'view_alerts', 'manage_alerts',
      'view_agents', 'manage_agents',
      'view_users', 'manage_users',
      'view_reports', 'generate_reports',
      'view_dashboards', 'manage_dashboards',
      'view_settings', 'manage_settings',
      'system_admin',
    ],
    security_analyst: [
      // Navigation permissions
      'nav_dashboard', 'nav_logs', 'nav_alerts', 'nav_agents', 'nav_reports',
      // Feature permissions
      'view_logs', 'search_logs', 'export_logs',
      'view_alerts', 'manage_alerts',
      'view_agents', 'manage_agents',
      'view_reports', 'generate_reports',
      'view_dashboards', 'manage_dashboards',
    ],
    compliance_officer: [
      // Navigation permissions
      'nav_dashboard', 'nav_logs', 'nav_alerts', 'nav_reports',
      // Feature permissions
      'view_logs', 'search_logs', 'export_logs',
      'view_alerts',
      'view_reports', 'generate_reports',
      'view_dashboards',
    ],
    executive: [
      // Navigation permissions
      'nav_dashboard', 'nav_reports',
      // Feature permissions
      'view_reports',
      'view_dashboards',
    ],
  };

  const rolePerms = rolePermissions[this.role] || [];
  return [...new Set([...rolePerms, ...this.permissions])];
};

module.exports = mongoose.model('User', userSchema);
