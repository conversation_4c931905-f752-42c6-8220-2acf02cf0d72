# Linux Log Collection Agent

A comprehensive Python-based cybersecurity agent for collecting and standardizing Linux logs. This system provides automated log collection from various Linux sources and converts them into a standardized JSON format suitable for SIEM systems and security analysis.

## Features

### Log Collection

- **System Logs**: /var/log/syslog, /var/log/messages, /var/log/kern.log
- **Authentication Logs**: /var/log/auth.log, /var/log/secure
- **Application Logs**: Application-specific log files and directories
- **Systemd Journal**: journalctl integration for systemd-based distributions
- **Real-time Monitoring**: File watching with inotify for real-time log collection
- **Multi-Distribution Support**: Ubuntu, CentOS, RHEL, Debian, Fedora, SUSE

### Integration

- **ExLog Dashboard API**: Direct integration with ExLog dashboard at http://localhost:5000/api/v1/logs
- **Batch Processing**: Configurable batch sizes (default: 100 logs per batch)
- **Retry Logic**: Automatic retry with exponential backoff and 30s timeout
- **Authentication**: X-API-Key header-based authentication
- **Offline Buffering**: Local buffering when API is unavailable

### Linux-Specific Features

- **Log Level Mapping**: Maps Linux syslog levels (emerg, alert, crit, err, warning, notice, info, debug) to ExLog format
- **Facility Handling**: Processes syslog facilities (kern, mail, daemon, auth, etc.)
- **Systemd Integration**: Extracts systemd unit names and service information
- **Permission Management**: Handles different permission requirements for system logs
- **Distribution Detection**: Automatically detects Linux distribution and adapts log paths

## Installation

### Prerequisites

- Python 3.7 or higher
- Root or sudo access (for system log access)
- systemd (for service installation)

### Quick Install

```bash
# Clone or download the linux-agent directory
cd linux-agent

# Run the installation script
sudo ./install/install.sh
```

### Manual Installation

```bash
# Install Python dependencies
pip3 install -r requirements.txt

# Copy configuration file
sudo cp config/default_config.yaml /etc/linux-log-agent/config.yaml

# Install systemd service
sudo cp install/systemd/linux-log-agent.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable linux-log-agent
```

## Configuration

Edit `/etc/linux-log-agent/config.yaml`:

```yaml
exlog_api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: "your-api-key-here"
  batch_size: 100
  timeout: 30

collection:
  syslog:
    enabled: true
    paths:
      - /var/log/syslog
      - /var/log/messages
  auth_logs:
    enabled: true
    paths:
      - /var/log/auth.log
      - /var/log/secure
  kernel_logs:
    enabled: true
    paths:
      - /var/log/kern.log
  journalctl:
    enabled: true
    units: []  # Empty for all units
```

## Usage

### Start as Service

```bash
sudo systemctl start linux-log-agent
sudo systemctl status linux-log-agent
```

### Run in Console Mode

```bash
sudo python3 main.py console
```

### Test Configuration

```bash
python3 test_api_client.py
```

## Supported Linux Distributions

- **Ubuntu** (16.04+)
- **Debian** (9+)
- **CentOS** (7+)
- **RHEL** (7+)
- **Fedora** (28+)
- **SUSE Linux Enterprise** (12+)
- **openSUSE** (15+)

## Log Format

The agent sends logs to the ExLog API in the following format:

```json
{
  "logs": [
    {
      "log_id": "unique-uuid",
      "timestamp": "2024-01-01T12:00:00Z",
      "source": "System|Auth|Kernel|Application|Journal",
      "source_type": "syslog|auth|kernel|journal|application",
      "host": "hostname",
      "log_level": "critical|error|warning|info|debug",
      "message": "Log message text",
      "raw_data": null,
      "additional_fields": {
        "facility": "kern",
        "priority": 6,
        "pid": 1234,
        "program": "systemd",
        "systemd_unit": "example.service"
      }
    }
  ]
}
```

## Security Considerations

- **File Permissions**: Agent requires read access to system log files
- **User Account**: Runs as dedicated `linux-log-agent` user with appropriate group memberships
- **Log Rotation**: Handles log rotation and file watching gracefully
- **Resource Limits**: Configurable CPU and memory limits

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure the agent runs with appropriate privileges
2. **API Connection Failed**: Check network connectivity and API endpoint
3. **Log Files Not Found**: Verify log file paths for your distribution
4. **Service Won't Start**: Check systemd logs with `journalctl -u linux-log-agent`

### Logs and Monitoring

- **Agent Logs**: `/var/log/linux-log-agent/agent.log`
- **Error Logs**: `/var/log/linux-log-agent/errors.log`
- **API Logs**: `/var/log/linux-log-agent/api.log`
- **Service Status**: `systemctl status linux-log-agent`

## Development

### Project Structure

```
linux-agent/
├── main.py                 # Entry point
├── config/                 # Configuration management
├── logging_agent/          # Core agent functionality
├── utils/                  # Utility modules
├── service/                # Service management
├── install/                # Installation scripts
└── tests/                  # Unit tests
```

### Contributing

1. Follow the same patterns as the Windows version for consistency
2. Test on multiple Linux distributions
3. Ensure proper error handling and logging
4. Update documentation for any new features

## License

Same license as the main Python Logging Agent project.
