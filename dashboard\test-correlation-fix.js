const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

async function testCorrelationFix() {
  try {
    console.log('Testing Correlation Engine Fix...');
    
    // Get current alert count
    const initialResponse = await axios.get(`${API_BASE}/alerts/statistics`, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    const initialCount = initialResponse.data.data.statistics.total;
    console.log('Initial alert count:', initialCount);
    
    // Test 1: Send a log that should NOT trigger the "Multiple Failed Login Attempts" rule
    console.log('\n1. Testing log that should NOT trigger login failure rule...');
    const nonTriggerLog = {
      timestamp: new Date().toISOString(),
      source: "Network",
      sourceType: "network",
      host: "DESKTOP-TEST",
      logLevel: "info", // NOT "error"
      message: "new_connection: ::1:50113 -> ::1:5000", // Does NOT contain login failure patterns
      logId: `test-${Date.now()}-1`,
      rawData: null,
      additionalFields: {
        connection_family: "AF_INET6",
        connection_type: "SOCK_STREAM",
        local_address: "::1",
        local_port: 50113,
        remote_address: "::1",
        remote_port: 5000,
        process_id: 32104,
        process_name: "python.exe",
        event_type: "new_connection"
      }
    };
    
    await axios.post(`${API_BASE}/logs`, { logs: [nonTriggerLog] }, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    
    // Wait a moment for processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if alert was created (it shouldn't be)
    const afterNonTriggerResponse = await axios.get(`${API_BASE}/alerts/statistics`, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    const afterNonTriggerCount = afterNonTriggerResponse.data.data.statistics.total;
    
    if (afterNonTriggerCount === initialCount) {
      console.log('✅ CORRECT: No alert triggered for network connection log');
    } else {
      console.log('❌ INCORRECT: Alert was triggered for network connection log');
    }
    
    // Test 2: Send a log that SHOULD trigger the "Multiple Failed Login Attempts" rule
    console.log('\n2. Testing log that SHOULD trigger login failure rule...');
    const triggerLog = {
      timestamp: new Date().toISOString(),
      source: "Security",
      sourceType: "security",
      host: "DESKTOP-TEST",
      logLevel: "error", // Matches the rule condition
      message: "Authentication failed for user admin from IP *************", // Contains "authentication failed"
      logId: `test-${Date.now()}-2`,
      rawData: null,
      additionalFields: {
        user: "admin",
        ip_address: "*************",
        event_type: "authentication_failure"
      }
    };
    
    await axios.post(`${API_BASE}/logs`, { logs: [triggerLog] }, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    
    // Wait a moment for processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if alert was created (it should be)
    const afterTriggerResponse = await axios.get(`${API_BASE}/alerts/statistics`, {
      headers: { 'X-API-Key': 'test-api-key-12345' }
    });
    const afterTriggerCount = afterTriggerResponse.data.data.statistics.total;
    
    if (afterTriggerCount > afterNonTriggerCount) {
      console.log('✅ CORRECT: Alert triggered for authentication failure log');
      
      // Get the latest alert to verify it's the right one
      const alertsResponse = await axios.get(`${API_BASE}/alerts?page=1&limit=1`, {
        headers: { 'X-API-Key': 'test-api-key-12345' }
      });
      
      if (alertsResponse.data.data.alerts.length > 0) {
        const latestAlert = alertsResponse.data.data.alerts[0];
        console.log('Latest alert:', {
          name: latestAlert.name,
          severity: latestAlert.severity,
          triggerMessage: latestAlert.triggerData?.log?.message
        });
      }
    } else {
      console.log('❌ INCORRECT: No alert triggered for authentication failure log');
    }
    
    console.log('\nFinal alert counts:');
    console.log('- Initial:', initialCount);
    console.log('- After non-trigger test:', afterNonTriggerCount);
    console.log('- After trigger test:', afterTriggerCount);
    
    console.log('\n✅ Correlation engine testing completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testCorrelationFix();
