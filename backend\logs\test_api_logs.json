{"log_id": "e00e6029-663b-4c37-acb6-4f6ab2f9008e", "timestamp": "2025-05-30T00:36:43", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "warning", "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.", "raw_data": null, "additional_fields": {"record_number": 71840, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["**************.in-addr.arpa.", "128", "02000000C0A80201000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "6004"], "data": null, "event_id": 1014, "event_category": 1014, "event_type": 2, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "40054361-5cc6-43a4-bda4-f616c0063d27", "timestamp": "2025-05-30T00:35:52", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The time provider 'VMICTimeProvider' has indicated that the current hardware and operating environment is not supported and has stopped. This behavior is expected for VMICTimeProvider on non-HyperV-guest environments. This may be the expected behavior for the current provider in the current operating environment as well.", "raw_data": null, "additional_fields": {"record_number": 71839, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["VMICTimeProvider"], "data": null, "event_id": 158, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "b06dcac1-816c-42e9-8be2-79da6e736533", "timestamp": "2025-05-30T00:35:45", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The Group Policy settings for the computer were processed successfully. New settings from 1 Group Policy objects were detected and applied.", "raw_data": null, "additional_fields": {"record_number": 71838, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["1", "4348", "0", "47", "", "1"], "data": null, "event_id": 1502, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "c839825c-93aa-4dc9-82e7-6ea4ce45f44f", "timestamp": "2025-05-30T00:35:13", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from demand start to auto start.", "raw_data": null, "additional_fields": {"record_number": 71837, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "demand start", "auto start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "63430684-8372-49cd-b76e-73dd0c7e28f6", "timestamp": "2025-05-30T00:32:39", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from auto start to demand start.", "raw_data": null, "additional_fields": {"record_number": 71836, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "auto start", "demand start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "5ecbc73d-c2af-49b2-843e-fe57fb240af9", "timestamp": "2025-05-30T00:30:20", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from demand start to auto start.", "raw_data": null, "additional_fields": {"record_number": 71835, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "demand start", "auto start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "328cc5d5-1bb8-4815-af16-047ab76bea73", "timestamp": "2025-05-30T00:30:18", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from auto start to demand start.", "raw_data": null, "additional_fields": {"record_number": 71834, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "auto start", "demand start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.013313", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "e44f7e7b-86d7-4df4-9e7a-ede91b522a0f", "timestamp": "2025-05-30T00:28:14", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from demand start to auto start.", "raw_data": null, "additional_fields": {"record_number": 71833, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "demand start", "auto start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.014312", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "71da415c-042c-40cc-a731-847139c85302", "timestamp": "2025-05-30T00:27:43", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "Source: Microsoft-Windows-Kernel-General | Event ID: 16 | Data: 177, \\??\\C:\\ProgramData\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\S-1-5-21-441365252-3849924418-184073628-1001\\SystemAppData\\Helium\\Cache\\46815b985956d0f7_COM15.dat, 1, 1", "raw_data": null, "additional_fields": {"record_number": 71832, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["177", "\\??\\C:\\ProgramData\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\S-1-5-21-441365252-3849924418-184073628-1001\\SystemAppData\\Helium\\Cache\\46815b985956d0f7_COM15.dat", "1", "1"], "data": null, "event_id": 16, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:04.014312", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "605dd324-8d29-4843-9505-a73d1b415c61", "timestamp": "2025-05-30T00:26:49", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "warning", "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.", "raw_data": null, "additional_fields": {"record_number": 71831, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["**************.in-addr.arpa.", "128", "02000000C0A80201000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "6004"], "data": null, "event_id": 1014, "event_category": 1014, "event_type": 2, "metadata": {"collection_time": "2025-05-30T00:37:04.014312", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "123b8347-5336-4898-a2aa-999e3dec4adf", "timestamp": "2025-05-30T00:36:43", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "warning", "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.", "raw_data": null, "additional_fields": {"record_number": 71840, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["**************.in-addr.arpa.", "128", "02000000C0A80201000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "6004"], "data": null, "event_id": 1014, "event_category": 1014, "event_type": 2, "metadata": {"collection_time": "2025-05-30T00:37:09.023862", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "4fd3058b-dbdd-4885-b8c5-202f5a0f4cf3", "timestamp": "2025-05-30T00:35:52", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The time provider 'VMICTimeProvider' has indicated that the current hardware and operating environment is not supported and has stopped. This behavior is expected for VMICTimeProvider on non-HyperV-guest environments. This may be the expected behavior for the current provider in the current operating environment as well.", "raw_data": null, "additional_fields": {"record_number": 71839, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["VMICTimeProvider"], "data": null, "event_id": 158, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.024457", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "6f79c8fe-98e8-4592-ab03-c6c7b981cdd9", "timestamp": "2025-05-30T00:35:45", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The Group Policy settings for the computer were processed successfully. New settings from 1 Group Policy objects were detected and applied.", "raw_data": null, "additional_fields": {"record_number": 71838, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["1", "4348", "0", "47", "", "1"], "data": null, "event_id": 1502, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.024457", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "20622bf4-7d79-42e7-a201-d952c39cacb4", "timestamp": "2025-05-30T00:35:13", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from demand start to auto start.", "raw_data": null, "additional_fields": {"record_number": 71837, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "demand start", "auto start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.024962", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "cfd6313c-52e1-4d85-8aad-e1073a898013", "timestamp": "2025-05-30T00:32:39", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from auto start to demand start.", "raw_data": null, "additional_fields": {"record_number": 71836, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "auto start", "demand start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.025112", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "d7818526-e706-4131-91a1-aff1ab675834", "timestamp": "2025-05-30T00:30:20", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from demand start to auto start.", "raw_data": null, "additional_fields": {"record_number": 71835, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "demand start", "auto start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.025112", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "6add89c5-b390-4da6-84d2-08dd858f5cdc", "timestamp": "2025-05-30T00:30:18", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from auto start to demand start.", "raw_data": null, "additional_fields": {"record_number": 71834, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "auto start", "demand start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.025619", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "5520b5f0-74d2-4b60-bf22-07e8aa55c09f", "timestamp": "2025-05-30T00:28:14", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "The start type of the Background Intelligent Transfer Service service was changed from demand start to auto start.", "raw_data": null, "additional_fields": {"record_number": 71833, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["Background Intelligent Transfer Service", "demand start", "auto start", "BITS"], "data": null, "event_id": 7040, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.025619", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "9a9fdce6-d2b5-4e1c-99e9-2602ac8f447a", "timestamp": "2025-05-30T00:27:43", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "info", "message": "Source: Microsoft-Windows-Kernel-General | Event ID: 16 | Data: 177, \\??\\C:\\ProgramData\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\S-1-5-21-441365252-3849924418-184073628-1001\\SystemAppData\\Helium\\Cache\\46815b985956d0f7_COM15.dat, 1, 1", "raw_data": null, "additional_fields": {"record_number": 71832, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["177", "\\??\\C:\\ProgramData\\Packages\\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\\S-1-5-21-441365252-3849924418-184073628-1001\\SystemAppData\\Helium\\Cache\\46815b985956d0f7_COM15.dat", "1", "1"], "data": null, "event_id": 16, "event_category": 0, "event_type": 4, "metadata": {"collection_time": "2025-05-30T00:37:09.025619", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
{"log_id": "853abf79-fa98-4ba2-930b-6a30cd10f438", "timestamp": "2025-05-30T00:26:49", "source": "System", "source_type": "event", "host": "DESKTOP-PLUAU4C", "log_level": "warning", "message": "Name resolution for the name **************.in-addr.arpa. timed out after none of the configured DNS servers responded. Client PID 6004.", "raw_data": null, "additional_fields": {"record_number": 71831, "computer_name": "DESKTOP-PLUAU4C", "string_inserts": ["**************.in-addr.arpa.", "128", "02000000C0A80201000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "6004"], "data": null, "event_id": 1014, "event_category": 1014, "event_type": 2, "metadata": {"collection_time": "2025-05-30T00:37:09.026154", "agent_version": "1.0.0", "standardizer_version": "1.0.0", "windows_event_log": true, "event_log_source": "System"}}}
