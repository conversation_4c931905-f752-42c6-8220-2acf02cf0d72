import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Pagination,
} from '@mui/material'
import {
  Search,
  FilterList,
  MoreVert,
  Visibility,
  Edit,
  Delete,
  Share,
  Download,
  Schedule,
  Assessment,
  Security,
  BarChart,
  Refresh,
} from '@mui/icons-material'
import { fetchReports, deleteReport, setFilters, executeReport } from '../../../store/slices/reportingSlice'
import api from '../../../services/api'

const ReportLibrary = () => {
  const dispatch = useDispatch()
  const { reports, reportsPagination, filters, loading, error } = useSelector(state => state.reporting)
  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedReport, setSelectedReport] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState(filters.search || '')

  useEffect(() => {
    dispatch(fetchReports(filters))
  }, [dispatch, filters])

  const handleSearch = (event) => {
    const value = event.target.value
    setSearchTerm(value)
    dispatch(setFilters({ search: value }))
  }

  const handleFilterChange = (filterType, value) => {
    dispatch(setFilters({ [filterType]: value }))
  }

  const handleMenuOpen = (event, report) => {
    setAnchorEl(event.currentTarget)
    setSelectedReport(report)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedReport(null)
  }

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true)
    handleMenuClose()
  }

  const handleDeleteConfirm = async () => {
    if (selectedReport) {
      await dispatch(deleteReport(selectedReport._id))
      setDeleteDialogOpen(false)
      setSelectedReport(null)
    }
  }

  const handlePageChange = (event, page) => {
    dispatch(setFilters({ page }))
  }

  const handleRefresh = () => {
    dispatch(fetchReports(filters))
  }

  const handleViewReport = async (report) => {
    try {
      // Get formatted HTML preview using API service
      const response = await api.post(`/reports/${report._id}/preview`, {
        parameters: { timeRange: '7d' }
      })

      const { html } = response.data.data

      // Open preview in new window
      const previewWindow = window.open('', '_blank', 'width=1200,height=800')
      previewWindow.document.write(html)
      previewWindow.document.close()
    } catch (error) {
      console.error('Failed to view report:', error)
      alert('Failed to generate report preview. Please try again later.')
    }
  }

  const handleExportReport = async (report) => {
    try {
      // Export as PDF using API service
      const response = await api.post(`/reports/${report._id}/export/pdf`, {
        parameters: { timeRange: '30d' }
      }, {
        responseType: 'blob'
      })

      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${report.name}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Failed to export PDF:', error)
      // Fallback to CSV export
      try {
        const response = await api.post(`/reports/${report._id}/export/csv`, {
          parameters: { timeRange: '30d' }
        }, {
          responseType: 'blob'
        })

        const blob = new Blob([response.data], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${report.name}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } catch (csvError) {
        console.error('CSV export also failed:', csvError)
        alert('Failed to export report. Please try again later.')
      }
    }
  }

  const getReportIcon = (type) => {
    switch (type) {
      case 'analytics':
        return <BarChart color="primary" />
      case 'compliance':
        return <Assessment color="secondary" />
      case 'custom':
        return <Security color="info" />
      default:
        return <Assessment />
    }
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'analytics':
        return 'primary'
      case 'compliance':
        return 'secondary'
      case 'custom':
        return 'info'
      default:
        return 'default'
    }
  }

  const renderReportCard = (report) => (
    <Grid item xs={12} sm={6} md={4} key={report._id}>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {getReportIcon(report.type)}
              <Typography variant="h6" sx={{ ml: 1 }}>
                {report.name}
              </Typography>
            </Box>
            <IconButton
              size="small"
              onClick={(e) => handleMenuOpen(e, report)}
            >
              <MoreVert />
            </IconButton>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {report.description || 'No description available'}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Chip
              label={report.type}
              size="small"
              color={getTypeColor(report.type)}
            />
            <Chip
              label={report.status}
              size="small"
              variant="outlined"
            />
          </Box>
          
          <Typography variant="caption" color="text.secondary">
            Created: {new Date(report.createdAt).toLocaleDateString()}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Last run: {report.lastRunAt ? new Date(report.lastRunAt).toLocaleDateString() : 'Never'}
          </Typography>
          
          {report.tags && report.tags.length > 0 && (
            <Box sx={{ mt: 1 }}>
              {report.tags.slice(0, 3).map((tag, index) => (
                <Chip
                  key={index}
                  label={tag}
                  size="small"
                  variant="outlined"
                  sx={{ mr: 0.5, mb: 0.5 }}
                />
              ))}
              {report.tags.length > 3 && (
                <Chip
                  label={`+${report.tags.length - 3} more`}
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
          )}
        </CardContent>
        
        <CardActions>
          <Button
            size="small"
            startIcon={<Visibility />}
            onClick={() => handleViewReport(report)}
          >
            View
          </Button>
          <Button
            size="small"
            startIcon={<Download />}
            onClick={() => handleExportReport(report)}
          >
            Export
          </Button>
        </CardActions>
      </Card>
    </Grid>
  )

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Typography variant="h5" gutterBottom>
            Report Library
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage and organize your saved reports and templates.
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={handleRefresh}
          disabled={loading.reports}
        >
          Refresh
        </Button>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search reports..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant={filters.type === '' ? 'contained' : 'outlined'}
                onClick={() => handleFilterChange('type', '')}
              >
                All Types
              </Button>
              <Button
                variant={filters.type === 'analytics' ? 'contained' : 'outlined'}
                onClick={() => handleFilterChange('type', 'analytics')}
              >
                Analytics
              </Button>
              <Button
                variant={filters.type === 'compliance' ? 'contained' : 'outlined'}
                onClick={() => handleFilterChange('type', 'compliance')}
              >
                Compliance
              </Button>
              <Button
                variant={filters.type === 'custom' ? 'contained' : 'outlined'}
                onClick={() => handleFilterChange('type', 'custom')}
              >
                Custom
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Reports Grid */}
      {loading.reports ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <Typography>Loading reports...</Typography>
        </Box>
      ) : error.reports ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error.reports}
        </Alert>
      ) : reports.length === 0 ? (
        <Alert severity="info">
          No reports found. Create your first report to get started.
        </Alert>
      ) : (
        <>
          <Grid container spacing={3}>
            {reports.map(renderReportCard)}
          </Grid>
          
          {/* Pagination */}
          {reportsPagination.pages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={reportsPagination.pages}
                page={reportsPagination.page}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => console.log('View report')}>
          <ListItemIcon>
            <Visibility fontSize="small" />
          </ListItemIcon>
          View Report
        </MenuItem>
        <MenuItem onClick={() => console.log('Edit report')}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          Edit Report
        </MenuItem>
        <MenuItem onClick={() => console.log('Share report')}>
          <ListItemIcon>
            <Share fontSize="small" />
          </ListItemIcon>
          Share Report
        </MenuItem>
        <MenuItem onClick={() => console.log('Schedule report')}>
          <ListItemIcon>
            <Schedule fontSize="small" />
          </ListItemIcon>
          Schedule Report
        </MenuItem>
        <MenuItem onClick={handleDeleteClick}>
          <ListItemIcon>
            <Delete fontSize="small" />
          </ListItemIcon>
          Delete Report
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Report</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedReport?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ReportLibrary
