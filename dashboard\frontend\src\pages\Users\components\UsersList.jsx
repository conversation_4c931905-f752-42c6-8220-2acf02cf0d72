import React, { useState, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import {
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  Button,
  Checkbox,
  Toolbar,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from '@mui/material'
import {
  Search,
  MoreVert,
  Edit,
  Delete,
  Lock,
  LockOpen,
  Refresh,
  FilterList,
  PersonOff,
  VpnKey,
} from '@mui/icons-material'
import { format } from 'date-fns'
import {
  fetchUsers,
  updateUserStatus,
  deleteUser,
  resetUserPassword,
  setFilters,
  setSelectedUsers,
  clearSelectedUsers,
} from '../../../store/slices/usersSlice'

const UsersList = ({ 
  users, 
  pagination, 
  filters, 
  isLoading, 
  canManageUsers, 
  onEditUser, 
  onRefresh,
  showSnackbar 
}) => {
  const dispatch = useDispatch()
  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedUser, setSelectedUser] = useState(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false)
  const [newPassword, setNewPassword] = useState('')
  const [forcePasswordChange, setForcePasswordChange] = useState(true)
  const [selectedUsers, setSelectedUsersLocal] = useState([])

  const handleMenuClick = (event, user) => {
    setAnchorEl(event.currentTarget)
    setSelectedUser(user)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedUser(null)
  }

  const handleFilterChange = (field, value) => {
    dispatch(setFilters({ [field]: value }))
    dispatch(fetchUsers({ ...filters, [field]: value, page: 1 }))
  }

  const handlePageChange = (event, newPage) => {
    dispatch(fetchUsers({ ...filters, page: newPage + 1 }))
  }

  const handleRowsPerPageChange = (event) => {
    const newLimit = parseInt(event.target.value, 10)
    dispatch(fetchUsers({ ...filters, limit: newLimit, page: 1 }))
  }

  const handleStatusChange = async (userId, newStatus) => {
    try {
      await dispatch(updateUserStatus({ id: userId, status: newStatus })).unwrap()
      showSnackbar(`User status updated to ${newStatus}`)
      handleMenuClose()
    } catch (error) {
      showSnackbar(error, 'error')
    }
  }

  const handleDeleteUser = async () => {
    try {
      await dispatch(deleteUser(selectedUser._id)).unwrap()
      showSnackbar('User deleted successfully')
      setDeleteDialogOpen(false)
      handleMenuClose()
    } catch (error) {
      showSnackbar(error, 'error')
    }
  }

  const handleResetPassword = async () => {
    if (!newPassword || newPassword.length < 8) {
      showSnackbar('Password must be at least 8 characters long', 'error')
      return
    }

    try {
      await dispatch(resetUserPassword({
        id: selectedUser._id,
        newPassword,
        forceChange: forcePasswordChange,
      })).unwrap()
      showSnackbar('Password reset successfully')
      setResetPasswordDialogOpen(false)
      setNewPassword('')
      handleMenuClose()
    } catch (error) {
      showSnackbar(error, 'error')
    }
  }

  const handleSelectUser = (userId) => {
    const newSelected = selectedUsers.includes(userId)
      ? selectedUsers.filter(id => id !== userId)
      : [...selectedUsers, userId]
    setSelectedUsersLocal(newSelected)
    dispatch(setSelectedUsers(newSelected))
  }

  const handleSelectAllUsers = (event) => {
    if (event.target.checked) {
      const allUserIds = users.map(user => user._id)
      setSelectedUsersLocal(allUserIds)
      dispatch(setSelectedUsers(allUserIds))
    } else {
      setSelectedUsersLocal([])
      dispatch(clearSelectedUsers())
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success'
      case 'inactive':
        return 'default'
      case 'locked':
        return 'error'
      default:
        return 'default'
    }
  }

  const getRoleDisplayName = (role) => {
    const roleMap = {
      admin: 'System Administrator',
      security_analyst: 'Security Analyst',
      compliance_officer: 'Compliance Officer',
      executive: 'Executive',
    }
    return roleMap[role] || role
  }

  return (
    <Card>
      <CardContent>
        {/* Filters and Search */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
            <TextField
              placeholder="Search users..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 300 }}
            />
            
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Role</InputLabel>
              <Select
                value={filters.role}
                label="Role"
                onChange={(e) => handleFilterChange('role', e.target.value)}
              >
                <MenuItem value="">All Roles</MenuItem>
                <MenuItem value="admin">Administrator</MenuItem>
                <MenuItem value="security_analyst">Security Analyst</MenuItem>
                <MenuItem value="compliance_officer">Compliance Officer</MenuItem>
                <MenuItem value="executive">Executive</MenuItem>
              </Select>
            </FormControl>

            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status}
                label="Status"
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">All Status</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="locked">Locked</MenuItem>
              </Select>
            </FormControl>

            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={onRefresh}
              disabled={isLoading}
            >
              Refresh
            </Button>
          </Box>

          {/* Bulk Actions */}
          {selectedUsers.length > 0 && canManageUsers && (
            <Toolbar sx={{ pl: 0, pr: 0 }}>
              <Typography variant="subtitle1" component="div" sx={{ flex: '1 1 100%' }}>
                {selectedUsers.length} selected
              </Typography>
              <Tooltip title="Bulk actions coming soon">
                <Button variant="outlined" disabled>
                  Bulk Actions
                </Button>
              </Tooltip>
            </Toolbar>
          )}
        </Box>

        {/* Users Table */}
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                {canManageUsers && (
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selectedUsers.length > 0 && selectedUsers.length < users.length}
                      checked={users.length > 0 && selectedUsers.length === users.length}
                      onChange={handleSelectAllUsers}
                    />
                  </TableCell>
                )}
                <TableCell>User</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={canManageUsers ? 7 : 6} align="center" sx={{ py: 4 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={canManageUsers ? 7 : 6} align="center" sx={{ py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      No users found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user) => (
                  <TableRow key={user._id} hover>
                    {canManageUsers && (
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedUsers.includes(user._id)}
                          onChange={() => handleSelectUser(user._id)}
                        />
                      </TableCell>
                    )}
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2">
                          {user.firstName} {user.lastName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {user.email}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          @{user.username}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getRoleDisplayName(user.role)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={user.status}
                        size="small"
                        color={getStatusColor(user.status)}
                      />
                    </TableCell>
                    <TableCell>
                      {user.lastLogin ? (
                        <Typography variant="body2">
                          {format(new Date(user.lastLogin), 'MMM dd, yyyy HH:mm')}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Never
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        onClick={(e) => handleMenuClick(e, user)}
                        disabled={!canManageUsers}
                      >
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          component="div"
          count={pagination.totalCount}
          page={pagination.currentPage - 1}
          onPageChange={handlePageChange}
          rowsPerPage={pagination.limit}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[10, 20, 50, 100]}
        />
      </CardContent>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => onEditUser(selectedUser)}>
          <Edit sx={{ mr: 1 }} />
          Edit User
        </MenuItem>
        
        {selectedUser?.status === 'active' ? (
          <MenuItem onClick={() => handleStatusChange(selectedUser._id, 'inactive')}>
            <PersonOff sx={{ mr: 1 }} />
            Deactivate
          </MenuItem>
        ) : (
          <MenuItem onClick={() => handleStatusChange(selectedUser._id, 'active')}>
            <LockOpen sx={{ mr: 1 }} />
            Activate
          </MenuItem>
        )}

        {selectedUser?.status !== 'locked' ? (
          <MenuItem onClick={() => handleStatusChange(selectedUser._id, 'locked')}>
            <Lock sx={{ mr: 1 }} />
            Lock Account
          </MenuItem>
        ) : (
          <MenuItem onClick={() => handleStatusChange(selectedUser._id, 'active')}>
            <LockOpen sx={{ mr: 1 }} />
            Unlock Account
          </MenuItem>
        )}

        <MenuItem onClick={() => setResetPasswordDialogOpen(true)}>
          <VpnKey sx={{ mr: 1 }} />
          Reset Password
        </MenuItem>

        <MenuItem 
          onClick={() => setDeleteDialogOpen(true)}
          sx={{ color: 'error.main' }}
        >
          <Delete sx={{ mr: 1 }} />
          Delete User
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete User</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete user "{selectedUser?.firstName} {selectedUser?.lastName}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteUser} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reset Password Dialog */}
      <Dialog open={resetPasswordDialogOpen} onClose={() => setResetPasswordDialogOpen(false)}>
        <DialogTitle>Reset Password</DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            Reset password for "{selectedUser?.firstName} {selectedUser?.lastName}"
          </Typography>
          <TextField
            fullWidth
            label="New Password"
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            sx={{ mb: 2 }}
            helperText="Password must be at least 8 characters long"
          />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Checkbox
              checked={forcePasswordChange}
              onChange={(e) => setForcePasswordChange(e.target.checked)}
            />
            <Typography variant="body2">
              Force user to change password on next login
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResetPasswordDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleResetPassword} variant="contained">
            Reset Password
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  )
}

export default UsersList
