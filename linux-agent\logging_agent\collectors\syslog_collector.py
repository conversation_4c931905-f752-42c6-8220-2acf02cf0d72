"""
Syslog Collector for Linux Log Collection Agent

This module collects system logs from /var/log/syslog, /var/log/messages,
and other syslog files on Linux systems.
"""

import logging
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from .base_collector import BaseLogCollector


class SyslogCollector(BaseLogCollector):
    """Collector for Linux syslog files."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the syslog collector.
        
        Args:
            config: Collector configuration
            logger: Logger instance
        """
        super().__init__(config, logger)
        
        # Syslog-specific configuration
        self.real_time = config.get('real_time', True)
        
        # Default syslog paths if none specified
        if not config.get('paths'):
            self.config['paths'] = self._get_default_syslog_paths()
        
        self.logger.info(f"Syslog collector initialized with {len(self.config['paths'])} paths")
    
    def _get_default_syslog_paths(self) -> List[str]:
        """Get default syslog paths for different distributions."""
        default_paths = [
            '/var/log/syslog',      # Ubuntu, Debian
            '/var/log/messages',    # CentOS, RHEL, Fedora
            '/var/log/system.log'   # Some other distributions
        ]
        
        existing_paths = []
        for path in default_paths:
            if os.path.exists(path) and os.access(path, os.R_OK):
                existing_paths.append(path)
        
        return existing_paths
    
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from syslog files.
        
        Returns:
            List of collected log entries
        """
        collected_logs = []
        
        try:
            log_paths = self._get_log_paths()
            
            if not log_paths:
                self.logger.warning("No accessible syslog files found")
                return collected_logs
            
            for log_path in log_paths:
                try:
                    # Read new lines from the file
                    new_lines = self._read_file_from_position(log_path)
                    
                    if new_lines:
                        self.logger.debug(f"Processing {len(new_lines)} new lines from {log_path}")
                        
                        for line in new_lines:
                            log_entry = self._parse_syslog_line(line, source="System")
                            if log_entry:
                                # Add file-specific metadata
                                log_entry['additional_fields']['log_file'] = log_path
                                log_entry['additional_fields']['collector'] = 'syslog'
                                
                                # Sanitize message
                                log_entry['message'] = self._sanitize_message(log_entry['message'])
                                
                                collected_logs.append(log_entry)
                        
                        self.stats['files_processed'] += 1
                
                except Exception as e:
                    self.logger.error(f"Error processing syslog file {log_path}: {e}")
                    self.stats['errors'] += 1
            
            if collected_logs:
                self.stats['logs_collected'] += len(collected_logs)
                self.stats['last_collection'] = datetime.now()
                self.logger.debug(f"Collected {len(collected_logs)} syslog entries")
            
        except Exception as e:
            self.logger.error(f"Error in syslog collection: {e}")
            self.stats['errors'] += 1
        
        return collected_logs
    
    def _parse_syslog_line(self, line: str, source: str = "System") -> Optional[Dict[str, Any]]:
        """
        Parse a syslog line with enhanced syslog-specific parsing.
        
        Args:
            line: Log line to parse
            source: Log source name
            
        Returns:
            Parsed log entry or None if parsing failed
        """
        # Use base class parsing first
        log_entry = super()._parse_syslog_line(line, source)
        
        if not log_entry:
            return None
        
        # Enhanced syslog-specific processing
        try:
            message = log_entry['message']
            additional_fields = log_entry['additional_fields']
            
            # Extract systemd unit information if present
            if 'systemd' in message.lower():
                systemd_info = self._extract_systemd_info(message)
                if systemd_info:
                    additional_fields.update(systemd_info)
            
            # Extract kernel information if present
            if log_entry['additional_fields'].get('program') == 'kernel':
                kernel_info = self._extract_kernel_info(message)
                if kernel_info:
                    additional_fields.update(kernel_info)
                    log_entry['source'] = 'Kernel'
                    log_entry['source_type'] = 'kernel'
            
            # Classify system events
            event_type = self._classify_system_event(message)
            if event_type:
                additional_fields['event_type'] = event_type
            
            # Extract IP addresses and network information
            network_info = self._extract_network_info(message)
            if network_info:
                additional_fields['network'] = network_info
            
        except Exception as e:
            self.logger.error(f"Error in enhanced syslog parsing: {e}")
        
        return log_entry
    
    def _extract_systemd_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract systemd-related information from message."""
        import re
        
        systemd_info = {}
        
        # Extract unit name
        unit_pattern = r'(\w+\.(?:service|socket|target|timer|mount|device))'
        unit_match = re.search(unit_pattern, message)
        if unit_match:
            systemd_info['systemd_unit'] = unit_match.group(1)
        
        # Extract systemd actions
        if 'started' in message.lower():
            systemd_info['systemd_action'] = 'started'
        elif 'stopped' in message.lower():
            systemd_info['systemd_action'] = 'stopped'
        elif 'failed' in message.lower():
            systemd_info['systemd_action'] = 'failed'
        elif 'reloaded' in message.lower():
            systemd_info['systemd_action'] = 'reloaded'
        
        return systemd_info if systemd_info else None
    
    def _extract_kernel_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract kernel-related information from message."""
        import re
        
        kernel_info = {}
        
        # Extract kernel module information
        if 'module' in message.lower():
            module_pattern = r'module\s+(\w+)'
            module_match = re.search(module_pattern, message, re.IGNORECASE)
            if module_match:
                kernel_info['kernel_module'] = module_match.group(1)
        
        # Extract hardware information
        if any(hw in message.lower() for hw in ['usb', 'pci', 'cpu', 'memory', 'disk']):
            kernel_info['hardware_related'] = True
        
        # Extract driver information
        if 'driver' in message.lower():
            kernel_info['driver_related'] = True
        
        return kernel_info if kernel_info else None
    
    def _classify_system_event(self, message: str) -> Optional[str]:
        """Classify the type of system event."""
        message_lower = message.lower()
        
        # Authentication events
        if any(auth in message_lower for auth in ['login', 'logout', 'authentication', 'sudo', 'su ']):
            return 'authentication'
        
        # Service events
        if any(svc in message_lower for svc in ['started', 'stopped', 'failed', 'service']):
            return 'service'
        
        # Network events
        if any(net in message_lower for net in ['network', 'interface', 'dhcp', 'dns']):
            return 'network'
        
        # Hardware events
        if any(hw in message_lower for hw in ['usb', 'pci', 'hardware', 'device']):
            return 'hardware'
        
        # File system events
        if any(fs in message_lower for fs in ['mount', 'unmount', 'filesystem', 'disk']):
            return 'filesystem'
        
        # Security events
        if any(sec in message_lower for sec in ['security', 'firewall', 'iptables', 'selinux']):
            return 'security'
        
        return None
    
    def _extract_network_info(self, message: str) -> Optional[Dict[str, Any]]:
        """Extract network-related information from message."""
        import re
        
        network_info = {}
        
        # Extract IP addresses
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ip_matches = re.findall(ip_pattern, message)
        if ip_matches:
            network_info['ip_addresses'] = list(set(ip_matches))  # Remove duplicates
        
        # Extract MAC addresses
        mac_pattern = r'\b(?:[0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}\b'
        mac_matches = re.findall(mac_pattern, message)
        if mac_matches:
            network_info['mac_addresses'] = list(set(mac_matches))
        
        # Extract interface names
        interface_pattern = r'\b(?:eth|wlan|lo|enp|wlp|docker|br-)\w*\b'
        interface_matches = re.findall(interface_pattern, message)
        if interface_matches:
            network_info['interfaces'] = list(set(interface_matches))
        
        # Extract ports
        port_pattern = r'\bport\s+(\d+)\b'
        port_matches = re.findall(port_pattern, message, re.IGNORECASE)
        if port_matches:
            network_info['ports'] = [int(port) for port in port_matches]
        
        return network_info if network_info else None
    
    def start(self) -> None:
        """Start the syslog collector."""
        super().start()
        
        # Log accessible syslog files
        log_paths = self._get_log_paths()
        if log_paths:
            self.logger.info(f"Monitoring syslog files: {log_paths}")
        else:
            self.logger.warning("No accessible syslog files found")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get syslog collector statistics."""
        stats = super().get_stats()
        stats['collector_type'] = 'syslog'
        stats['monitored_files'] = self._get_log_paths()
        return stats
