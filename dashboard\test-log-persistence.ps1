# Test script to verify log persistence after fixing TTL issue

Write-Host "Testing Log Persistence..." -ForegroundColor Green

# Login first
$loginData = @{
    email = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/auth/login" -Method Post -Body $loginData -ContentType "application/json"
    $token = $loginResponse.data.token
    Write-Host "✅ Login successful" -ForegroundColor Green
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Check initial log count
Write-Host "`n1. Checking initial log count..." -ForegroundColor Yellow
try {
    $initialResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?limit=1000" -Method Get -Headers $headers
    $initialCount = $initialResponse.data.pagination.totalCount
    Write-Host "Initial log count: $initialCount" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Failed to get initial count: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get API key for log ingestion
Write-Host "`n2. Getting API key for log ingestion..." -ForegroundColor Yellow
try {
    $userResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/auth/me" -Method Get -Headers $headers
    $apiKey = $userResponse.data.user.apiKeys[0].key
    Write-Host "✅ API key retrieved" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get API key: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create test logs
$testLogs = @{
    logs = @(
        @{
            logId = "persistence_test_001"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "System"
            sourceType = "event"
            host = "test-server-01"
            logLevel = "info"
            message = "Test log for persistence verification - should not be deleted"
            additionalFields = @{
                testId = "persistence_test"
                timestamp = (Get-Date).ToString()
            }
            metadata = @{
                agentId = "test-agent-001"
                agentVersion = "1.0.0"
                standardizerVersion = "1.0.0"
            }
            tags = @("test", "persistence", "verification")
            severity = 2
        },
        @{
            logId = "persistence_test_002"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "Application"
            sourceType = "application"
            host = "test-server-02"
            logLevel = "warning"
            message = "Another test log for persistence verification"
            additionalFields = @{
                testId = "persistence_test"
                component = "test-component"
            }
            metadata = @{
                agentId = "test-agent-002"
                agentVersion = "1.0.0"
                standardizerVersion = "1.0.0"
            }
            tags = @("test", "persistence", "warning")
            severity = 3
        }
    )
} | ConvertTo-Json -Depth 10

# Ingest test logs
Write-Host "`n3. Ingesting test logs..." -ForegroundColor Yellow
$ingestHeaders = @{
    "X-API-Key" = $apiKey
    "Content-Type" = "application/json"
}

try {
    $ingestResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs" -Method Post -Body $testLogs -Headers $ingestHeaders
    Write-Host "✅ Logs ingested successfully" -ForegroundColor Green
    Write-Host "Processed: $($ingestResponse.data.processed)" -ForegroundColor Cyan
    Write-Host "Failed: $($ingestResponse.data.failed)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Log ingestion failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Wait a moment for processing
Write-Host "`n4. Waiting 5 seconds for processing..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Check log count after ingestion
Write-Host "`n5. Checking log count after ingestion..." -ForegroundColor Yellow
try {
    $afterResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?limit=1000" -Method Get -Headers $headers
    $afterCount = $afterResponse.data.pagination.totalCount
    Write-Host "Log count after ingestion: $afterCount" -ForegroundColor Cyan
    
    if ($afterCount -gt $initialCount) {
        Write-Host "✅ Logs persisted successfully! Added $($afterCount - $initialCount) logs" -ForegroundColor Green
    } else {
        Write-Host "❌ Logs were not persisted or were deleted" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to get final count: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Wait another 10 seconds and check again to ensure no auto-deletion
Write-Host "`n6. Waiting 10 seconds to verify no auto-deletion..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

try {
    $finalResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?limit=1000" -Method Get -Headers $headers
    $finalCount = $finalResponse.data.pagination.totalCount
    Write-Host "Final log count: $finalCount" -ForegroundColor Cyan
    
    if ($finalCount -eq $afterCount) {
        Write-Host "✅ No auto-deletion occurred! Logs are persistent" -ForegroundColor Green
    } else {
        Write-Host "❌ Auto-deletion still occurring! Lost $($afterCount - $finalCount) logs" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to get final verification count: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Search for our test logs specifically
Write-Host "`n7. Searching for test logs..." -ForegroundColor Yellow
try {
    $searchResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?search=persistence_test&limit=10" -Method Get -Headers $headers
    $testLogCount = $searchResponse.data.logs.Count
    Write-Host "Found $testLogCount test logs" -ForegroundColor Cyan
    
    if ($testLogCount -gt 0) {
        Write-Host "✅ Test logs found and searchable" -ForegroundColor Green
        foreach ($log in $searchResponse.data.logs) {
            Write-Host "  - $($log.logId): $($log.message.Substring(0, [Math]::Min(50, $log.message.Length)))..." -ForegroundColor White
        }
    } else {
        Write-Host "❌ Test logs not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Search failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Log persistence test completed!" -ForegroundColor Green
Write-Host "If all tests passed, logs should now persist correctly." -ForegroundColor Cyan
