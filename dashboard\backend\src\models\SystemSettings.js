const mongoose = require('mongoose');

const systemSettingsSchema = new mongoose.Schema({
  // System identification
  settingsVersion: {
    type: String,
    default: '1.0.0',
  },
  
  // Log retention policies
  logRetention: {
    defaultRetentionDays: {
      type: Number,
      min: 1,
      max: 3650, // 10 years
      default: 90,
    },
    retentionPolicies: [{
      name: {
        type: String,
        required: true,
        trim: true,
      },
      description: String,
      retentionDays: {
        type: Number,
        min: 1,
        max: 3650,
        required: true,
      },
      logSources: [{
        type: String,
        enum: ['System', 'Application', 'Security', 'Network', 'Custom'],
      }],
      logLevels: [{
        type: String,
        enum: ['critical', 'error', 'warning', 'info', 'debug'],
      }],
      isDefault: {
        type: Boolean,
        default: false,
      },
      createdAt: {
        type: Date,
        default: Date.now,
      },
    }],
    autoArchiveEnabled: {
      type: Boolean,
      default: true,
    },
    archiveCompressionEnabled: {
      type: Boolean,
      default: true,
    },
    archiveLocation: {
      type: String,
      default: 'local',
      enum: ['local', 's3', 'azure', 'gcp'],
    },
  },

  // Data export/import settings
  dataManagement: {
    exportFormats: [{
      type: String,
      enum: ['json', 'csv', 'xml', 'pdf'],
      default: ['json', 'csv'],
    }],
    maxExportRecords: {
      type: Number,
      min: 100,
      max: 1000000,
      default: 100000,
    },
    exportRetentionDays: {
      type: Number,
      min: 1,
      max: 30,
      default: 7,
    },
    allowBulkImport: {
      type: Boolean,
      default: true,
    },
    maxImportFileSize: {
      type: String,
      default: '100MB',
    },
    importValidationEnabled: {
      type: Boolean,
      default: true,
    },
  },

  // System notification preferences
  systemNotifications: {
    emailSettings: {
      enabled: {
        type: Boolean,
        default: true,
      },
      smtpHost: String,
      smtpPort: {
        type: Number,
        default: 587,
      },
      smtpSecure: {
        type: Boolean,
        default: true,
      },
      smtpUser: String,
      smtpPassword: String, // Should be encrypted
      fromAddress: String,
      fromName: {
        type: String,
        default: 'ExLog System',
      },
    },
    webhookSettings: {
      enabled: {
        type: Boolean,
        default: false,
      },
      defaultWebhookUrl: String,
      retryAttempts: {
        type: Number,
        min: 0,
        max: 5,
        default: 3,
      },
      timeout: {
        type: Number,
        min: 1000,
        max: 30000,
        default: 10000,
      },
    },
    slackIntegration: {
      enabled: {
        type: Boolean,
        default: false,
      },
      webhookUrl: String,
      defaultChannel: String,
      botToken: String,
    },
  },

  // Dashboard customization options
  dashboardSettings: {
    defaultTheme: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'light',
    },
    allowUserThemeOverride: {
      type: Boolean,
      default: true,
    },
    defaultRefreshInterval: {
      type: Number,
      min: 10,
      max: 300,
      default: 30,
    },
    maxWidgetsPerDashboard: {
      type: Number,
      min: 5,
      max: 50,
      default: 20,
    },
    enablePublicDashboards: {
      type: Boolean,
      default: false,
    },
    customBranding: {
      enabled: {
        type: Boolean,
        default: false,
      },
      logoUrl: String,
      companyName: String,
      primaryColor: String,
      secondaryColor: String,
    },
  },

  // Security configuration
  securitySettings: {
    passwordPolicy: {
      minLength: {
        type: Number,
        min: 8,
        max: 128,
        default: 8,
      },
      requireUppercase: {
        type: Boolean,
        default: true,
      },
      requireLowercase: {
        type: Boolean,
        default: true,
      },
      requireNumbers: {
        type: Boolean,
        default: true,
      },
      requireSpecialChars: {
        type: Boolean,
        default: true,
      },
      maxAge: {
        type: Number,
        min: 30,
        max: 365,
        default: 90, // days
      },
      preventReuse: {
        type: Number,
        min: 0,
        max: 24,
        default: 5, // last N passwords
      },
    },
    sessionSettings: {
      defaultTimeout: {
        type: Number,
        min: 15,
        max: 480,
        default: 60, // minutes
      },
      maxConcurrentSessions: {
        type: Number,
        min: 1,
        max: 10,
        default: 3,
      },
      requireMfaForAdmins: {
        type: Boolean,
        default: true,
      },
      allowRememberMe: {
        type: Boolean,
        default: true,
      },
      rememberMeDuration: {
        type: Number,
        min: 1,
        max: 90,
        default: 30, // days
      },
    },
    auditSettings: {
      enabled: {
        type: Boolean,
        default: true,
      },
      retentionDays: {
        type: Number,
        min: 30,
        max: 2555, // 7 years
        default: 365,
      },
      logFailedLogins: {
        type: Boolean,
        default: true,
      },
      logSuccessfulLogins: {
        type: Boolean,
        default: true,
      },
      logDataAccess: {
        type: Boolean,
        default: true,
      },
      logConfigChanges: {
        type: Boolean,
        default: true,
      },
    },
  },

  // API configuration
  apiSettings: {
    rateLimiting: {
      enabled: {
        type: Boolean,
        default: true,
      },
      requestsPerMinute: {
        type: Number,
        min: 10,
        max: 10000,
        default: 100,
      },
      burstLimit: {
        type: Number,
        min: 10,
        max: 1000,
        default: 200,
      },
    },
    apiKeySettings: {
      defaultExpirationDays: {
        type: Number,
        min: 1,
        max: 365,
        default: 90,
      },
      maxKeysPerUser: {
        type: Number,
        min: 1,
        max: 50,
        default: 10,
      },
      requireIpWhitelist: {
        type: Boolean,
        default: false,
      },
    },
    cors: {
      enabled: {
        type: Boolean,
        default: true,
      },
      allowedOrigins: [{
        type: String,
        default: ['http://localhost:3000'],
      }],
      allowCredentials: {
        type: Boolean,
        default: true,
      },
    },
  },

  // System maintenance
  maintenance: {
    maintenanceMode: {
      type: Boolean,
      default: false,
    },
    maintenanceMessage: {
      type: String,
      default: 'System is currently under maintenance. Please try again later.',
    },
    scheduledMaintenance: [{
      title: String,
      description: String,
      startTime: Date,
      endTime: Date,
      notifyUsers: {
        type: Boolean,
        default: true,
      },
      createdAt: {
        type: Date,
        default: Date.now,
      },
    }],
    autoBackup: {
      enabled: {
        type: Boolean,
        default: true,
      },
      frequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly'],
        default: 'daily',
      },
      retentionCount: {
        type: Number,
        min: 1,
        max: 100,
        default: 30,
      },
      location: {
        type: String,
        default: 'local',
      },
    },
  },

  // Metadata
  lastModified: {
    type: Date,
    default: Date.now,
  },
  modifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  version: {
    type: Number,
    default: 1,
  },
}, {
  timestamps: true,
});

// Indexes
systemSettingsSchema.index({ settingsVersion: 1 });
systemSettingsSchema.index({ lastModified: -1 });

// Pre-save middleware to update version and lastModified
systemSettingsSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.version += 1;
    this.lastModified = new Date();
  }
  next();
});

// Static method to get current settings
systemSettingsSchema.statics.getCurrentSettings = async function() {
  let settings = await this.findOne().sort({ createdAt: -1 });
  
  if (!settings) {
    // Create default settings if none exist
    settings = new this({});
    await settings.save();
  }
  
  return settings;
};

// Method to validate settings
systemSettingsSchema.methods.validateSettings = function() {
  const errors = [];
  
  // Validate email settings if enabled
  if (this.systemNotifications.emailSettings.enabled) {
    if (!this.systemNotifications.emailSettings.smtpHost) {
      errors.push('SMTP host is required when email notifications are enabled');
    }
    if (!this.systemNotifications.emailSettings.fromAddress) {
      errors.push('From address is required when email notifications are enabled');
    }
  }
  
  // Validate retention policies
  if (this.logRetention.retentionPolicies.length === 0) {
    errors.push('At least one retention policy is required');
  }
  
  return errors;
};

module.exports = mongoose.model('SystemSettings', systemSettingsSchema);
