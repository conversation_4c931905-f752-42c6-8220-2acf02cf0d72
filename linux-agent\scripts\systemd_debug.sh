#!/bin/bash
"""
Systemd Service Debug Helper

This script helps diagnose systemd service issues for the Linux Log Agent.
"""

SERVICE_NAME="linux-agent"
LOG_DIR="/var/log/linux-log-agent"
CONFIG_PATH="/opt/linux-agent/config/default_config.yaml"

echo "=== Linux Log Agent Systemd Debug Helper ==="
echo

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "This script should be run as root for full diagnostics"
   echo "Some checks may fail or be incomplete"
   echo
fi

echo "1. Service Status:"
systemctl status $SERVICE_NAME --no-pager -l
echo

echo "2. Service Logs (last 50 lines):"
journalctl -u $SERVICE_NAME -n 50 --no-pager
echo

echo "3. Service Configuration:"
echo "Service file: /etc/systemd/system/${SERVICE_NAME}.service"
if [ -f "/etc/systemd/system/${SERVICE_NAME}.service" ]; then
    echo "✓ Service file exists"
    cat /etc/systemd/system/${SERVICE_NAME}.service
else
    echo "✗ Service file not found"
fi
echo

echo "4. File System Checks:"
echo "Config file: $CONFIG_PATH"
if [ -f "$CONFIG_PATH" ]; then
    echo "✓ Config file exists"
    echo "  Permissions: $(ls -la $CONFIG_PATH)"
else
    echo "✗ Config file not found"
fi
echo

echo "Log directory: $LOG_DIR"
if [ -d "$LOG_DIR" ]; then
    echo "✓ Log directory exists"
    echo "  Permissions: $(ls -ld $LOG_DIR)"
    echo "  Contents:"
    ls -la $LOG_DIR
else
    echo "✗ Log directory not found"
fi
echo

echo "5. User and Group Check:"
if id "linux-log-agent" &>/dev/null; then
    echo "✓ User 'linux-log-agent' exists"
    id linux-log-agent
else
    echo "✗ User 'linux-log-agent' does not exist"
fi

if getent group "linux-log-agent" &>/dev/null; then
    echo "✓ Group 'linux-log-agent' exists"
    getent group linux-log-agent
else
    echo "✗ Group 'linux-log-agent' does not exist"
fi
echo

echo "6. Python Environment Check:"
echo "Python executable: $(which python3)"
echo "Python version: $(python3 --version)"
echo

echo "7. Agent Directory Check:"
AGENT_DIR="/opt/linux-agent"
if [ -d "$AGENT_DIR" ]; then
    echo "✓ Agent directory exists: $AGENT_DIR"
    echo "  Permissions: $(ls -ld $AGENT_DIR)"
    echo "  Contents:"
    ls -la $AGENT_DIR
else
    echo "✗ Agent directory not found: $AGENT_DIR"
fi
echo

echo "8. System Log Access Check:"
SYSTEM_LOGS=("/var/log/syslog" "/var/log/auth.log" "/var/log/kern.log" "/var/log/messages")
for log_file in "${SYSTEM_LOGS[@]}"; do
    if [ -f "$log_file" ]; then
        if sudo -u linux-log-agent test -r "$log_file" 2>/dev/null; then
            echo "✓ $log_file - readable by linux-log-agent user"
        else
            echo "✗ $log_file - NOT readable by linux-log-agent user"
        fi
    else
        echo "- $log_file - does not exist"
    fi
done
echo

echo "9. Manual Service Test:"
echo "To test the service manually, run:"
echo "sudo -u linux-log-agent /usr/bin/python3 /opt/linux-agent/service/systemd_service.py --config $CONFIG_PATH"
echo

echo "10. Debug Script Test:"
echo "To run the debug script, use:"
echo "sudo -u linux-log-agent /usr/bin/python3 /opt/linux-agent/scripts/debug_service.py $CONFIG_PATH"
echo

echo "=== Debug Complete ==="
echo "Check the output above for any ✗ marks indicating issues."
echo "Service logs are available with: journalctl -u $SERVICE_NAME -f"
