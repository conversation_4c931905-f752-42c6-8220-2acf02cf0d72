const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  reportId: {
    type: String,
    required: true,
    unique: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000,
  },
  type: {
    type: String,
    enum: ['analytics', 'compliance', 'custom'],
    required: true,
    index: true,
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  lastRunAt: {
    type: Date,
    default: null,
  },
  content: {
    layout: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    components: {
      type: Array,
      default: [],
    },
    dataSourceConfig: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    filters: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    parameters: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
  },
  sharing: {
    isPublic: {
      type: Boolean,
      default: false,
    },
    sharedWith: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
      role: {
        type: String,
        enum: ['viewer', 'editor'],
        default: 'viewer',
      },
    }],
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 50,
  }],
  folder: {
    type: String,
    default: 'My Reports',
    maxlength: 100,
  },
  version: {
    type: Number,
    default: 1,
  },
  versionHistory: [{
    version: Number,
    timestamp: {
      type: Date,
      default: Date.now,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    changeDescription: {
      type: String,
      maxlength: 500,
    },
  }],
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
    index: true,
  },
  metadata: {
    executionCount: {
      type: Number,
      default: 0,
    },
    avgExecutionTime: {
      type: Number,
      default: 0,
    },
    lastError: {
      type: String,
      default: null,
    },
    dataSize: {
      type: Number,
      default: 0,
    },
  },
}, {
  timestamps: true,
});

// Indexes
reportSchema.index({ owner: 1, createdAt: -1 });
reportSchema.index({ type: 1, status: 1 });
reportSchema.index({ 'sharing.isPublic': 1, status: 1 });
reportSchema.index({ tags: 1 });
reportSchema.index({ folder: 1, owner: 1 });

// Text index for search
reportSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text',
});

// Pre-save middleware to update version and timestamp
reportSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedAt = new Date();
    if (this.isModified('content')) {
      this.version += 1;
      this.versionHistory.push({
        version: this.version,
        timestamp: new Date(),
        user: this.owner,
        changeDescription: 'Report content updated',
      });
    }
  }
  next();
});

// Method to check if user can access report
reportSchema.methods.canAccess = function(userId) {
  // Owner can always access
  if (this.owner.toString() === userId.toString()) {
    return true;
  }
  
  // Check if public
  if (this.sharing.isPublic && this.status === 'published') {
    return true;
  }
  
  // Check if shared with user
  return this.sharing.sharedWith.some(share => 
    share.user.toString() === userId.toString()
  );
};

// Method to check if user can edit report
reportSchema.methods.canEdit = function(userId) {
  // Owner can always edit
  if (this.owner.toString() === userId.toString()) {
    return true;
  }
  
  // Check if shared with edit permissions
  return this.sharing.sharedWith.some(share => 
    share.user.toString() === userId.toString() && share.role === 'editor'
  );
};

module.exports = mongoose.model('Report', reportSchema);
