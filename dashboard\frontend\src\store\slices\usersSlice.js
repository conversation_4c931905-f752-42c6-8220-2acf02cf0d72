import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../services/api'

// Async thunks for API calls
export const fetchUsers = createAsyncThunk(
  'users/fetchUsers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/users', { params })
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch users')
    }
  }
)

export const fetchUserStats = createAsyncThunk(
  'users/fetchUserStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/users/stats')
      return response.data.data.stats
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch user statistics')
    }
  }
)

export const createUser = createAsyncThunk(
  'users/createUser',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await api.post('/users', userData)
      return response.data.data.user
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create user')
    }
  }
)

export const updateUser = createAsyncThunk(
  'users/updateUser',
  async ({ id, userData }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/users/${id}`, userData)
      return response.data.data.user
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update user')
    }
  }
)

export const updateUserStatus = createAsyncThunk(
  'users/updateUserStatus',
  async ({ id, status }, { rejectWithValue }) => {
    try {
      const response = await api.put(`/users/${id}/status`, { status })
      return response.data.data.user
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update user status')
    }
  }
)

export const resetUserPassword = createAsyncThunk(
  'users/resetUserPassword',
  async ({ id, newPassword, forceChange }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/users/${id}/reset-password`, {
        newPassword,
        forceChange,
      })
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reset password')
    }
  }
)

export const deleteUser = createAsyncThunk(
  'users/deleteUser',
  async (id, { rejectWithValue }) => {
    try {
      await api.delete(`/users/${id}`)
      return id
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete user')
    }
  }
)

export const fetchRoles = createAsyncThunk(
  'users/fetchRoles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/roles/public')
      return response.data.data.roles
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch roles')
    }
  }
)

export const fetchAllRoles = createAsyncThunk(
  'users/fetchAllRoles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/roles')
      return response.data.data.roles
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch all roles')
    }
  }
)

export const fetchPermissions = createAsyncThunk(
  'users/fetchPermissions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/roles/permissions')
      return response.data.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch permissions')
    }
  }
)

const initialState = {
  users: [],
  stats: null,
  roles: [],
  permissions: [],
  groupedPermissions: {},
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 20,
    hasNextPage: false,
    hasPrevPage: false,
  },
  filters: {
    search: '',
    role: '',
    status: '',
  },
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
  selectedUsers: [],
}

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {
        search: '',
        role: '',
        status: '',
      }
    },
    setSelectedUsers: (state, action) => {
      state.selectedUsers = action.payload
    },
    clearSelectedUsers: (state) => {
      state.selectedUsers = []
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Users
      .addCase(fetchUsers.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false
        state.users = action.payload.users || []
        state.pagination = action.payload.pagination || state.pagination
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Fetch User Stats
      .addCase(fetchUserStats.pending, (state) => {
        state.isLoading = true
      })
      .addCase(fetchUserStats.fulfilled, (state, action) => {
        state.isLoading = false
        state.stats = action.payload
      })
      .addCase(fetchUserStats.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })

      // Create User
      .addCase(createUser.pending, (state) => {
        state.isCreating = true
        state.error = null
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.isCreating = false
        state.users.unshift(action.payload)
      })
      .addCase(createUser.rejected, (state, action) => {
        state.isCreating = false
        state.error = action.payload
      })

      // Update User
      .addCase(updateUser.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isUpdating = false
        const index = state.users.findIndex(user => user._id === action.payload._id)
        if (index !== -1) {
          state.users[index] = action.payload
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload
      })

      // Update User Status
      .addCase(updateUserStatus.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(updateUserStatus.fulfilled, (state, action) => {
        state.isUpdating = false
        const index = state.users.findIndex(user => user._id === action.payload._id)
        if (index !== -1) {
          state.users[index] = action.payload
        }
      })
      .addCase(updateUserStatus.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload
      })

      // Reset User Password
      .addCase(resetUserPassword.pending, (state) => {
        state.isUpdating = true
        state.error = null
      })
      .addCase(resetUserPassword.fulfilled, (state) => {
        state.isUpdating = false
      })
      .addCase(resetUserPassword.rejected, (state, action) => {
        state.isUpdating = false
        state.error = action.payload
      })

      // Delete User
      .addCase(deleteUser.pending, (state) => {
        state.isDeleting = true
        state.error = null
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isDeleting = false
        state.users = state.users.filter(user => user._id !== action.payload)
        state.selectedUsers = state.selectedUsers.filter(id => id !== action.payload)
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isDeleting = false
        state.error = action.payload
      })

      // Fetch Roles
      .addCase(fetchRoles.fulfilled, (state, action) => {
        state.roles = action.payload
      })

      // Fetch All Roles (admin)
      .addCase(fetchAllRoles.fulfilled, (state, action) => {
        state.roles = action.payload
      })

      // Fetch Permissions
      .addCase(fetchPermissions.fulfilled, (state, action) => {
        state.permissions = action.payload.permissions || []
        state.groupedPermissions = action.payload.groupedPermissions || {}
      })
  },
})

export const {
  clearError,
  setFilters,
  clearFilters,
  setSelectedUsers,
  clearSelectedUsers
} = usersSlice.actions

export default usersSlice.reducer
