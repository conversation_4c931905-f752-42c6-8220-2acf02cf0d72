const Log = require('../models/Log');
const Alert = require('../models/Alert');
const User = require('../models/User');
const moment = require('moment');

class AnalyticsService {
  /**
   * Get security posture overview
   */
  async getSecurityPosture(timeRange = '30d') {
    const endDate = new Date();
    const startDate = this.getStartDate(timeRange, endDate);

    try {
      // Get log statistics
      const logStats = await this.getLogStatistics(startDate, endDate);
      
      // Get alert statistics
      const alertStats = await this.getAlertStatistics(startDate, endDate);
      
      // Get threat intelligence
      const threatIntel = await this.getThreatIntelligence(startDate, endDate);
      
      // Calculate security score
      const securityScore = this.calculateSecurityScore(logStats, alertStats);
      
      return {
        securityScore,
        timeRange: {
          start: startDate,
          end: endDate,
        },
        logStatistics: logStats,
        alertStatistics: alertStats,
        threatIntelligence: threatIntel,
        recommendations: this.generateRecommendations(logStats, alertStats),
      };
    } catch (error) {
      throw new Error(`Failed to get security posture: ${error.message}`);
    }
  }

  /**
   * Get incident analytics
   */
  async getIncidentAnalytics(timeRange = '30d') {
    const endDate = new Date();
    const startDate = this.getStartDate(timeRange, endDate);

    try {
      // Incident overview by severity
      const incidentsBySeverity = await Alert.aggregate([
        {
          $match: {
            triggeredAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: '$severity',
            count: { $sum: 1 },
            avgResolutionTime: {
              $avg: {
                $cond: [
                  { $ne: ['$resolvedAt', null] },
                  { $subtract: ['$resolvedAt', '$triggeredAt'] },
                  null,
                ],
              },
            },
          },
        },
        {
          $sort: { count: -1 },
        },
      ]);

      // Incident trend over time
      const incidentTrend = await Alert.aggregate([
        {
          $match: {
            triggeredAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: '%Y-%m-%d', date: '$triggeredAt' } },
              severity: '$severity',
            },
            count: { $sum: 1 },
          },
        },
        {
          $sort: { '_id.date': 1 },
        },
      ]);

      // Top incident categories
      const topCategories = await Alert.aggregate([
        {
          $match: {
            triggeredAt: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: '$triggerData.category',
            count: { $sum: 1 },
            severityBreakdown: {
              $push: '$severity',
            },
          },
        },
        {
          $sort: { count: -1 },
        },
        {
          $limit: 10,
        },
      ]);

      // MTTD and MTTR calculations
      const responseMetrics = await Alert.aggregate([
        {
          $match: {
            triggeredAt: { $gte: startDate, $lte: endDate },
            acknowledgedAt: { $ne: null },
          },
        },
        {
          $group: {
            _id: null,
            avgMTTD: {
              $avg: { $subtract: ['$acknowledgedAt', '$triggeredAt'] },
            },
            avgMTTR: {
              $avg: {
                $cond: [
                  { $ne: ['$resolvedAt', null] },
                  { $subtract: ['$resolvedAt', '$triggeredAt'] },
                  null,
                ],
              },
            },
            totalIncidents: { $sum: 1 },
            resolvedIncidents: {
              $sum: {
                $cond: [{ $ne: ['$resolvedAt', null] }, 1, 0],
              },
            },
          },
        },
      ]);

      return {
        timeRange: { start: startDate, end: endDate },
        incidentsBySeverity,
        incidentTrend,
        topCategories,
        responseMetrics: responseMetrics[0] || {
          avgMTTD: 0,
          avgMTTR: 0,
          totalIncidents: 0,
          resolvedIncidents: 0,
        },
      };
    } catch (error) {
      throw new Error(`Failed to get incident analytics: ${error.message}`);
    }
  }

  /**
   * Get threat intelligence data
   */
  async getThreatIntelligence(startDate, endDate) {
    try {
      // Geographic attack origins
      const geoAttacks = await Log.aggregate([
        {
          $match: {
            timestamp: { $gte: startDate, $lte: endDate },
            logLevel: { $in: ['critical', 'error'] },
            'additionalFields.sourceIP': { $exists: true },
          },
        },
        {
          $group: {
            _id: '$additionalFields.sourceCountry',
            count: { $sum: 1 },
            uniqueIPs: { $addToSet: '$additionalFields.sourceIP' },
          },
        },
        {
          $project: {
            country: '$_id',
            attackCount: '$count',
            uniqueIPCount: { $size: '$uniqueIPs' },
          },
        },
        {
          $sort: { attackCount: -1 },
        },
        {
          $limit: 20,
        },
      ]);

      // Top attack vectors
      const attackVectors = await Log.aggregate([
        {
          $match: {
            timestamp: { $gte: startDate, $lte: endDate },
            logLevel: { $in: ['critical', 'error'] },
            'additionalFields.attackType': { $exists: true },
          },
        },
        {
          $group: {
            _id: '$additionalFields.attackType',
            count: { $sum: 1 },
            severity: { $avg: '$severity' },
          },
        },
        {
          $sort: { count: -1 },
        },
        {
          $limit: 10,
        },
      ]);

      // Emerging threats (new patterns in last 7 days)
      const recentDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
      const emergingThreats = await Log.aggregate([
        {
          $match: {
            timestamp: { $gte: recentDate, $lte: endDate },
            logLevel: { $in: ['critical', 'error'] },
          },
        },
        {
          $group: {
            _id: {
              pattern: '$additionalFields.pattern',
              source: '$source',
            },
            count: { $sum: 1 },
            firstSeen: { $min: '$timestamp' },
            lastSeen: { $max: '$timestamp' },
          },
        },
        {
          $match: {
            count: { $gte: 5 }, // Only patterns seen 5+ times
          },
        },
        {
          $sort: { count: -1 },
        },
        {
          $limit: 5,
        },
      ]);

      return {
        geoAttacks,
        attackVectors,
        emergingThreats,
      };
    } catch (error) {
      throw new Error(`Failed to get threat intelligence: ${error.message}`);
    }
  }

  /**
   * Get operational metrics
   */
  async getOperationalMetrics(timeRange = '24h') {
    const endDate = new Date();
    const startDate = this.getStartDate(timeRange, endDate);

    try {
      // Log source health
      const sourceHealth = await Log.aggregate([
        {
          $match: {
            timestamp: { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: {
              source: '$source',
              host: '$host',
            },
            count: { $sum: 1 },
            lastSeen: { $max: '$timestamp' },
            errorCount: {
              $sum: {
                $cond: [{ $eq: ['$logLevel', 'error'] }, 1, 0],
              },
            },
          },
        },
        {
          $group: {
            _id: '$_id.source',
            totalLogs: { $sum: '$count' },
            hostCount: { $sum: 1 },
            avgLogsPerHost: { $avg: '$count' },
            totalErrors: { $sum: '$errorCount' },
            lastActivity: { $max: '$lastSeen' },
          },
        },
        {
          $sort: { totalLogs: -1 },
        },
      ]);

      // User activity summary
      const userActivity = await User.aggregate([
        {
          $match: {
            'activityLog.timestamp': { $gte: startDate, $lte: endDate },
          },
        },
        {
          $unwind: '$activityLog',
        },
        {
          $match: {
            'activityLog.timestamp': { $gte: startDate, $lte: endDate },
          },
        },
        {
          $group: {
            _id: '$activityLog.action',
            count: { $sum: 1 },
            uniqueUsers: { $addToSet: '$_id' },
          },
        },
        {
          $project: {
            action: '$_id',
            count: 1,
            uniqueUserCount: { $size: '$uniqueUsers' },
          },
        },
        {
          $sort: { count: -1 },
        },
      ]);

      return {
        timeRange: { start: startDate, end: endDate },
        sourceHealth,
        userActivity,
      };
    } catch (error) {
      throw new Error(`Failed to get operational metrics: ${error.message}`);
    }
  }

  /**
   * Helper methods
   */
  getStartDate(timeRange, endDate) {
    const end = moment(endDate);
    
    switch (timeRange) {
      case '1h':
        return end.subtract(1, 'hour').toDate();
      case '24h':
        return end.subtract(24, 'hours').toDate();
      case '7d':
        return end.subtract(7, 'days').toDate();
      case '30d':
        return end.subtract(30, 'days').toDate();
      case '90d':
        return end.subtract(90, 'days').toDate();
      default:
        return end.subtract(30, 'days').toDate();
    }
  }

  async getLogStatistics(startDate, endDate) {
    return await Log.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: null,
          totalLogs: { $sum: 1 },
          criticalLogs: {
            $sum: { $cond: [{ $eq: ['$logLevel', 'critical'] }, 1, 0] },
          },
          errorLogs: {
            $sum: { $cond: [{ $eq: ['$logLevel', 'error'] }, 1, 0] },
          },
          warningLogs: {
            $sum: { $cond: [{ $eq: ['$logLevel', 'warning'] }, 1, 0] },
          },
          uniqueHosts: { $addToSet: '$host' },
          uniqueSources: { $addToSet: '$source' },
        },
      },
      {
        $project: {
          totalLogs: 1,
          criticalLogs: 1,
          errorLogs: 1,
          warningLogs: 1,
          uniqueHostCount: { $size: '$uniqueHosts' },
          uniqueSourceCount: { $size: '$uniqueSources' },
        },
      },
    ]);
  }

  async getAlertStatistics(startDate, endDate) {
    return await Alert.aggregate([
      {
        $match: {
          triggeredAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: null,
          totalAlerts: { $sum: 1 },
          criticalAlerts: {
            $sum: { $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0] },
          },
          highAlerts: {
            $sum: { $cond: [{ $eq: ['$severity', 'high'] }, 1, 0] },
          },
          resolvedAlerts: {
            $sum: { $cond: [{ $ne: ['$resolvedAt', null] }, 1, 0] },
          },
          acknowledgedAlerts: {
            $sum: { $cond: [{ $ne: ['$acknowledgedAt', null] }, 1, 0] },
          },
        },
      },
    ]);
  }

  calculateSecurityScore(logStats, alertStats) {
    // Simple scoring algorithm - can be enhanced
    let score = 100;
    
    if (logStats.length > 0) {
      const stats = logStats[0];
      const errorRate = (stats.criticalLogs + stats.errorLogs) / stats.totalLogs;
      score -= errorRate * 30; // Reduce score based on error rate
    }
    
    if (alertStats.length > 0) {
      const stats = alertStats[0];
      const criticalRate = stats.criticalAlerts / stats.totalAlerts;
      const resolutionRate = stats.resolvedAlerts / stats.totalAlerts;
      
      score -= criticalRate * 40; // Reduce score for critical alerts
      score += resolutionRate * 10; // Increase score for resolved alerts
    }
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  generateRecommendations(logStats, alertStats) {
    const recommendations = [];
    
    if (logStats.length > 0) {
      const stats = logStats[0];
      const errorRate = (stats.criticalLogs + stats.errorLogs) / stats.totalLogs;
      
      if (errorRate > 0.1) {
        recommendations.push({
          type: 'high',
          title: 'High Error Rate Detected',
          description: 'Consider investigating the root cause of frequent errors',
          action: 'Review error logs and implement fixes',
        });
      }
    }
    
    if (alertStats.length > 0) {
      const stats = alertStats[0];
      const resolutionRate = stats.resolvedAlerts / stats.totalAlerts;
      
      if (resolutionRate < 0.8) {
        recommendations.push({
          type: 'medium',
          title: 'Low Alert Resolution Rate',
          description: 'Many alerts remain unresolved',
          action: 'Review and resolve pending alerts',
        });
      }
    }
    
    return recommendations;
  }
}

module.exports = new AnalyticsService();
