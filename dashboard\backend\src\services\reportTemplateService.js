const { v4: uuidv4 } = require('uuid');
const Report = require('../models/Report');
const logger = require('../config/logger');

class ReportTemplateService {
  constructor() {
    this.defaultTemplates = [
      {
        name: 'Security Analytics Summary',
        description: 'Comprehensive security analytics report with incident trends, threat intelligence, and security posture metrics',
        type: 'analytics',
        category: 'security',
        content: {
          layout: {
            type: 'grid',
            columns: 12,
            rows: 'auto'
          },
          components: [
            {
              id: 'security-overview',
              type: 'metric',
              title: 'Security Overview',
              position: { x: 0, y: 0, w: 12, h: 2 },
              config: {
                metrics: ['total', 'critical', 'error', 'warning', 'alerts'],
                displayType: 'cards'
              }
            },
            {
              id: 'incident-trend',
              type: 'chart',
              title: 'Incident Trend Over Time',
              position: { x: 0, y: 2, w: 6, h: 4 },
              config: {
                chartType: 'line',
                timeGrouping: 'day',
                dataSource: 'incidents'
              }
            },
            {
              id: 'severity-distribution',
              type: 'chart',
              title: 'Incidents by Severity',
              position: { x: 6, y: 2, w: 6, h: 4 },
              config: {
                chartType: 'pie',
                dataSource: 'incidents',
                groupBy: 'severity'
              }
            },
            {
              id: 'top-hosts',
              type: 'table',
              title: 'Top Hosts by Activity',
              position: { x: 0, y: 6, w: 6, h: 4 },
              config: {
                columns: ['host', 'total_events', 'critical_count', 'error_count'],
                limit: 10,
                sortBy: 'total_events'
              }
            },
            {
              id: 'log-sources',
              type: 'chart',
              title: 'Log Volume by Source',
              position: { x: 6, y: 6, w: 6, h: 4 },
              config: {
                chartType: 'bar',
                dataSource: 'logs',
                groupBy: 'source'
              }
            }
          ],
          filters: {
            timeRange: '30d',
            logLevel: null,
            source: null
          }
        },
        tags: ['security', 'analytics', 'incidents', 'default']
      },
      {
        name: 'PCI DSS Compliance Report',
        description: 'Detailed PCI DSS compliance assessment with control status, evidence, and recommendations',
        type: 'compliance',
        category: 'compliance',
        content: {
          layout: {
            type: 'document',
            sections: ['overview', 'controls', 'evidence', 'recommendations']
          },
          components: [
            {
              id: 'compliance-score',
              type: 'metric',
              title: 'PCI DSS Compliance Score',
              config: {
                framework: 'pci',
                displayType: 'gauge',
                showTrend: true
              }
            },
            {
              id: 'control-status',
              type: 'table',
              title: 'Control Assessment Results',
              config: {
                framework: 'pci',
                columns: ['control_id', 'control_name', 'status', 'score', 'last_tested'],
                showEvidence: true
              }
            },
            {
              id: 'compliance-metrics',
              type: 'chart',
              title: 'Compliance Metrics',
              config: {
                chartType: 'bar',
                framework: 'pci',
                metrics: ['authentication', 'access_control', 'data_protection', 'audit']
              }
            }
          ],
          parameters: {
            framework: 'pci',
            timeRange: '30d',
            includeEvidence: true
          }
        },
        tags: ['compliance', 'pci', 'audit', 'default']
      },
      {
        name: 'Operational Metrics Dashboard',
        description: 'System operational metrics including log source health, user activity, and performance indicators',
        type: 'analytics',
        category: 'operations',
        content: {
          layout: {
            type: 'grid',
            columns: 12,
            rows: 'auto'
          },
          components: [
            {
              id: 'system-health',
              type: 'metric',
              title: 'System Health Overview',
              position: { x: 0, y: 0, w: 12, h: 2 },
              config: {
                metrics: ['active_sources', 'log_volume', 'error_rate', 'uptime'],
                displayType: 'cards'
              }
            },
            {
              id: 'log-volume-trend',
              type: 'chart',
              title: 'Log Volume Trend',
              position: { x: 0, y: 2, w: 8, h: 4 },
              config: {
                chartType: 'area',
                timeGrouping: 'hour',
                dataSource: 'logs'
              }
            },
            {
              id: 'error-rate-gauge',
              type: 'gauge',
              title: 'Error Rate',
              position: { x: 8, y: 2, w: 4, h: 4 },
              config: {
                metric: 'errorRate',
                maxValue: 100,
                thresholds: { warning: 5, critical: 10 }
              }
            },
            {
              id: 'source-health',
              type: 'table',
              title: 'Log Source Health',
              position: { x: 0, y: 6, w: 12, h: 4 },
              config: {
                columns: ['source', 'total_logs', 'error_count', 'last_activity', 'status'],
                limit: 15
              }
            }
          ],
          filters: {
            timeRange: '24h'
          }
        },
        tags: ['operations', 'monitoring', 'performance', 'default']
      },
      {
        name: 'Executive Security Summary',
        description: 'High-level security summary for executive reporting with key metrics and trends',
        type: 'analytics',
        category: 'executive',
        content: {
          layout: {
            type: 'executive',
            sections: ['summary', 'trends', 'recommendations']
          },
          components: [
            {
              id: 'security-score',
              type: 'metric',
              title: 'Overall Security Score',
              config: {
                displayType: 'large-number',
                showTrend: true,
                trendPeriod: '30d'
              }
            },
            {
              id: 'key-metrics',
              type: 'metric',
              title: 'Key Security Metrics',
              config: {
                metrics: ['critical_incidents', 'mttr', 'compliance_score', 'threat_level'],
                displayType: 'executive-cards'
              }
            },
            {
              id: 'monthly-trend',
              type: 'chart',
              title: 'Security Trend (Last 6 Months)',
              config: {
                chartType: 'line',
                timeGrouping: 'month',
                period: '6m',
                metrics: ['incidents', 'compliance_score']
              }
            }
          ],
          parameters: {
            timeRange: '30d',
            executiveView: true
          }
        },
        tags: ['executive', 'summary', 'high-level', 'default']
      }
    ];
  }

  async initializeDefaultTemplates(userId) {
    try {
      logger.info('Initializing default report templates...');
      
      for (const template of this.defaultTemplates) {
        // Check if template already exists
        const existingTemplate = await Report.findOne({
          name: template.name,
          type: template.type,
          'sharing.isPublic': true
        });

        if (!existingTemplate) {
          const report = new Report({
            reportId: uuidv4(),
            name: template.name,
            description: template.description,
            type: template.type,
            owner: userId,
            content: template.content,
            tags: template.tags,
            folder: 'Default Templates',
            status: 'published',
            sharing: {
              isPublic: true,
              sharedWith: []
            }
          });

          await report.save();
          logger.info(`Created default template: ${template.name}`);
        }
      }

      logger.info('Default report templates initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize default templates:', error);
      throw error;
    }
  }

  async getTemplatesByCategory(category) {
    try {
      const query = {
        'sharing.isPublic': true,
        status: 'published',
        folder: 'Default Templates'
      };

      if (category) {
        query['content.category'] = category;
      }

      const templates = await Report.find(query)
        .populate('owner', 'username firstName lastName')
        .sort({ createdAt: -1 })
        .lean();

      return templates;
    } catch (error) {
      logger.error('Failed to get templates by category:', error);
      throw error;
    }
  }

  async createTemplateFromReport(reportId, userId, templateData) {
    try {
      const sourceReport = await Report.findById(reportId);
      
      if (!sourceReport) {
        throw new Error('Source report not found');
      }

      // Check if user has access to the source report
      if (!sourceReport.canAccess(userId)) {
        throw new Error('Access denied to source report');
      }

      const template = new Report({
        reportId: uuidv4(),
        name: templateData.name || `${sourceReport.name} Template`,
        description: templateData.description || `Template based on ${sourceReport.name}`,
        type: sourceReport.type,
        owner: userId,
        content: sourceReport.content,
        tags: [...(sourceReport.tags || []), 'template', 'custom'],
        folder: templateData.folder || 'My Templates',
        status: 'published',
        sharing: templateData.sharing || { isPublic: false, sharedWith: [] }
      });

      await template.save();
      logger.info(`Created template from report: ${sourceReport.name}`);
      
      return template;
    } catch (error) {
      logger.error('Failed to create template from report:', error);
      throw error;
    }
  }

  getDefaultTemplateConfig(templateName) {
    return this.defaultTemplates.find(t => t.name === templateName);
  }
}

module.exports = new ReportTemplateService();
