const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';
const timestamp = Date.now();

// Simple test to trigger the "Multiple Failed Login Attempts" rule
const failedLoginLogs = Array.from({ length: 6 }, (_, i) => ({
  logId: `test-${timestamp}-${i + 1}`,
  timestamp: new Date().toISOString(),
  source: 'Security',
  sourceType: 'security',
  host: 'web-server-01',
  logLevel: 'error',
  message: `<PERSON><PERSON> failed for user admin from IP ************* - invalid credentials (attempt ${i + 1})`,
  rawData: `AUTH_FAILED: user=admin, ip=*************, reason=invalid_password, attempt=${i + 1}`,
  additionalFields: {
    eventId: 4625,
    eventCategory: 'Authentication',
    eventType: 'Login Failure'
  }
}));

async function testAlerts() {
  console.log('Testing alert system with failed login attempts...');
  console.log(`Sending ${failedLoginLogs.length} failed login logs to trigger alert`);
  
  try {
    // Send logs one by one to simulate real-time events
    for (let i = 0; i < failedLoginLogs.length; i++) {
      const log = failedLoginLogs[i];
      
      console.log(`Sending log ${i + 1}/${failedLoginLogs.length}: ${log.message}`);
      
      const response = await axios.post(`${API_BASE}/logs`, {
        logs: [log]
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'test-api-key-12345'
        }
      });
      
      console.log(`✓ Log sent successfully`);
      
      // Wait a bit between logs to allow correlation engine to process
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n✓ All test logs sent successfully!');
    console.log('\nWaiting 5 seconds for correlation engine to process...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\nChecking for triggered alerts...');
    console.log('Run this command to check alerts:');
    console.log('docker exec dashboard-mongodb-1 mongosh exlog --eval "db.alerts.find({}, {name: 1, severity: 1, status: 1, triggeredAt: 1}).pretty()"');
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the test
testAlerts();
