#!/bin/bash

# ExLog Network Access Test Script
# This script helps test network connectivity to the ExLog application

echo "=== ExLog Network Access Test ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Function to get local IP addresses
get_local_ips() {
    local ips=()
    
    # Try different methods to get IP addresses
    if command -v ip &> /dev/null; then
        # Linux with ip command
        while IFS= read -r line; do
            ips+=("$line")
        done < <(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
        
        # Also get all interface IPs
        while IFS= read -r line; do
            if [[ ! " ${ips[@]} " =~ " ${line} " ]]; then
                ips+=("$line")
            fi
        done < <(ip addr show | grep -oP 'inet \K[0-9.]+' | grep -v '127.0.0.1' | grep -v '169.254.')
        
    elif command -v ifconfig &> /dev/null; then
        # macOS/BSD with ifconfig
        while IFS= read -r line; do
            ips+=("$line")
        done < <(ifconfig | grep -oE 'inet [0-9.]+' | grep -v '127.0.0.1' | grep -v '169.254.' | awk '{print $2}')
    fi
    
    # Remove duplicates and filter private networks
    local unique_ips=()
    for ip in "${ips[@]}"; do
        if [[ ! " ${unique_ips[@]} " =~ " ${ip} " ]] && [[ "$ip" =~ ^(192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.) ]]; then
            unique_ips+=("$ip")
        fi
    done
    
    printf '%s\n' "${unique_ips[@]}"
}

# Function to test HTTP connectivity
test_http_connection() {
    local url="$1"
    local description="$2"
    
    if curl -s --max-time 10 --fail "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $description - OK${NC}"
        return 0
    else
        echo -e "${RED}❌ $description - Connection failed${NC}"
        return 1
    fi
}

# Function to test Docker containers
test_docker_containers() {
    echo -e "${YELLOW}Checking Docker containers...${NC}"
    
    if ! command -v docker-compose &> /dev/null && ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker not available${NC}"
        return 1
    fi
    
    # Try docker-compose ps
    if command -v docker-compose &> /dev/null; then
        local containers
        containers=$(docker-compose ps --services 2>/dev/null)
        
        if [ -n "$containers" ]; then
            while IFS= read -r service; do
                local status
                status=$(docker-compose ps "$service" 2>/dev/null | tail -n +3 | awk '{print $4}')
                if [[ "$status" == *"Up"* ]]; then
                    echo -e "${GREEN}✅ $service: Running${NC}"
                else
                    echo -e "${RED}❌ $service: $status${NC}"
                fi
            done <<< "$containers"
        else
            echo -e "${RED}❌ No containers found. Run 'docker-compose up -d' first.${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ docker-compose not available${NC}"
        return 1
    fi
    
    return 0
}

# Main test execution
echo -e "${CYAN}1. Checking Docker containers...${NC}"
if ! test_docker_containers; then
    echo ""
    echo -e "${YELLOW}Please start the Docker containers first:${NC}"
    echo -e "${WHITE}  docker-compose up -d${NC}"
    echo ""
    exit 1
fi
echo ""

echo -e "${CYAN}2. Getting local IP addresses...${NC}"
mapfile -t ip_addresses < <(get_local_ips)

if [ ${#ip_addresses[@]} -eq 0 ]; then
    echo -e "${RED}❌ No network IP addresses found${NC}"
    exit 1
fi

echo -e "${YELLOW}Found IP addresses:${NC}"
for ip in "${ip_addresses[@]}"; do
    echo -e "${WHITE}  - $ip${NC}"
done
echo ""

echo -e "${CYAN}3. Testing localhost access...${NC}"
localhost_ok=true

declare -a localhost_tests=(
    "http://localhost/health|Nginx Health Check"
    "http://localhost/api/v1/health|API Health Check"
    "http://localhost:3000/health|Frontend Health Check"
    "http://localhost:5000/health|Backend Direct Access"
)

for test in "${localhost_tests[@]}"; do
    IFS='|' read -r url desc <<< "$test"
    if ! test_http_connection "$url" "$desc"; then
        localhost_ok=false
    fi
done
echo ""

echo -e "${CYAN}4. Testing network IP access...${NC}"
network_ok=true

for ip in "${ip_addresses[@]}"; do
    echo -e "${YELLOW}Testing IP: $ip${NC}"
    
    declare -a network_tests=(
        "http://$ip/health|  Nginx Health Check"
        "http://$ip/api/v1/health|  API Health Check"
        "http://$ip:3000/health|  Frontend Direct Access"
        "http://$ip:5000/health|  Backend Direct Access"
    )
    
    for test in "${network_tests[@]}"; do
        IFS='|' read -r url desc <<< "$test"
        if ! test_http_connection "$url" "$desc"; then
            network_ok=false
        fi
    done
    echo ""
done

# Summary
echo -e "${GREEN}=== Test Summary ===${NC}"
echo ""

if [ "$localhost_ok" = true ]; then
    echo -e "${GREEN}✅ Localhost access: Working${NC}"
else
    echo -e "${RED}❌ Localhost access: Issues detected${NC}"
fi

if [ "$network_ok" = true ]; then
    echo -e "${GREEN}✅ Network access: Working${NC}"
else
    echo -e "${RED}❌ Network access: Issues detected${NC}"
fi

echo ""
echo -e "${GREEN}=== Access URLs ===${NC}"
echo ""
echo -e "${YELLOW}Localhost access:${NC}"
echo -e "${WHITE}  Dashboard: http://localhost${NC}"
echo -e "${WHITE}  API: http://localhost/api/v1${NC}"
echo ""

if [ ${#ip_addresses[@]} -gt 0 ]; then
    echo -e "${YELLOW}Network access:${NC}"
    for ip in "${ip_addresses[@]}"; do
        echo -e "${WHITE}  Dashboard: http://$ip${NC}"
        echo -e "${WHITE}  API: http://$ip/api/v1${NC}"
    done
    echo ""
    echo -e "${CYAN}Default login: <EMAIL> / Admin123!${NC}"
fi

echo ""
echo -e "${GREEN}=== Troubleshooting ===${NC}"
echo ""

if [ "$localhost_ok" != true ] || [ "$network_ok" != true ]; then
    echo -e "${YELLOW}If you're experiencing issues:${NC}"
    echo -e "${WHITE}1. Check firewall settings on this computer${NC}"
    echo -e "${WHITE}2. Verify Docker containers are running: docker-compose ps${NC}"
    echo -e "${WHITE}3. Check container logs: docker-compose logs [service-name]${NC}"
    echo -e "${WHITE}4. Restart services: docker-compose restart${NC}"
    echo -e "${WHITE}5. Rebuild if needed: docker-compose down && docker-compose up --build -d${NC}"
    echo ""
    echo -e "${CYAN}For detailed troubleshooting, see NETWORKING_CONFIGURATION.md${NC}"
fi

echo -e "${GREEN}Test completed!${NC}"
