const axios = require('axios');

const API_BASE = 'http://localhost:5000/api/v1';

// You'll need to get a valid JWT token from the frontend or login
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODQ3NmZkZTZkZDA1MDQ4NjRkODYxZTAiLCJpYXQiOjE3MzM4MDI0NzQsImV4cCI6MTczMzgwNjA3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testAlertsAPI() {
  try {
    console.log('Testing alerts API...');
    
    // Test 1: Get alerts without filters
    console.log('\n1. Testing GET /api/v1/alerts');
    const response1 = await axios.get(`${API_BASE}/alerts`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    console.log('✓ Success:', response1.data.data.pagination);
    console.log(`Found ${response1.data.data.alerts.length} alerts`);
    
    // Test 2: Get alerts with empty filters (like the frontend sends)
    console.log('\n2. Testing GET /api/v1/alerts with empty filters');
    const response2 = await axios.get(`${API_BASE}/alerts?page=1&limit=50&status=&severity=&timeRange=24h`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    console.log('✓ Success:', response2.data.data.pagination);
    console.log(`Found ${response2.data.data.alerts.length} alerts`);
    
    // Test 3: Get alert statistics
    console.log('\n3. Testing GET /api/v1/alerts/statistics');
    const response3 = await axios.get(`${API_BASE}/alerts/statistics`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    });
    console.log('✓ Success:', response3.data.data.statistics);
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testAlertsAPI();
