# Rate Limiting Fix - ExLog Dashboard

## Problem Description

The ExLog Dashboard was experiencing a critical issue where users would encounter **429 "Too Many Requests"** errors after using the application for a short period, especially when navigating between tabs and pages frequently. This was causing the backend to stop responding and making the application unusable.

### Root Cause Analysis

The issue was caused by overly restrictive rate limiting configuration:

1. **Low Rate Limits**: The original configuration allowed only **100 requests per 15 minutes** per IP address
2. **All API Calls Rate Limited**: Every API call, including authenticated requests, was subject to the same restrictive rate limit
3. **Frequent API Calls**: The application makes many legitimate API calls:
   - Dashboard auto-refresh every 30 seconds (now optimized to 2 minutes)
   - Navigation between pages triggers multiple API calls
   - Each page load requires several API endpoints (alerts, logs, users, etc.)
   - Authentication validation calls

## Solution Implemented

### 1. Tiered Rate Limiting Strategy

Implemented a three-tier rate limiting approach:

#### Tier 1: Authentication Endpoints (Strictest)
- **Endpoints**: `/api/v1/auth/login`, `/api/v1/auth/register`
- **Limit**: 20 requests per 15 minutes per IP
- **Purpose**: Prevent brute force attacks

#### Tier 2: General API Endpoints (Moderate)
- **Endpoints**: All `/api/` endpoints
- **Limit**: 1000 requests per 15 minutes per IP
- **Skip Logic**: Bypassed for requests with valid Authorization headers
- **Purpose**: Protect against abuse while allowing normal usage

#### Tier 3: Authenticated API Endpoints (Lenient)
- **Endpoints**: All authenticated routes
- **Limit**: 200 requests per minute per IP
- **Purpose**: Allow heavy usage for legitimate authenticated users

### 2. Backend Changes

#### File: `backend/src/index.js`
```javascript
// Strict rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs, // 15 minutes
  max: config.security.authRateLimitMax, // 20 requests
  message: 'Too many authentication attempts from this IP, please try again later.',
});

// Moderate rate limiting for general API endpoints
const apiLimiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs, // 15 minutes
  max: config.security.rateLimitMax, // 1000 requests
  skip: (req) => {
    // Skip rate limiting for authenticated users
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    return !!token;
  }
});

// Lenient rate limiting for authenticated API calls
const authenticatedLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: config.security.authenticatedRateLimitMax, // 200 requests
});
```

#### File: `backend/src/config/index.js`
```javascript
security: {
  rateLimitMax: 1000, // Increased from 100
  authRateLimitMax: 20, // New: Strict limit for auth endpoints
  authenticatedRateLimitMax: 200, // New: Per minute for authenticated users
}
```

### 3. Frontend Optimizations

#### File: `frontend/src/pages/Dashboard/Dashboard.jsx`
- **Auto-refresh interval**: Increased from 30 seconds to 2 minutes
- **Purpose**: Reduce unnecessary API calls while maintaining data freshness

#### File: `frontend/src/services/api.js`
- **Enhanced error handling**: Better handling of 429 rate limit errors
- **User-friendly messages**: Clear error messages for rate limiting

### 4. Environment Configuration

#### File: `.env.example`
```bash
# Rate limiting configuration
RATE_LIMIT_MAX=1000
AUTH_RATE_LIMIT_MAX=20
AUTHENTICATED_RATE_LIMIT_MAX=200
```

## Benefits of the Solution

### 1. **Improved User Experience**
- No more unexpected 429 errors during normal usage
- Smooth navigation between pages and tabs
- Uninterrupted dashboard functionality

### 2. **Maintained Security**
- Authentication endpoints still protected against brute force
- General API endpoints protected against abuse
- Authenticated users get appropriate access levels

### 3. **Scalability**
- Configuration supports higher user loads
- Tiered approach allows fine-tuning per endpoint type
- Environment variables for easy adjustment

### 4. **Performance Optimization**
- Reduced unnecessary API calls (dashboard refresh interval)
- Better resource utilization
- Improved application responsiveness

## Testing and Validation

### Before Fix
- Users experienced 429 errors after 5-10 minutes of usage
- Application became unusable during normal navigation
- Backend stopped responding to legitimate requests

### After Fix
- Users can navigate freely without rate limiting issues
- Dashboard functions normally with extended usage
- Authentication remains secure with appropriate limits

## Configuration Details

### Rate Limit Values
| Endpoint Type | Window | Limit | Purpose |
|---------------|--------|-------|---------|
| Authentication | 15 min | 20 | Prevent brute force |
| General API | 15 min | 1000 | Normal usage protection |
| Authenticated | 1 min | 200 | Heavy usage support |

### Environment Variables
- `RATE_LIMIT_MAX`: General API rate limit (default: 1000)
- `AUTH_RATE_LIMIT_MAX`: Authentication rate limit (default: 20)
- `AUTHENTICATED_RATE_LIMIT_MAX`: Authenticated user rate limit (default: 200)

## Deployment Instructions

1. **Build and deploy** the updated containers:
   ```bash
   docker compose build backend frontend
   docker compose up -d
   ```

2. **Verify** the application is working:
   - Access the dashboard at http://localhost:8080
   - Navigate between different pages
   - Monitor for any rate limiting errors

3. **Monitor** the application logs for any issues:
   ```bash
   docker compose logs -f backend
   ```

## Future Considerations

1. **User-based Rate Limiting**: Consider implementing per-user rate limits instead of IP-based
2. **Dynamic Rate Limits**: Implement rate limits that adjust based on system load
3. **Monitoring**: Add metrics to track rate limiting effectiveness
4. **Caching**: Implement response caching to reduce API call frequency

## Conclusion

This fix resolves the critical rate limiting issue while maintaining security and improving the overall user experience. The tiered approach ensures that legitimate users can use the application freely while still protecting against abuse and attacks.
