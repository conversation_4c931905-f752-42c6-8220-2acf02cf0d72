import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { settingsService } from '../../services/settingsService'

// Async thunks
export const fetchProfile = createAsyncThunk(
  'settings/fetchProfile',
  async (_, { rejectWithValue }) => {
    try {
      const response = await settingsService.getProfile()
      return response.data.profile
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch profile')
    }
  }
)

export const updateProfile = createAsyncThunk(
  'settings/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      const response = await settingsService.updateProfile(profileData)
      return response.data.profile
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update profile')
    }
  }
)

export const updatePreferences = createAsyncThunk(
  'settings/updatePreferences',
  async (preferences, { rejectWithValue }) => {
    try {
      const response = await settingsService.updatePreferences(preferences)
      return response.data.preferences
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update preferences')
    }
  }
)

export const changePassword = createAsyncThunk(
  'settings/changePassword',
  async (passwordData, { rejectWithValue }) => {
    try {
      const response = await settingsService.changePassword(passwordData)
      return response.message
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to change password')
    }
  }
)

export const fetchApiKeys = createAsyncThunk(
  'settings/fetchApiKeys',
  async (_, { rejectWithValue }) => {
    try {
      const response = await settingsService.getApiKeys()
      return response.data.apiKeys
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch API keys')
    }
  }
)

export const createApiKey = createAsyncThunk(
  'settings/createApiKey',
  async (apiKeyData, { rejectWithValue }) => {
    try {
      const response = await settingsService.createApiKey(apiKeyData)
      return response.data.apiKey
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create API key')
    }
  }
)

export const updateApiKey = createAsyncThunk(
  'settings/updateApiKey',
  async ({ keyId, apiKeyData }, { rejectWithValue }) => {
    try {
      const response = await settingsService.updateApiKey(keyId, apiKeyData)
      return { keyId, apiKey: response.data.apiKey }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update API key')
    }
  }
)

export const deleteApiKey = createAsyncThunk(
  'settings/deleteApiKey',
  async (keyId, { rejectWithValue }) => {
    try {
      await settingsService.deleteApiKey(keyId)
      return keyId
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete API key')
    }
  }
)

export const fetchSessions = createAsyncThunk(
  'settings/fetchSessions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await settingsService.getSessions()
      return response.data.sessions
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch sessions')
    }
  }
)

export const terminateSession = createAsyncThunk(
  'settings/terminateSession',
  async (sessionId, { rejectWithValue }) => {
    try {
      await settingsService.terminateSession(sessionId)
      return sessionId
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to terminate session')
    }
  }
)

export const fetchLoginHistory = createAsyncThunk(
  'settings/fetchLoginHistory',
  async ({ page = 1, limit = 20 } = {}, { rejectWithValue }) => {
    try {
      const response = await settingsService.getLoginHistory(page, limit)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch login history')
    }
  }
)

export const fetchSystemSettings = createAsyncThunk(
  'settings/fetchSystemSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await settingsService.getSystemSettings()
      return response.data.settings
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch system settings')
    }
  }
)

export const updateSystemSettings = createAsyncThunk(
  'settings/updateSystemSettings',
  async (settings, { rejectWithValue }) => {
    try {
      // This would be implemented based on specific system setting updates
      // For now, we'll just return the settings
      return settings
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update system settings')
    }
  }
)

const initialState = {
  // Profile data
  profile: null,
  preferences: null,
  
  // API Keys
  apiKeys: [],
  
  // Security
  sessions: [],
  loginHistory: [],
  loginHistoryPagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
  
  // System settings (admin only)
  systemSettings: null,
  
  // Loading states
  isLoading: {
    profile: false,
    preferences: false,
    apiKeys: false,
    sessions: false,
    loginHistory: false,
    systemSettings: false,
  },
  
  // Error states
  errors: {
    profile: null,
    preferences: null,
    apiKeys: null,
    sessions: null,
    loginHistory: null,
    systemSettings: null,
  },
  
  // Success messages
  successMessages: {
    profile: null,
    preferences: null,
    apiKeys: null,
    sessions: null,
    password: null,
  },
}

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    clearError: (state, action) => {
      const { section } = action.payload
      if (section) {
        state.errors[section] = null
      } else {
        Object.keys(state.errors).forEach(key => {
          state.errors[key] = null
        })
      }
    },
    clearSuccessMessage: (state, action) => {
      const { section } = action.payload
      if (section) {
        state.successMessages[section] = null
      } else {
        Object.keys(state.successMessages).forEach(key => {
          state.successMessages[key] = null
        })
      }
    },
    setPreferences: (state, action) => {
      state.preferences = action.payload
    },
    updateLocalPreferences: (state, action) => {
      state.preferences = { ...state.preferences, ...action.payload }
    },
  },
  extraReducers: (builder) => {
    builder
      // Profile
      .addCase(fetchProfile.pending, (state) => {
        state.isLoading.profile = true
        state.errors.profile = null
      })
      .addCase(fetchProfile.fulfilled, (state, action) => {
        state.isLoading.profile = false
        state.profile = action.payload
        state.preferences = action.payload.preferences
      })
      .addCase(fetchProfile.rejected, (state, action) => {
        state.isLoading.profile = false
        state.errors.profile = action.payload
      })
      
      .addCase(updateProfile.pending, (state) => {
        state.isLoading.profile = true
        state.errors.profile = null
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading.profile = false
        state.profile = { ...state.profile, ...action.payload }
        state.successMessages.profile = 'Profile updated successfully'
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading.profile = false
        state.errors.profile = action.payload
      })
      
      // Preferences
      .addCase(updatePreferences.pending, (state) => {
        state.isLoading.preferences = true
        state.errors.preferences = null
      })
      .addCase(updatePreferences.fulfilled, (state, action) => {
        state.isLoading.preferences = false
        state.preferences = action.payload
        state.successMessages.preferences = 'Preferences updated successfully'
      })
      .addCase(updatePreferences.rejected, (state, action) => {
        state.isLoading.preferences = false
        state.errors.preferences = action.payload
      })
      
      // Password
      .addCase(changePassword.pending, (state) => {
        state.isLoading.profile = true
        state.errors.profile = null
      })
      .addCase(changePassword.fulfilled, (state, action) => {
        state.isLoading.profile = false
        state.successMessages.password = action.payload
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.isLoading.profile = false
        state.errors.profile = action.payload
      })
      
      // API Keys
      .addCase(fetchApiKeys.pending, (state) => {
        state.isLoading.apiKeys = true
        state.errors.apiKeys = null
      })
      .addCase(fetchApiKeys.fulfilled, (state, action) => {
        state.isLoading.apiKeys = false
        state.apiKeys = action.payload
      })
      .addCase(fetchApiKeys.rejected, (state, action) => {
        state.isLoading.apiKeys = false
        state.errors.apiKeys = action.payload
      })
      
      .addCase(createApiKey.fulfilled, (state, action) => {
        state.apiKeys.push(action.payload)
        state.successMessages.apiKeys = 'API key created successfully'
      })
      
      .addCase(updateApiKey.fulfilled, (state, action) => {
        const { keyId, apiKey } = action.payload
        const index = state.apiKeys.findIndex(key => key._id === keyId)
        if (index !== -1) {
          state.apiKeys[index] = { ...state.apiKeys[index], ...apiKey }
        }
        state.successMessages.apiKeys = 'API key updated successfully'
      })
      
      .addCase(deleteApiKey.fulfilled, (state, action) => {
        state.apiKeys = state.apiKeys.filter(key => key._id !== action.payload)
        state.successMessages.apiKeys = 'API key deleted successfully'
      })
      
      // Sessions
      .addCase(fetchSessions.pending, (state) => {
        state.isLoading.sessions = true
        state.errors.sessions = null
      })
      .addCase(fetchSessions.fulfilled, (state, action) => {
        state.isLoading.sessions = false
        state.sessions = action.payload
      })
      .addCase(fetchSessions.rejected, (state, action) => {
        state.isLoading.sessions = false
        state.errors.sessions = action.payload
      })
      
      .addCase(terminateSession.fulfilled, (state, action) => {
        state.sessions = state.sessions.filter(session => session.sessionId !== action.payload)
        state.successMessages.sessions = 'Session terminated successfully'
      })
      
      // Login History
      .addCase(fetchLoginHistory.pending, (state) => {
        state.isLoading.loginHistory = true
        state.errors.loginHistory = null
      })
      .addCase(fetchLoginHistory.fulfilled, (state, action) => {
        state.isLoading.loginHistory = false
        state.loginHistory = action.payload.loginHistory
        state.loginHistoryPagination = action.payload.pagination
      })
      .addCase(fetchLoginHistory.rejected, (state, action) => {
        state.isLoading.loginHistory = false
        state.errors.loginHistory = action.payload
      })
      
      // System Settings
      .addCase(fetchSystemSettings.pending, (state) => {
        state.isLoading.systemSettings = true
        state.errors.systemSettings = null
      })
      .addCase(fetchSystemSettings.fulfilled, (state, action) => {
        state.isLoading.systemSettings = false
        state.systemSettings = action.payload
      })
      .addCase(fetchSystemSettings.rejected, (state, action) => {
        state.isLoading.systemSettings = false
        state.errors.systemSettings = action.payload
      })
      
      .addCase(updateSystemSettings.fulfilled, (state, action) => {
        state.systemSettings = { ...state.systemSettings, ...action.payload }
      })
  },
})

export const {
  clearError,
  clearSuccessMessage,
  setPreferences,
  updateLocalPreferences,
} = settingsSlice.actions

export default settingsSlice.reducer
