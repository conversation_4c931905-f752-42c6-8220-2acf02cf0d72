"""
Service installation script for post-MSI installation
This script will be run after the MSI installation to set up the service
"""

import sys
import os
import logging
from pathlib import Path
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('service_install.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ServiceInstaller:
    """Service installation helper"""
    
    def __init__(self):
        self.install_dir = Path(sys.executable).parent if getattr(sys, 'frozen', False) else Path(__file__).parent
        self.service_exe = self.install_dir / "LoggingAgentService.exe"
        
    def check_admin_rights(self):
        """Check if running with administrator privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def install_service(self):
        """Install the Windows service"""
        logger.info("Installing Windows service...")
        
        if not self.service_exe.exists():
            logger.error(f"Service executable not found: {self.service_exe}")
            return False
        
        try:
            # Install the service
            cmd = [str(self.service_exe), "install"]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            logger.info("Service installed successfully")
            logger.debug(f"Install output: {result.stdout}")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Service installation failed: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False
    
    def create_start_menu_shortcuts(self):
        """Create Start Menu shortcuts"""
        logger.info("Creating Start Menu shortcuts...")
        
        try:
            import win32com.client
            
            shell = win32com.client.Dispatch("WScript.Shell")
            
            # Get Start Menu Programs folder
            start_menu = shell.SpecialFolders("Programs")
            app_folder = Path(start_menu) / "Python Logging Agent"
            app_folder.mkdir(exist_ok=True)
            
            # GUI shortcut
            gui_exe = self.install_dir / "LoggingAgentGUI.exe"
            if gui_exe.exists():
                shortcut = shell.CreateShortCut(str(app_folder / "Logging Agent GUI.lnk"))
                shortcut.Targetpath = str(gui_exe)
                shortcut.WorkingDirectory = str(self.install_dir)
                shortcut.Description = "Python Logging Agent Configuration GUI"
                shortcut.save()
                logger.info("Created GUI shortcut")
            
            # Console shortcut
            console_exe = self.install_dir / "LoggingAgent.exe"
            if console_exe.exists():
                shortcut = shell.CreateShortCut(str(app_folder / "Logging Agent Console.lnk"))
                shortcut.Targetpath = str(console_exe)
                shortcut.WorkingDirectory = str(self.install_dir)
                shortcut.Description = "Python Logging Agent Console"
                shortcut.save()
                logger.info("Created console shortcut")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create shortcuts: {e}")
            return False
    
    def setup_config_directory(self):
        """Setup configuration directory with proper permissions"""
        logger.info("Setting up configuration directory...")
        
        try:
            config_dir = self.install_dir / "config"
            config_dir.mkdir(exist_ok=True)
            
            # Copy default config if it doesn't exist
            user_config = config_dir / "config.yaml"
            default_config = config_dir / "default_config.yaml"
            
            if default_config.exists() and not user_config.exists():
                import shutil
                shutil.copy2(default_config, user_config)
                logger.info("Created user configuration file")
            
            # Setup logs directory
            logs_dir = self.install_dir / "logs"
            logs_dir.mkdir(exist_ok=True)
            
            logger.info("Configuration directory setup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup config directory: {e}")
            return False
    
    def install(self):
        """Main installation process"""
        logger.info("Starting post-installation setup...")
        
        # Check admin rights
        if not self.check_admin_rights():
            logger.error("Administrator privileges required for service installation")
            logger.info("Please run this installer as Administrator")
            return False
        
        success = True
        
        # Setup configuration
        if not self.setup_config_directory():
            logger.warning("Configuration setup failed")
            success = False
        
        # Install service
        if not self.install_service():
            logger.warning("Service installation failed")
            success = False
        
        # Create shortcuts
        if not self.create_start_menu_shortcuts():
            logger.warning("Shortcut creation failed")
            # Don't fail the installation for this
        
        if success:
            logger.info("="*50)
            logger.info("INSTALLATION COMPLETED SUCCESSFULLY!")
            logger.info("You can now:")
            logger.info("1. Open 'Python Logging Agent GUI' from Start Menu")
            logger.info("2. Configure your API settings")
            logger.info("3. Start the service from the GUI")
            logger.info("="*50)
        else:
            logger.error("Installation completed with errors")
            logger.info("Check the log file for details")
        
        return success

def main():
    """Main entry point"""
    print("Python Logging Agent - Post-Installation Setup")
    print("="*50)
    
    installer = ServiceInstaller()
    success = installer.install()
    
    if success:
        print("\n✓ Installation completed successfully!")
        input("Press Enter to continue...")
    else:
        print("\n✗ Installation completed with errors!")
        input("Press Enter to continue...")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
