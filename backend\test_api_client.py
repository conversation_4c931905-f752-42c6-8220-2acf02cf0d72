#!/usr/bin/env python3
"""
Test script for ExLog API Client

This script tests the API client functionality by sending sample logs
to the ExLog dashboard API.
"""

import asyncio
import json
import sys
import time
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from utils.api_client import ExLogAPIClient
    from config.config_manager import ConfigManager
    API_CLIENT_AVAILABLE = True
except ImportError as e:
    print(f"Error importing API client: {e}")
    print("Please install dependencies: pip install aiohttp requests")
    API_CLIENT_AVAILABLE = False


def create_sample_logs(count: int = 5):
    """Create sample logs for testing."""
    logs = []
    
    for i in range(count):
        log = {
            "log_id": f"test-log-{i+1:03d}",
            "timestamp": datetime.now().isoformat(),
            "source": "System" if i % 2 == 0 else "Application",
            "source_type": "event",
            "host": "test-host",
            "log_level": ["info", "warning", "error"][i % 3],
            "message": f"Test log message {i+1} - This is a sample log entry for testing the API client",
            "raw_data": None,
            "additional_fields": {
                "test_field": f"test_value_{i+1}",
                "event_id": 1000 + i,
                "category": "test"
            }
        }
        logs.append(log)
    
    return logs


async def test_api_client():
    """Test the API client functionality."""
    if not API_CLIENT_AVAILABLE:
        print("API client not available. Cannot run test.")
        return False
    
    print("Testing ExLog API Client")
    print("=" * 50)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        api_config = config.get('exlog_api', {})
        
        if not api_config.get('enabled', False):
            print("API client is disabled in configuration.")
            print("Please enable it in config/default_config.yaml")
            return False
        
        print(f"API Endpoint: {api_config.get('endpoint')}")
        print(f"API Key: {'*' * len(api_config.get('api_key', ''))}")
        print(f"Batch Size: {api_config.get('batch_size', 100)}")
        print()
        
        # Create API client
        client = ExLogAPIClient(api_config)
        
        # Start the client
        print("Starting API client...")
        await client.start()
        print("API client started successfully!")
        print()
        
        # Create sample logs
        sample_logs = create_sample_logs(10)
        print(f"Created {len(sample_logs)} sample logs")
        
        # Send logs
        print("Sending logs to API...")
        client.send_logs(sample_logs)
        
        # Wait a bit for processing
        print("Waiting for logs to be processed...")
        await asyncio.sleep(5)
        
        # Get statistics
        stats = client.get_stats()
        print("\nAPI Client Statistics:")
        print(f"  Logs sent: {stats.get('logs_sent', 0)}")
        print(f"  Logs failed: {stats.get('logs_failed', 0)}")
        print(f"  Batches sent: {stats.get('batches_sent', 0)}")
        print(f"  Batches failed: {stats.get('batches_failed', 0)}")
        print(f"  API errors: {stats.get('api_errors', 0)}")
        print(f"  Queue size: {stats.get('queue_size', 0)}")
        print(f"  Offline buffer size: {stats.get('offline_buffer_size', 0)}")
        
        if stats.get('last_successful_send'):
            print(f"  Last successful send: {stats['last_successful_send']}")
        
        if stats.get('last_error'):
            print(f"  Last error: {stats['last_error']}")
        
        # Stop the client
        print("\nStopping API client...")
        await client.stop()
        print("API client stopped successfully!")
        
        # Check if logs were sent successfully
        success = stats.get('logs_sent', 0) > 0
        if success:
            print("\n✅ Test completed successfully!")
        else:
            print("\n❌ Test failed - no logs were sent successfully")
            
        return success
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return False


def test_synchronous():
    """Test the API client using synchronous requests library."""
    try:
        import requests
        
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        api_config = config.get('exlog_api', {})
        
        endpoint = api_config.get('endpoint', 'http://localhost:5000/api/v1/logs')
        api_key = api_config.get('api_key', '')
        
        print("\nTesting with synchronous requests...")
        print(f"Endpoint: {endpoint}")
        
        # Create sample logs
        sample_logs = create_sample_logs(3)
        
        # Prepare payload
        payload = {"logs": sample_logs}
        
        # Send request
        headers = {
            'Content-Type': 'application/json',
            'X-API-Key': api_key
        }
        
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Body: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ Synchronous test successful!")
            return True
        else:
            print("❌ Synchronous test failed!")
            return False
            
    except Exception as e:
        print(f"❌ Synchronous test failed: {e}")
        return False


def main():
    """Main test function."""
    print("ExLog API Client Test Suite")
    print("=" * 50)
    
    # Test 1: Asynchronous API client
    if API_CLIENT_AVAILABLE:
        async_success = asyncio.run(test_api_client())
    else:
        async_success = False
    
    # Test 2: Synchronous requests
    sync_success = test_synchronous()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"  Asynchronous API Client: {'✅ PASS' if async_success else '❌ FAIL'}")
    print(f"  Synchronous Requests:    {'✅ PASS' if sync_success else '❌ FAIL'}")
    
    if async_success or sync_success:
        print("\n🎉 At least one test passed! API integration is working.")
        return 0
    else:
        print("\n💥 All tests failed! Please check your configuration and API server.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
