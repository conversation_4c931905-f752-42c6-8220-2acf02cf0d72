import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Button,
  Grid,
  TextField,
  Typography,
  FormControlLabel,
  Switch,
  Slider,
  Alert,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Pagination,
  Tabs,
  Tab,
} from '@mui/material'
import {
  Save as SaveIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Storage as StorageIcon,
  Email as EmailIcon,
  Webhook as WebhookIcon,
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Computer as ComputerIcon,
  Smartphone as SmartphoneIcon,
  Tablet as TabletIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material'
import { settingsService } from '../../../services/settingsService'

const SystemTab = ({ onSuccess }) => {
  const [activeTab, setActiveTab] = useState(0)
  const [systemSettings, setSystemSettings] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [retentionDialog, setRetentionDialog] = useState(false)
  const [selectedPolicy, setSelectedPolicy] = useState(null)
  const [policyForm, setPolicyForm] = useState({
    name: '',
    description: '',
    retentionDays: 90,
    logSources: [],
    logLevels: [],
    isDefault: false,
  })

  // Security monitoring state
  const [loginActivities, setLoginActivities] = useState([])
  const [securityStats, setSecurityStats] = useState({})
  const [securityPagination, setSecurityPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  })
  const [securityFilters, setSecurityFilters] = useState({
    success: '', // '', 'true', 'false'
    startDate: '',
    endDate: '',
    userId: '',
  })

  useEffect(() => {
    loadSystemSettings()
    if (activeTab === 1) {
      loadSecurityMonitoring()
    }
  }, [activeTab])

  const loadSecurityMonitoring = async () => {
    setIsLoading(true)
    try {
      const filters = {
        page: securityPagination.currentPage,
        limit: securityPagination.itemsPerPage,
        ...securityFilters,
      }

      const response = await settingsService.getSystemLoginActivity(filters)
      setLoginActivities(response.data.loginActivities)
      setSecurityStats(response.data.stats)
      setSecurityPagination(prev => ({
        ...prev,
        ...response.data.pagination,
      }))
    } catch (error) {
      onSuccess('Failed to load security monitoring data', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const loadSystemSettings = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.getSystemSettings()
      setSystemSettings(response.data.settings)
    } catch (error) {
      onSuccess('Failed to load system settings', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveLogRetention = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.updateLogRetentionSettings({
        defaultRetentionDays: systemSettings.logRetention.defaultRetentionDays,
        autoArchiveEnabled: systemSettings.logRetention.autoArchiveEnabled,
        archiveCompressionEnabled: systemSettings.logRetention.archiveCompressionEnabled,
        archiveLocation: systemSettings.logRetention.archiveLocation,
      })
      
      if (response.status === 'success') {
        onSuccess('Log retention settings updated successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to update settings', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveNotifications = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.updateNotificationSettings({
        emailSettings: systemSettings.systemNotifications.emailSettings,
        webhookSettings: systemSettings.systemNotifications.webhookSettings,
        slackIntegration: systemSettings.systemNotifications.slackIntegration,
      })
      
      if (response.status === 'success') {
        onSuccess('Notification settings updated successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to update settings', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateRetentionPolicy = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.createRetentionPolicy(policyForm)
      
      if (response.status === 'success') {
        await loadSystemSettings()
        setRetentionDialog(false)
        resetPolicyForm()
        onSuccess('Retention policy created successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to create policy', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateRetentionPolicy = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.updateRetentionPolicy(selectedPolicy._id, policyForm)
      
      if (response.status === 'success') {
        await loadSystemSettings()
        setRetentionDialog(false)
        resetPolicyForm()
        onSuccess('Retention policy updated successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to update policy', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteRetentionPolicy = async (policyId) => {
    if (!window.confirm('Are you sure you want to delete this retention policy?')) return

    setIsLoading(true)
    try {
      const response = await settingsService.deleteRetentionPolicy(policyId)
      
      if (response.status === 'success') {
        await loadSystemSettings()
        onSuccess('Retention policy deleted successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to delete policy', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const resetPolicyForm = () => {
    setPolicyForm({
      name: '',
      description: '',
      retentionDays: 90,
      logSources: [],
      logLevels: [],
      isDefault: false,
    })
    setSelectedPolicy(null)
  }

  const openCreatePolicyDialog = () => {
    resetPolicyForm()
    setRetentionDialog(true)
  }

  const openEditPolicyDialog = (policy) => {
    setSelectedPolicy(policy)
    setPolicyForm({
      name: policy.name,
      description: policy.description || '',
      retentionDays: policy.retentionDays,
      logSources: policy.logSources || [],
      logLevels: policy.logLevels || [],
      isDefault: policy.isDefault || false,
    })
    setRetentionDialog(true)
  }

  const updateSystemSetting = (section, field, value) => {
    setSystemSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }))
  }

  const updateNestedSetting = (section, subsection, field, value) => {
    setSystemSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value,
        },
      },
    }))
  }

  // Security monitoring helper functions
  const getDeviceType = (deviceInfo) => {
    if (!deviceInfo) return 'Unknown'

    const device = deviceInfo.device?.toLowerCase() || ''
    if (device.includes('mobile') || device.includes('phone')) {
      return 'Mobile'
    } else if (device.includes('tablet')) {
      return 'Tablet'
    }
    return 'Desktop'
  }

  const getDeviceIcon = (deviceInfo) => {
    const deviceType = getDeviceType(deviceInfo)

    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <SmartphoneIcon fontSize="small" />
      case 'tablet':
        return <TabletIcon fontSize="small" />
      default:
        return <ComputerIcon fontSize="small" />
    }
  }

  const getStatusChip = (success, failureReason) => {
    if (success) {
      return (
        <Chip
          icon={<CheckCircleIcon />}
          label="Success"
          color="success"
          size="small"
        />
      )
    } else {
      return (
        <Chip
          icon={<ErrorIcon />}
          label={failureReason || 'Failed'}
          color="error"
          size="small"
        />
      )
    }
  }

  const handleSecurityFilterChange = (filterType, value) => {
    setSecurityFilters(prev => ({ ...prev, [filterType]: value }))
    setSecurityPagination(prev => ({ ...prev, currentPage: 1 })) // Reset to first page
  }

  const handleSecurityPageChange = (event, newPage) => {
    setSecurityPagination(prev => ({ ...prev, currentPage: newPage }))
  }

  const applySecurityFilters = () => {
    loadSecurityMonitoring()
  }

  if (!systemSettings) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>Loading system settings...</Typography>
      </Box>
    )
  }

  return (
    <Box>
      {/* Tabs */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Tabs
            value={activeTab}
            onChange={(e, newValue) => setActiveTab(newValue)}
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="System Settings" />
            <Tab label="Security Monitoring" />
          </Tabs>
        </CardContent>
      </Card>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Grid container spacing={3}>
        {/* Log Retention Settings */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Log Retention Settings" 
              avatar={<StorageIcon />}
              action={
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveLogRetention}
                  disabled={isLoading}
                >
                  Save
                </Button>
              }
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Typography gutterBottom>
                    Default Retention: {systemSettings.logRetention.defaultRetentionDays} days
                  </Typography>
                  <Slider
                    value={systemSettings.logRetention.defaultRetentionDays}
                    onChange={(e, value) => updateSystemSetting('logRetention', 'defaultRetentionDays', value)}
                    min={1}
                    max={365}
                    step={1}
                    marks={[
                      { value: 30, label: '30d' },
                      { value: 90, label: '90d' },
                      { value: 180, label: '6m' },
                      { value: 365, label: '1y' },
                    ]}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Archive Location</InputLabel>
                    <Select
                      value={systemSettings.logRetention.archiveLocation}
                      label="Archive Location"
                      onChange={(e) => updateSystemSetting('logRetention', 'archiveLocation', e.target.value)}
                    >
                      <MenuItem value="local">Local Storage</MenuItem>
                      <MenuItem value="s3">Amazon S3</MenuItem>
                      <MenuItem value="azure">Azure Blob</MenuItem>
                      <MenuItem value="gcp">Google Cloud Storage</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemSettings.logRetention.autoArchiveEnabled}
                        onChange={(e) => updateSystemSetting('logRetention', 'autoArchiveEnabled', e.target.checked)}
                      />
                    }
                    label="Enable automatic archiving"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemSettings.logRetention.archiveCompressionEnabled}
                        onChange={(e) => updateSystemSetting('logRetention', 'archiveCompressionEnabled', e.target.checked)}
                      />
                    }
                    label="Enable archive compression"
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              {/* Retention Policies */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Retention Policies</Typography>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={openCreatePolicyDialog}
                >
                  Add Policy
                </Button>
              </Box>

              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Retention Days</TableCell>
                      <TableCell>Log Sources</TableCell>
                      <TableCell>Log Levels</TableCell>
                      <TableCell>Default</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {systemSettings.logRetention.retentionPolicies.map((policy) => (
                      <TableRow key={policy._id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {policy.name}
                          </Typography>
                          {policy.description && (
                            <Typography variant="caption" color="text.secondary">
                              {policy.description}
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>{policy.retentionDays} days</TableCell>
                        <TableCell>
                          {policy.logSources.length > 0 ? (
                            policy.logSources.map(source => (
                              <Chip key={source} label={source} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                            ))
                          ) : (
                            <Typography variant="caption" color="text.secondary">All</Typography>
                          )}
                        </TableCell>
                        <TableCell>
                          {policy.logLevels.length > 0 ? (
                            policy.logLevels.map(level => (
                              <Chip key={level} label={level} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                            ))
                          ) : (
                            <Typography variant="caption" color="text.secondary">All</Typography>
                          )}
                        </TableCell>
                        <TableCell>
                          {policy.isDefault && <Chip label="Default" color="primary" size="small" />}
                        </TableCell>
                        <TableCell>
                          <IconButton
                            size="small"
                            onClick={() => openEditPolicyDialog(policy)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteRetentionPolicy(policy._id)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Email Notification Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Email Notifications" 
              avatar={<EmailIcon />}
              action={
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveNotifications}
                  disabled={isLoading}
                >
                  Save
                </Button>
              }
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemSettings.systemNotifications.emailSettings.enabled}
                        onChange={(e) => updateNestedSetting('systemNotifications', 'emailSettings', 'enabled', e.target.checked)}
                      />
                    }
                    label="Enable email notifications"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="SMTP Host"
                    value={systemSettings.systemNotifications.emailSettings.smtpHost || ''}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'emailSettings', 'smtpHost', e.target.value)}
                    disabled={!systemSettings.systemNotifications.emailSettings.enabled}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="SMTP Port"
                    type="number"
                    value={systemSettings.systemNotifications.emailSettings.smtpPort || 587}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'emailSettings', 'smtpPort', parseInt(e.target.value))}
                    disabled={!systemSettings.systemNotifications.emailSettings.enabled}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemSettings.systemNotifications.emailSettings.smtpSecure}
                        onChange={(e) => updateNestedSetting('systemNotifications', 'emailSettings', 'smtpSecure', e.target.checked)}
                        disabled={!systemSettings.systemNotifications.emailSettings.enabled}
                      />
                    }
                    label="Use TLS/SSL"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="From Address"
                    type="email"
                    value={systemSettings.systemNotifications.emailSettings.fromAddress || ''}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'emailSettings', 'fromAddress', e.target.value)}
                    disabled={!systemSettings.systemNotifications.emailSettings.enabled}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="From Name"
                    value={systemSettings.systemNotifications.emailSettings.fromName || 'ExLog System'}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'emailSettings', 'fromName', e.target.value)}
                    disabled={!systemSettings.systemNotifications.emailSettings.enabled}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Webhook Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Webhook Integration" avatar={<WebhookIcon />} />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={systemSettings.systemNotifications.webhookSettings.enabled}
                        onChange={(e) => updateNestedSetting('systemNotifications', 'webhookSettings', 'enabled', e.target.checked)}
                      />
                    }
                    label="Enable webhook notifications"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Default Webhook URL"
                    value={systemSettings.systemNotifications.webhookSettings.defaultWebhookUrl || ''}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'webhookSettings', 'defaultWebhookUrl', e.target.value)}
                    disabled={!systemSettings.systemNotifications.webhookSettings.enabled}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Retry Attempts"
                    type="number"
                    value={systemSettings.systemNotifications.webhookSettings.retryAttempts || 3}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'webhookSettings', 'retryAttempts', parseInt(e.target.value))}
                    disabled={!systemSettings.systemNotifications.webhookSettings.enabled}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Timeout (ms)"
                    type="number"
                    value={systemSettings.systemNotifications.webhookSettings.timeout || 10000}
                    onChange={(e) => updateNestedSetting('systemNotifications', 'webhookSettings', 'timeout', parseInt(e.target.value))}
                    disabled={!systemSettings.systemNotifications.webhookSettings.enabled}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        </Grid>
      )}

      {/* Security Monitoring Tab */}
      {activeTab === 1 && (
        <Grid container spacing={3}>
          {/* Security Statistics */}
          <Grid item xs={12}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {securityStats.totalLogins || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Login Attempts
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {securityStats.successfulLogins || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Successful Logins
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="error.main">
                      {securityStats.failedLogins || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Failed Logins
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">
                      {securityStats.uniqueUsers || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Unique Users
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>

          {/* Security Monitoring */}
          <Grid item xs={12}>
            <Card>
              <CardHeader
                title="System-Wide Login Activity"
                avatar={<SecurityIcon />}
                action={
                  <Button
                    startIcon={<RefreshIcon />}
                    onClick={loadSecurityMonitoring}
                    disabled={isLoading}
                  >
                    Refresh
                  </Button>
                }
              />
              <CardContent>
                {/* Filters */}
                <Card variant="outlined" sx={{ mb: 3 }}>
                  <CardHeader title="Filters" />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={3}>
                        <FormControl fullWidth>
                          <InputLabel>Status</InputLabel>
                          <Select
                            value={securityFilters.success}
                            label="Status"
                            onChange={(e) => handleSecurityFilterChange('success', e.target.value)}
                          >
                            <MenuItem value="">All</MenuItem>
                            <MenuItem value="true">Successful Only</MenuItem>
                            <MenuItem value="false">Failed Only</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="Start Date"
                          type="date"
                          value={securityFilters.startDate}
                          onChange={(e) => handleSecurityFilterChange('startDate', e.target.value)}
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="End Date"
                          type="date"
                          value={securityFilters.endDate}
                          onChange={(e) => handleSecurityFilterChange('endDate', e.target.value)}
                          InputLabelProps={{ shrink: true }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <Button
                          fullWidth
                          variant="contained"
                          startIcon={<FilterIcon />}
                          onClick={applySecurityFilters}
                          sx={{ height: '56px' }}
                        >
                          Apply Filters
                        </Button>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>

                {/* Login Activity Table */}
                {loginActivities.length === 0 ? (
                  <Alert severity="info">
                    No login activity found for the selected filters.
                  </Alert>
                ) : (
                  <>
                    <TableContainer component={Paper} variant="outlined">
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>User</TableCell>
                            <TableCell>Status</TableCell>
                            <TableCell>Date & Time</TableCell>
                            <TableCell>Device</TableCell>
                            <TableCell>Browser</TableCell>
                            <TableCell>IP Address</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {loginActivities.map((activity, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Box>
                                  <Typography variant="body2" fontWeight="medium">
                                    {activity.user.firstName} {activity.user.lastName}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    {activity.user.email}
                                  </Typography>
                                  <Chip
                                    label={activity.user.role}
                                    size="small"
                                    sx={{ ml: 1 }}
                                  />
                                </Box>
                              </TableCell>
                              <TableCell>
                                {getStatusChip(activity.success, activity.failureReason)}
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {settingsService.formatDateTime(activity.timestamp)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {getDeviceIcon(activity.deviceInfo)}
                                  <Box>
                                    <Typography variant="body2">
                                      {getDeviceType(activity.deviceInfo)}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      {activity.deviceInfo?.os || 'Unknown OS'}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {activity.deviceInfo?.browser || 'Unknown Browser'}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2" fontFamily="monospace">
                                  {activity.ip || 'Unknown'}
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {/* Pagination */}
                    {securityPagination.totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                        <Pagination
                          count={securityPagination.totalPages}
                          page={securityPagination.currentPage}
                          onChange={handleSecurityPageChange}
                          color="primary"
                          showFirstButton
                          showLastButton
                        />
                      </Box>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Retention Policy Dialog */}
      <Dialog open={retentionDialog} onClose={() => setRetentionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedPolicy ? 'Edit Retention Policy' : 'Create Retention Policy'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Policy Name"
                value={policyForm.name}
                onChange={(e) => setPolicyForm(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Retention Days"
                type="number"
                value={policyForm.retentionDays}
                onChange={(e) => setPolicyForm(prev => ({ ...prev, retentionDays: parseInt(e.target.value) }))}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                value={policyForm.description}
                onChange={(e) => setPolicyForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={policyForm.isDefault}
                    onChange={(e) => setPolicyForm(prev => ({ ...prev, isDefault: e.target.checked }))}
                  />
                }
                label="Set as default policy"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRetentionDialog(false)}>Cancel</Button>
          <Button
            onClick={selectedPolicy ? handleUpdateRetentionPolicy : handleCreateRetentionPolicy}
            variant="contained"
            disabled={isLoading || !policyForm.name || !policyForm.retentionDays}
          >
            {selectedPolicy ? 'Update' : 'Create'} Policy
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default SystemTab
