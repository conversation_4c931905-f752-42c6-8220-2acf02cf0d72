import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Button,
  Grid,
  Typography,
  FormControlLabel,
  Switch,
  Slider,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
} from '@mui/material'
import {
  Security as SecurityIcon,
  Smartphone as SmartphoneIcon,
  Computer as ComputerIcon,
  Tablet as TabletIcon,
  Delete as DeleteIcon,
  ExitToApp as LogoutIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material'
import { settingsService } from '../../../services/settingsService'

const SecurityTab = ({ onSuccess }) => {
  const { user } = useSelector((state) => state.auth)
  const [sessions, setSessions] = useState([])
  const [loginHistory, setLoginHistory] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [terminateDialog, setTerminateDialog] = useState(false)
  const [terminateAllDialog, setTerminateAllDialog] = useState(false)
  const [selectedSession, setSelectedSession] = useState(null)
  const [securitySettings, setSecuritySettings] = useState({
    sessionTimeout: 60,
    requireMfaForSensitiveActions: false,
    loginNotifications: true,
    allowMultipleSessions: true,
  })

  useEffect(() => {
    loadSessions()
    loadLoginHistory()
  }, [])

  const loadSessions = async () => {
    try {
      // Check if user is admin and can view all sessions
      const isAdmin = user?.permissions?.includes('system_admin') ||
                     user?.permissions?.includes('manage_users') ||
                     user?.role === 'admin'

      const response = isAdmin
        ? await settingsService.getAllSessions()
        : await settingsService.getSessions()

      setSessions(response.data.sessions || [])
    } catch (error) {
      onSuccess('Failed to load sessions', 'error')
    }
  }

  const loadLoginHistory = async () => {
    if (!user?._id) {
      onSuccess('User not authenticated', 'error')
      return
    }

    try {
      const response = await settingsService.getLoginHistory(user._id, 1, 10)
      setLoginHistory(response.data.loginHistory || [])
    } catch (error) {
      onSuccess('Failed to load login history', 'error')
    }
  }

  const handleTerminateSession = async () => {
    setIsLoading(true)
    try {
      let response

      // If terminating another user's session (admin functionality)
      if (selectedSession.userId && selectedSession.userId !== user._id) {
        response = await settingsService.terminateUserSession(selectedSession.userId, selectedSession.sessionId)
      } else {
        // Terminating own session
        response = await settingsService.terminateSession(selectedSession.sessionId)
      }

      if (response.status === 'success') {
        await loadSessions()
        setTerminateDialog(false)
        onSuccess('Session terminated successfully')
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to terminate session', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleTerminateAllSessions = async () => {
    setIsLoading(true)
    try {
      const response = await settingsService.terminateAllOtherSessions()

      if (response.status === 'success') {
        await loadSessions()
        setTerminateAllDialog(false)
        onSuccess(`${response.data?.terminatedCount || 0} sessions terminated successfully`)
      }
    } catch (error) {
      onSuccess(error.response?.data?.message || 'Failed to terminate sessions', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const getDeviceIcon = (deviceInfo) => {
    if (!deviceInfo) return <ComputerIcon />
    
    const device = deviceInfo.device?.toLowerCase() || ''
    if (device.includes('mobile') || device.includes('phone')) {
      return <SmartphoneIcon />
    } else if (device.includes('tablet')) {
      return <TabletIcon />
    }
    return <ComputerIcon />
  }

  const getSessionStatus = (session) => {
    const lastActivity = new Date(session.lastActivity)
    const now = new Date()
    const diffInMinutes = (now - lastActivity) / (1000 * 60)
    
    if (diffInMinutes < 5) {
      return <Chip label="Active" color="success" size="small" />
    } else if (diffInMinutes < 30) {
      return <Chip label="Recent" color="warning" size="small" />
    } else {
      return <Chip label="Idle" color="default" size="small" />
    }
  }

  const getLoginStatusIcon = (success) => {
    return success ? (
      <CheckCircleIcon color="success" fontSize="small" />
    ) : (
      <ErrorIcon color="error" fontSize="small" />
    )
  }

  const formatLocation = (location) => {
    return location || 'Unknown Location'
  }

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Security Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Security Settings" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography gutterBottom>
                    Session Timeout: {securitySettings.sessionTimeout} minutes
                  </Typography>
                  <Slider
                    value={securitySettings.sessionTimeout}
                    onChange={(e, value) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: value }))}
                    min={15}
                    max={480}
                    step={15}
                    marks={[
                      { value: 15, label: '15m' },
                      { value: 60, label: '1h' },
                      { value: 240, label: '4h' },
                      { value: 480, label: '8h' },
                    ]}
                    valueLabelDisplay="auto"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={securitySettings.requireMfaForSensitiveActions}
                        onChange={(e) => setSecuritySettings(prev => ({ 
                          ...prev, 
                          requireMfaForSensitiveActions: e.target.checked 
                        }))}
                      />
                    }
                    label="Require MFA for sensitive actions"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={securitySettings.loginNotifications}
                        onChange={(e) => setSecuritySettings(prev => ({ 
                          ...prev, 
                          loginNotifications: e.target.checked 
                        }))}
                      />
                    }
                    label="Email notifications for new logins"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={securitySettings.allowMultipleSessions}
                        onChange={(e) => setSecuritySettings(prev => ({ 
                          ...prev, 
                          allowMultipleSessions: e.target.checked 
                        }))}
                      />
                    }
                    label="Allow multiple active sessions"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Security Actions" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="warning"
                    startIcon={<LogoutIcon />}
                    onClick={() => setTerminateAllDialog(true)}
                    disabled={sessions.length <= 1}
                  >
                    Terminate All Other Sessions
                  </Button>
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    This will log you out of all other devices and browsers
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Alert severity="info">
                    <Typography variant="body2">
                      <strong>Security Tip:</strong> Regularly review your active sessions and login history. 
                      If you notice any suspicious activity, terminate unknown sessions immediately and change your password.
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Sessions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader
              title="Active Sessions"
              subheader={`${sessions.length} active session${sessions.length !== 1 ? 's' : ''}`}
            />
            <CardContent>
              {sessions.length === 0 ? (
                <Alert severity="info">No active sessions found.</Alert>
              ) : (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        {/* Show user column if admin viewing all sessions */}
                        {sessions.some(s => s.userEmail) && <TableCell>User</TableCell>}
                        <TableCell>Device</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Last Activity</TableCell>
                        <TableCell>Created</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {sessions.map((session) => (
                        <TableRow key={session.sessionId}>
                          {/* Show user info if admin viewing all sessions */}
                          {sessions.some(s => s.userEmail) && (
                            <TableCell>
                              <Box>
                                <Typography variant="body2">
                                  {session.userName || 'Unknown User'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {session.userEmail || 'Unknown Email'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                  Role: {session.userRole || 'Unknown'}
                                </Typography>
                              </Box>
                            </TableCell>
                          )}
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getDeviceIcon(session.deviceInfo)}
                              <Box>
                                <Typography variant="body2">
                                  {session.deviceInfo?.browser || 'Unknown Browser'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {session.deviceInfo?.os || 'Unknown OS'}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatLocation(session.deviceInfo?.location)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {session.deviceInfo?.ip || 'Unknown IP'}
                            </Typography>
                          </TableCell>
                          <TableCell>{getSessionStatus(session)}</TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {settingsService.formatTimeAgo(session.lastActivity)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {settingsService.formatDateTime(session.createdAt)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Tooltip title="Terminate Session">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setSelectedSession(session)
                                  setTerminateDialog(true)
                                }}
                                color="error"
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Login History */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Recent Login History" />
            <CardContent>
              {loginHistory.length === 0 ? (
                <Alert severity="info">No login history found.</Alert>
              ) : (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Status</TableCell>
                        <TableCell>Date & Time</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Device</TableCell>
                        <TableCell>IP Address</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {loginHistory.map((login, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getLoginStatusIcon(login.success)}
                              <Typography variant="body2">
                                {login.success ? 'Success' : 'Failed'}
                              </Typography>
                            </Box>
                            {!login.success && login.failureReason && (
                              <Typography variant="caption" color="error">
                                {login.failureReason}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {settingsService.formatDateTime(login.timestamp)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatLocation(login.location)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getDeviceIcon(login.deviceInfo)}
                              <Box>
                                <Typography variant="body2">
                                  {login.deviceInfo?.browser || 'Unknown'}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {login.deviceInfo?.os || 'Unknown'}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontFamily="monospace">
                              {login.ip || 'Unknown'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Terminate Session Dialog */}
      <Dialog open={terminateDialog} onClose={() => setTerminateDialog(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            Terminate Session
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to terminate this session? The user will be logged out immediately.
          </Typography>
          {selectedSession && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              {selectedSession.userEmail && (
                <>
                  <Typography variant="body2">
                    <strong>User:</strong> {selectedSession.userName} ({selectedSession.userEmail})
                  </Typography>
                  <Typography variant="body2">
                    <strong>Role:</strong> {selectedSession.userRole}
                  </Typography>
                </>
              )}
              <Typography variant="body2">
                <strong>Device:</strong> {selectedSession.deviceInfo?.browser} on {selectedSession.deviceInfo?.os}
              </Typography>
              <Typography variant="body2">
                <strong>Location:</strong> {formatLocation(selectedSession.deviceInfo?.location)}
              </Typography>
              <Typography variant="body2">
                <strong>Last Activity:</strong> {settingsService.formatTimeAgo(selectedSession.lastActivity)}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTerminateDialog(false)}>Cancel</Button>
          <Button onClick={handleTerminateSession} color="error" variant="contained" disabled={isLoading}>
            Terminate Session
          </Button>
        </DialogActions>
      </Dialog>

      {/* Terminate All Sessions Dialog */}
      <Dialog open={terminateAllDialog} onClose={() => setTerminateAllDialog(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            Terminate All Other Sessions
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to terminate all other active sessions? This will log you out of all other devices and browsers.
          </Typography>
          <Alert severity="warning" sx={{ mt: 2 }}>
            This action will terminate {sessions.length - 1} session{sessions.length - 1 !== 1 ? 's' : ''}.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTerminateAllDialog(false)}>Cancel</Button>
          <Button onClick={handleTerminateAllSessions} color="error" variant="contained" disabled={isLoading}>
            Terminate All Other Sessions
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default SecurityTab
