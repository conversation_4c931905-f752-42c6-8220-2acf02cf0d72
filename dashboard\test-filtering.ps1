# Test script to verify filtering works correctly

Write-Host "Testing ExLog Filtering..." -ForegroundColor Green

# Login first
$loginData = @{
    email = "<EMAIL>"
    password = "Admin123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/auth/login" -Method Post -Body $loginData -ContentType "application/json"
    $token = $loginResponse.data.token
    Write-Host "✅ Login successful" -ForegroundColor Green
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Test 1: No filters (should work)
Write-Host "`n1. Testing with no filters..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?limit=5" -Method Get -Headers $headers
    Write-Host "✅ No filters test passed - $($response.data.logs.Count) logs returned" -ForegroundColor Green
} catch {
    Write-Host "❌ No filters test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: With valid source filter
Write-Host "`n2. Testing with valid source filter..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?source=System&limit=5" -Method Get -Headers $headers
    Write-Host "✅ Source filter test passed - $($response.data.logs.Count) logs returned" -ForegroundColor Green
} catch {
    Write-Host "❌ Source filter test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: With valid log level filter
Write-Host "`n3. Testing with valid log level filter..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?logLevel=info&limit=5" -Method Get -Headers $headers
    Write-Host "✅ Log level filter test passed - $($response.data.logs.Count) logs returned" -ForegroundColor Green
} catch {
    Write-Host "❌ Log level filter test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: With host filter
Write-Host "`n4. Testing with host filter..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?host=server-01&limit=5" -Method Get -Headers $headers
    Write-Host "✅ Host filter test passed - $($response.data.logs.Count) logs returned" -ForegroundColor Green
} catch {
    Write-Host "❌ Host filter test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: With search filter
Write-Host "`n5. Testing with search filter..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?search=system&limit=5" -Method Get -Headers $headers
    Write-Host "✅ Search filter test passed - $($response.data.logs.Count) logs returned" -ForegroundColor Green
} catch {
    Write-Host "❌ Search filter test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Combined filters
Write-Host "`n6. Testing with combined filters..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs?source=System&logLevel=info&limit=5" -Method Get -Headers $headers
    Write-Host "✅ Combined filters test passed - $($response.data.logs.Count) logs returned" -ForegroundColor Green
} catch {
    Write-Host "❌ Combined filters test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Filtering tests completed!" -ForegroundColor Green
Write-Host "The frontend should now work correctly with filtering." -ForegroundColor Cyan
