"""
Systemd Service Wrapper for Linux Log Collection Agent

This module provides systemd service functionality for running the
Linux log collection agent as a system service.
"""

import logging
import os
import signal
import sys
import time
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from logging_agent.agent import LinuxLoggingAgent
from config.config_manager import ConfigManager


class LinuxLoggingAgentService:
    """Systemd service wrapper for the Linux Logging Agent."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the service."""
        self.config_path = config_path
        self.agent = None
        self.logger = None
        self._running = False
        
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGHUP, self._reload_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        if self.logger:
            self.logger.info(f"Received signal {signum}, shutting down...")
        
        self._running = False
        
        if self.agent:
            self.agent.stop()
    
    def _reload_handler(self, signum, frame):
        """Handle reload signal (SIGHUP)."""
        if self.logger:
            self.logger.info("Received SIGHUP, reloading configuration...")
        
        if self.agent:
            self.agent.reload_config()

    def _validate_environment(self) -> bool:
        """Validate the environment before starting the service."""
        try:
            # Check if running as correct user
            import pwd
            current_user = pwd.getpwuid(os.getuid()).pw_name
            self.logger.debug(f"Running as user: {current_user}")

            # Check log directory permissions
            log_dir = '/var/log/linux-log-agent'
            if not os.path.exists(log_dir):
                self.logger.warning(f"Log directory does not exist: {log_dir}")
                try:
                    os.makedirs(log_dir, exist_ok=True)
                    self.logger.info(f"Created log directory: {log_dir}")
                except Exception as e:
                    self.logger.error(f"Cannot create log directory {log_dir}: {e}")
                    return False

            # Check if we can write to log directory
            test_file = os.path.join(log_dir, 'test_write.tmp')
            try:
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                self.logger.debug("Log directory write test passed")
            except Exception as e:
                self.logger.error(f"Cannot write to log directory {log_dir}: {e}")
                return False

            # Check config file accessibility
            if self.config_path:
                if not os.path.exists(self.config_path):
                    self.logger.error(f"Config file does not exist: {self.config_path}")
                    return False
                if not os.access(self.config_path, os.R_OK):
                    self.logger.error(f"Config file is not readable: {self.config_path}")
                    return False
                self.logger.debug(f"Config file accessible: {self.config_path}")

            # Check system log files accessibility (common ones)
            system_logs = ['/var/log/syslog', '/var/log/auth.log', '/var/log/kern.log', '/var/log/messages']
            accessible_logs = []
            for log_file in system_logs:
                if os.path.exists(log_file) and os.access(log_file, os.R_OK):
                    accessible_logs.append(log_file)

            self.logger.info(f"Accessible system logs: {accessible_logs}")

            return True

        except Exception as e:
            self.logger.error(f"Environment validation failed: {e}")
            self.logger.exception("Environment validation traceback:")
            return False

    def run(self) -> int:
        """
        Run the service.
        
        Returns:
            Exit code (0 for success, non-zero for error)
        """
        try:
            # Set up logging for service mode
            self._setup_service_logging()

            self.logger.info("Linux Log Collection Agent Service starting...")
            self.logger.debug(f"Using config path: {self.config_path}")
            self.logger.debug(f"Working directory: {os.getcwd()}")
            self.logger.debug(f"Python path: {sys.path}")

            # Validate environment before proceeding
            self.logger.info("Validating environment...")
            if not self._validate_environment():
                self.logger.error("Environment validation failed")
                return 1
            self.logger.info("Environment validation passed")

            # Initialize the agent with detailed error handling
            try:
                self.logger.info("Initializing Linux Log Collection Agent...")
                self.agent = LinuxLoggingAgent(
                    config_path=self.config_path,
                    enable_signals=False  # We handle signals in the service
                )
                self.logger.info("Agent initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize agent: {e}")
                self.logger.exception("Agent initialization traceback:")
                return 1

            # Start the agent with detailed error handling
            try:
                self.logger.info("Starting Linux Log Collection Agent...")
                if not self.agent.start():
                    self.logger.error("Agent start() method returned False")
                    return 1
                self.logger.info("Agent started successfully")
            except Exception as e:
                self.logger.error(f"Failed to start agent: {e}")
                self.logger.exception("Agent start traceback:")
                return 1

            self.logger.info("Linux Log Collection Agent Service started successfully")
            self._running = True
            
            # Main service loop
            while self._running:
                try:
                    time.sleep(1)
                    
                    # Check if agent is still running
                    if not self.agent._running:
                        self.logger.error("Agent stopped unexpectedly")
                        break
                    
                    # Log status periodically (every 5 minutes)
                    if int(time.time()) % 300 == 0:
                        status = self.agent.get_status()
                        self.logger.info(
                            f"Service status: Uptime {status['uptime_seconds']:.0f}s, "
                            f"Logs collected: {status['statistics']['logs_collected']}, "
                            f"Logs processed: {status['statistics']['logs_processed']}"
                        )
                
                except Exception as e:
                    self.logger.error(f"Error in service loop: {e}")
                    time.sleep(5)
            
            self.logger.info("Linux Log Collection Agent Service stopping...")
            
            if self.agent:
                self.agent.stop()
            
            self.logger.info("Linux Log Collection Agent Service stopped")
            return 0
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Service error: {e}")
            else:
                print(f"Service error: {e}")
            return 1
    
    def _setup_service_logging(self):
        """Set up logging for service mode."""
        try:
            # Create log directory
            log_dir = '/var/log/linux-log-agent'
            os.makedirs(log_dir, exist_ok=True)

            # Set up detailed logging for debugging
            logging.basicConfig(
                level=logging.DEBUG,  # More verbose for debugging
                format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                handlers=[
                    logging.FileHandler(os.path.join(log_dir, 'service.log')),
                    logging.FileHandler(os.path.join(log_dir, 'service_errors.log')),  # Separate error log
                    logging.StreamHandler()  # For systemd journal
                ]
            )

            self.logger = logging.getLogger('LinuxLogAgentService')
            self.logger.info("Service logging initialized successfully")

        except Exception as e:
            # Fallback to basic logging if setup fails
            logging.basicConfig(
                level=logging.DEBUG,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            self.logger = logging.getLogger('LinuxLogAgentService')
            self.logger.error(f"Failed to setup service logging: {e}")
            self.logger.info("Using fallback logging configuration")


def main():
    """Main entry point for systemd service."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Linux Log Collection Agent Service')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    
    args = parser.parse_args()
    
    service = LinuxLoggingAgentService(config_path=args.config)
    return service.run()


if __name__ == '__main__':
    sys.exit(main())
