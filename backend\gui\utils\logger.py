"""
Logging utilities for the GUI application
"""

import logging
import sys
from pathlib import Path
from datetime import datetime

def setup_gui_logger(name: str = "gui", level: str = "INFO") -> logging.Logger:
    """Setup logger for GUI application"""
    
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # File handler for detailed logs
    file_handler = logging.FileHandler(
        log_dir / f"gui_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    logger.addHandler(file_handler)
    
    # Console handler for important messages
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    # Error file handler
    error_handler = logging.FileHandler(
        log_dir / f"gui_errors_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    logger.addHandler(error_handler)
    
    return logger

class GUILogHandler(logging.Handler):
    """Custom log handler for displaying logs in GUI"""
    
    def __init__(self, callback=None):
        super().__init__()
        self.callback = callback
        self.logs = []
        self.max_logs = 1000
    
    def emit(self, record):
        """Emit a log record"""
        try:
            msg = self.format(record)
            
            # Add to internal buffer
            self.logs.append({
                'timestamp': datetime.fromtimestamp(record.created),
                'level': record.levelname,
                'message': msg,
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno
            })
            
            # Keep only recent logs
            if len(self.logs) > self.max_logs:
                self.logs = self.logs[-self.max_logs:]
            
            # Call callback if provided
            if self.callback:
                self.callback(self.logs[-1])
                
        except Exception:
            self.handleError(record)
    
    def get_logs(self, level_filter=None, limit=None):
        """Get logs with optional filtering"""
        logs = self.logs
        
        if level_filter:
            logs = [log for log in logs if log['level'] == level_filter]
        
        if limit:
            logs = logs[-limit:]
            
        return logs
    
    def clear_logs(self):
        """Clear all logs"""
        self.logs.clear()
