# GitLab CI/CD Configuration for Python Backend Agent - Simplified
# This configuration uses centralized templates from the cicd-templates project

# Include centralized templates
include:
  - project: 'spr888/cicd-templates'
    ref: main
    file:
      - 'templates/base.yml'
      - 'templates/python.yml'

# Project-specific variables
variables:
  PYTHON_VERSION: "3.9"
  PROJECT_NAME: "python-logging-agent"

# Simple project validation
validate_backend_structure:
  stage: validate
  extends: .base_job
  image: python:3.9
  script:
    - echo "Validating backend project structure..."
    - ls -la
    - echo "✓ Found main.py" && test -f main.py
    - echo "✓ Found requirements.txt" && test -f requirements.txt
    - echo "Backend structure validation passed"
