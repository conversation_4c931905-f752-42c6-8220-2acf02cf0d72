"""
Application Log Collector for Linux Log Collection Agent

This module collects application logs from various directories and files
such as Apache, Nginx, MySQL, PostgreSQL, and other application logs.
"""

import glob
import logging
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from .base_collector import BaseLogCollector


class ApplicationLogCollector(BaseLogCollector):
    """Collector for Linux application logs."""
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Initialize the application log collector.
        
        Args:
            config: Collector configuration
            logger: Logger instance
        """
        super().__init__(config, logger)
        
        # Application-specific configuration
        self.recursive = config.get('recursive', True)
        self.patterns = config.get('patterns', ['*.log', '*.err', 'access.log*', 'error.log*'])
        
        # Default application log paths if none specified
        if not config.get('paths'):
            self.config['paths'] = self._get_default_app_paths()
        
        # Discover log files
        self._log_files = self._discover_log_files()
        
        self.logger.info(f"Application log collector initialized with {len(self._log_files)} log files")
    
    def _get_default_app_paths(self) -> List[str]:
        """Get default application log paths."""
        default_paths = [
            '/var/log/apache2/',     # Apache logs
            '/var/log/httpd/',       # Apache logs (CentOS/RHEL)
            '/var/log/nginx/',       # Nginx logs
            '/var/log/mysql/',       # MySQL logs
            '/var/log/postgresql/',  # PostgreSQL logs
            '/var/log/redis/',       # Redis logs
            '/var/log/mongodb/',     # MongoDB logs
            '/var/log/docker/',      # Docker logs
            '/opt/*/logs/',          # Application logs in /opt
            '/var/log/app/',         # Generic application logs
        ]
        
        existing_paths = []
        for path in default_paths:
            # Handle wildcards
            if '*' in path:
                matching_paths = glob.glob(path)
                for match in matching_paths:
                    if os.path.exists(match) and os.access(match, os.R_OK):
                        existing_paths.append(match)
            else:
                if os.path.exists(path) and os.access(path, os.R_OK):
                    existing_paths.append(path)
        
        return existing_paths
    
    def _discover_log_files(self) -> List[str]:
        """Discover log files based on paths and patterns."""
        log_files = []
        
        for path in self.config.get('paths', []):
            if os.path.isfile(path):
                # Direct file path
                if os.access(path, os.R_OK):
                    log_files.append(path)
            elif os.path.isdir(path):
                # Directory path - search for log files
                log_files.extend(self._find_log_files_in_directory(path))
        
        return list(set(log_files))  # Remove duplicates
    
    def _find_log_files_in_directory(self, directory: str) -> List[str]:
        """Find log files in a directory based on patterns."""
        log_files = []
        
        try:
            for pattern in self.patterns:
                if self.recursive:
                    # Recursive search
                    search_pattern = os.path.join(directory, '**', pattern)
                    matches = glob.glob(search_pattern, recursive=True)
                else:
                    # Non-recursive search
                    search_pattern = os.path.join(directory, pattern)
                    matches = glob.glob(search_pattern)
                
                for match in matches:
                    if os.path.isfile(match) and os.access(match, os.R_OK):
                        log_files.append(match)
        
        except Exception as e:
            self.logger.error(f"Error searching directory {directory}: {e}")
        
        return log_files
    
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from application log files.
        
        Returns:
            List of collected log entries
        """
        collected_logs = []
        
        try:
            # Refresh log file list periodically
            if len(collected_logs) == 0:  # Only refresh when no logs collected
                self._log_files = self._discover_log_files()
            
            for log_file in self._log_files:
                try:
                    # Read new lines from the file
                    new_lines = self._read_file_from_position(log_file)
                    
                    if new_lines:
                        self.logger.debug(f"Processing {len(new_lines)} new lines from {log_file}")
                        
                        for line in new_lines:
                            log_entry = self._parse_application_log_line(line, log_file)
                            if log_entry:
                                # Add file-specific metadata
                                log_entry['additional_fields']['log_file'] = log_file
                                log_entry['additional_fields']['collector'] = 'application'
                                
                                # Sanitize message
                                log_entry['message'] = self._sanitize_message(log_entry['message'])
                                
                                collected_logs.append(log_entry)
                        
                        self.stats['files_processed'] += 1
                
                except Exception as e:
                    self.logger.error(f"Error processing application log file {log_file}: {e}")
                    self.stats['errors'] += 1
            
            if collected_logs:
                self.stats['logs_collected'] += len(collected_logs)
                self.stats['last_collection'] = datetime.now()
                self.logger.debug(f"Collected {len(collected_logs)} application log entries")
            
        except Exception as e:
            self.logger.error(f"Error in application log collection: {e}")
            self.stats['errors'] += 1
        
        return collected_logs
    
    def _parse_application_log_line(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """
        Parse an application log line with application-specific parsing.
        
        Args:
            line: Log line to parse
            log_file: Path to the log file
            
        Returns:
            Parsed log entry or None if parsing failed
        """
        if not line.strip():
            return None
        
        try:
            # Determine application type from file path
            app_type = self._determine_application_type(log_file)
            
            # Parse based on application type
            if app_type == 'apache':
                return self._parse_apache_log(line, log_file)
            elif app_type == 'nginx':
                return self._parse_nginx_log(line, log_file)
            elif app_type == 'mysql':
                return self._parse_mysql_log(line, log_file)
            elif app_type == 'postgresql':
                return self._parse_postgresql_log(line, log_file)
            else:
                return self._parse_generic_application_log(line, log_file, app_type)
        
        except Exception as e:
            self.logger.error(f"Error parsing application log line: {e}")
            return None
    
    def _determine_application_type(self, log_file: str) -> str:
        """Determine application type from log file path."""
        log_file_lower = log_file.lower()
        
        if 'apache' in log_file_lower or 'httpd' in log_file_lower:
            return 'apache'
        elif 'nginx' in log_file_lower:
            return 'nginx'
        elif 'mysql' in log_file_lower:
            return 'mysql'
        elif 'postgresql' in log_file_lower or 'postgres' in log_file_lower:
            return 'postgresql'
        elif 'redis' in log_file_lower:
            return 'redis'
        elif 'mongodb' in log_file_lower or 'mongo' in log_file_lower:
            return 'mongodb'
        elif 'docker' in log_file_lower:
            return 'docker'
        else:
            return 'generic'
    
    def _parse_apache_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse Apache log line."""
        # Apache access log format: IP - - [timestamp] "method path protocol" status size "referer" "user-agent"
        if 'access' in log_file.lower():
            return self._parse_apache_access_log(line, log_file)
        else:
            return self._parse_apache_error_log(line, log_file)
    
    def _parse_apache_access_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse Apache access log line."""
        # Common Log Format pattern
        pattern = r'^(\S+) \S+ \S+ \[([^\]]+)\] "([^"]*)" (\d+) (\S+)(?: "([^"]*)" "([^"]*)")?'
        match = re.match(pattern, line)
        
        if match:
            ip, timestamp_str, request, status, size, referer, user_agent = match.groups()
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%d/%b/%Y:%H:%M:%S %z')
            except ValueError:
                timestamp = datetime.now()
            
            # Parse request
            request_parts = request.split(' ', 2)
            method = request_parts[0] if len(request_parts) > 0 else ''
            path = request_parts[1] if len(request_parts) > 1 else ''
            protocol = request_parts[2] if len(request_parts) > 2 else ''
            
            # Determine log level based on status code
            status_code = int(status) if status.isdigit() else 0
            if status_code >= 500:
                log_level = 'error'
            elif status_code >= 400:
                log_level = 'warning'
            else:
                log_level = 'info'
            
            return {
                'timestamp': timestamp.isoformat(),
                'source': 'Apache',
                'source_type': 'application',
                'host': self.hostname,
                'log_level': log_level,
                'message': f"{method} {path} - {status}",
                'raw_data': line,
                'additional_fields': {
                    'application': 'apache',
                    'log_type': 'access',
                    'client_ip': ip,
                    'http_method': method,
                    'http_path': path,
                    'http_protocol': protocol,
                    'http_status': status_code,
                    'response_size': int(size) if size.isdigit() else 0,
                    'referer': referer,
                    'user_agent': user_agent
                }
            }
        
        return None
    
    def _parse_apache_error_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse Apache error log line."""
        # Apache error log format: [timestamp] [level] [pid] message
        pattern = r'^\[([^\]]+)\] \[([^\]]+)\] (?:\[pid (\d+)\])? (.+)$'
        match = re.match(pattern, line)
        
        if match:
            timestamp_str, level, pid, message = match.groups()
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%a %b %d %H:%M:%S.%f %Y')
            except ValueError:
                timestamp = datetime.now()
            
            # Map Apache log levels
            level_mapping = {
                'emerg': 'critical',
                'alert': 'critical',
                'crit': 'critical',
                'error': 'error',
                'warn': 'warning',
                'notice': 'info',
                'info': 'info',
                'debug': 'debug'
            }
            log_level = level_mapping.get(level.lower(), 'info')
            
            return {
                'timestamp': timestamp.isoformat(),
                'source': 'Apache',
                'source_type': 'application',
                'host': self.hostname,
                'log_level': log_level,
                'message': message,
                'raw_data': line,
                'additional_fields': {
                    'application': 'apache',
                    'log_type': 'error',
                    'apache_level': level,
                    'pid': int(pid) if pid else None
                }
            }
        
        return None
    
    def _parse_nginx_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse Nginx log line."""
        if 'access' in log_file.lower():
            return self._parse_nginx_access_log(line, log_file)
        else:
            return self._parse_nginx_error_log(line, log_file)
    
    def _parse_nginx_access_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse Nginx access log line."""
        # Similar to Apache access log
        pattern = r'^(\S+) \S+ \S+ \[([^\]]+)\] "([^"]*)" (\d+) (\S+)(?: "([^"]*)" "([^"]*)")?'
        match = re.match(pattern, line)
        
        if match:
            ip, timestamp_str, request, status, size, referer, user_agent = match.groups()
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%d/%b/%Y:%H:%M:%S %z')
            except ValueError:
                timestamp = datetime.now()
            
            # Parse request
            request_parts = request.split(' ', 2)
            method = request_parts[0] if len(request_parts) > 0 else ''
            path = request_parts[1] if len(request_parts) > 1 else ''
            
            # Determine log level
            status_code = int(status) if status.isdigit() else 0
            if status_code >= 500:
                log_level = 'error'
            elif status_code >= 400:
                log_level = 'warning'
            else:
                log_level = 'info'
            
            return {
                'timestamp': timestamp.isoformat(),
                'source': 'Nginx',
                'source_type': 'application',
                'host': self.hostname,
                'log_level': log_level,
                'message': f"{method} {path} - {status}",
                'raw_data': line,
                'additional_fields': {
                    'application': 'nginx',
                    'log_type': 'access',
                    'client_ip': ip,
                    'http_method': method,
                    'http_path': path,
                    'http_status': status_code,
                    'response_size': int(size) if size.isdigit() else 0,
                    'referer': referer,
                    'user_agent': user_agent
                }
            }
        
        return None
    
    def _parse_nginx_error_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse Nginx error log line."""
        # Nginx error log format: timestamp [level] pid#tid: message
        pattern = r'^(\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] (\d+)#(\d+): (.+)$'
        match = re.match(pattern, line)
        
        if match:
            timestamp_str, level, pid, tid, message = match.groups()
            
            # Parse timestamp
            try:
                timestamp = datetime.strptime(timestamp_str, '%Y/%m/%d %H:%M:%S')
            except ValueError:
                timestamp = datetime.now()
            
            # Map Nginx log levels
            level_mapping = {
                'emerg': 'critical',
                'alert': 'critical',
                'crit': 'critical',
                'error': 'error',
                'warn': 'warning',
                'notice': 'info',
                'info': 'info',
                'debug': 'debug'
            }
            log_level = level_mapping.get(level.lower(), 'info')
            
            return {
                'timestamp': timestamp.isoformat(),
                'source': 'Nginx',
                'source_type': 'application',
                'host': self.hostname,
                'log_level': log_level,
                'message': message,
                'raw_data': line,
                'additional_fields': {
                    'application': 'nginx',
                    'log_type': 'error',
                    'nginx_level': level,
                    'pid': int(pid),
                    'tid': int(tid)
                }
            }
        
        return None
    
    def _parse_mysql_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse MySQL log line."""
        return self._parse_generic_application_log(line, log_file, 'mysql')
    
    def _parse_postgresql_log(self, line: str, log_file: str) -> Optional[Dict[str, Any]]:
        """Parse PostgreSQL log line."""
        return self._parse_generic_application_log(line, log_file, 'postgresql')
    
    def _parse_generic_application_log(self, line: str, log_file: str, app_type: str) -> Optional[Dict[str, Any]]:
        """Parse generic application log line."""
        # Try to extract timestamp from the beginning of the line
        timestamp_patterns = [
            r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',  # YYYY-MM-DD HH:MM:SS
            r'^(\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2})',  # YYYY/MM/DD HH:MM:SS
            r'^(\w{3} \d{1,2} \d{2}:\d{2}:\d{2})',      # Mon DD HH:MM:SS
        ]
        
        timestamp = datetime.now()
        message = line
        
        for pattern in timestamp_patterns:
            match = re.match(pattern, line)
            if match:
                timestamp_str = match.group(1)
                try:
                    if '-' in timestamp_str:
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                    elif '/' in timestamp_str:
                        timestamp = datetime.strptime(timestamp_str, '%Y/%m/%d %H:%M:%S')
                    else:
                        # Add current year for syslog format
                        current_year = datetime.now().year
                        timestamp = datetime.strptime(f"{current_year} {timestamp_str}", '%Y %b %d %H:%M:%S')
                    
                    message = line[len(match.group(0)):].strip()
                    break
                except ValueError:
                    continue
        
        # Determine log level from message content
        log_level = self._determine_log_level(message)
        
        return {
            'timestamp': timestamp.isoformat(),
            'source': app_type.title(),
            'source_type': 'application',
            'host': self.hostname,
            'log_level': log_level,
            'message': message,
            'raw_data': line,
            'additional_fields': {
                'application': app_type,
                'log_type': 'generic'
            }
        }
    
    def start(self) -> None:
        """Start the application log collector."""
        super().start()
        
        if self._log_files:
            self.logger.info(f"Monitoring {len(self._log_files)} application log files")
            for log_file in self._log_files[:5]:  # Show first 5 files
                self.logger.debug(f"  - {log_file}")
            if len(self._log_files) > 5:
                self.logger.debug(f"  ... and {len(self._log_files) - 5} more files")
        else:
            self.logger.warning("No accessible application log files found")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get application log collector statistics."""
        stats = super().get_stats()
        stats['collector_type'] = 'application'
        stats['monitored_files'] = len(self._log_files)
        stats['search_paths'] = self.config.get('paths', [])
        stats['patterns'] = self.patterns
        return stats
