# ExLog Networking Configuration

## Overview

This document describes the networking configuration changes made to enable access to the ExLog application from external IP addresses on the local network, not just localhost.

## Changes Made

### 1. Docker Compose Configuration (`docker-compose.yml`)

**Frontend Service:**
- Updated environment variables to use relative URLs (`/api/v1`, `/ws`) instead of hardcoded localhost URLs
- Added fallback URLs for direct access scenarios
- This allows the frontend to work with both localhost and IP address access

**Backend Service:**
- Added `CORS_ORIGIN=*` environment variable to allow cross-origin requests from any origin
- This enables API access from different IP addresses and ports

### 2. Backend CORS Configuration (`backend/src/config/index.js`)

**Enhanced CORS Handling:**
- Implemented dynamic CORS origin validation
- Supports wildcard (`*`) for development environments
- Supports comma-separated multiple origins
- Automatically allows private network ranges (192.168.x.x, 10.x.x.x, 172.16-31.x.x) in development
- Maintains security by rejecting unauthorized origins

**Supported Origin Patterns:**
- `*` - Allow all origins (development only)
- `http://localhost:3000,http://*************:3000` - Multiple specific origins
- Automatic private network detection in development mode

### 3. Server Binding Configuration

**Backend Server (`backend/src/index.js`):**
- Changed server binding from default (localhost only) to `0.0.0.0` (all interfaces)
- Enables external network access to the API server

**WebSocket Server (`backend/src/websocket/index.js`):**
- Added `host: '0.0.0.0'` to WebSocket server configuration
- Enables external network access to WebSocket connections

### 4. Nginx Configuration (`nginx/nginx.conf`)

**Server Name:**
- Changed from `server_name localhost` to `server_name _`
- Accepts connections from any hostname/IP address

**WebSocket Routing:**
- Updated WebSocket location from `/ws/` to `/ws`
- Fixed proxy target to use container name and port

### 5. Frontend Configuration

**Vite Development Server (`frontend/vite.config.js`):**
- Changed host from `true` to `'0.0.0.0'` for explicit all-interface binding
- Updated proxy targets to use container names for Docker networking
- Added WebSocket proxy configuration

**Frontend Nginx (`frontend/nginx.conf`):**
- Changed server name to accept any host (`server_name _`)

### 6. Environment Configuration (`.env.example`)

**Updated Default Values:**
- CORS_ORIGIN examples for different scenarios
- Frontend URLs using relative paths for better network compatibility
- Documentation for production vs development configurations

## Network Access Scenarios

### Scenario 1: Localhost Access (Original)
- **URL:** `http://localhost` or `http://localhost:3000`
- **Status:** ✅ Still works as before

### Scenario 2: IP Address Access (New)
- **URL:** `http://*************` or `http://*************:3000`
- **Status:** ✅ Now works with these changes

### Scenario 3: Different Device on Network (New)
- **URL:** `http://[your-computer-ip]` from another device
- **Status:** ✅ Now works with these changes

## Security Considerations

### Development Environment
- CORS is configured to allow private network ranges automatically
- Wildcard CORS origin (`*`) is acceptable for development

### Production Environment
- **Important:** Change `CORS_ORIGIN=*` to specific allowed origins
- Example: `CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com`
- Consider implementing additional security measures:
  - IP whitelisting
  - Rate limiting (already implemented)
  - HTTPS enforcement
  - Firewall rules

## Testing the Configuration

### 1. Find Your Computer's IP Address

**Windows:**
```cmd
ipconfig
```

**Linux/Mac:**
```bash
ip addr show
# or
ifconfig
```

### 2. Access the Application

**Via Nginx Proxy (Recommended):**
- `http://[your-ip]` - Full application through nginx
- `http://[your-ip]/api/v1/health` - API health check

**Direct Service Access:**
- `http://[your-ip]:3000` - Frontend only
- `http://[your-ip]:5000/api/v1/health` - Backend API only

### 3. Test from Another Device

From another device on the same network:
- Open browser and navigate to `http://[computer-ip]`
- Try logging in with: `<EMAIL>` / `Admin123!`

## Troubleshooting

### Common Issues

**1. Connection Refused:**
- Check if Docker containers are running: `docker-compose ps`
- Verify firewall settings on host machine
- Ensure ports 80, 3000, 5000, 5001 are not blocked

**2. CORS Errors:**
- Check browser console for CORS-related errors
- Verify CORS_ORIGIN environment variable is set correctly
- Restart backend container after CORS changes

**3. WebSocket Connection Issues:**
- Check if WebSocket proxy is working: browser dev tools → Network → WS
- Verify nginx WebSocket configuration
- Check WebSocket container logs: `docker-compose logs websocket`

### Debugging Commands

```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs frontend
docker-compose logs backend
docker-compose logs nginx

# Restart specific service
docker-compose restart backend

# Rebuild and restart all services
docker-compose down
docker-compose up --build -d
```

## Rollback Instructions

If you need to revert to localhost-only access:

1. **Restore docker-compose.yml:**
   ```yaml
   environment:
     - REACT_APP_API_URL=http://localhost:5000/api/v1
     - REACT_APP_WS_URL=ws://localhost:5001
   ```

2. **Restore backend CORS:**
   ```javascript
   cors: {
     origin: 'http://localhost:3000',
     credentials: true,
   }
   ```

3. **Restore nginx server name:**
   ```nginx
   server_name localhost;
   ```

4. **Restart services:**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

## Additional Notes

- These changes maintain backward compatibility with localhost access
- The configuration is optimized for development environments
- For production deployment, review and tighten security settings
- Consider using environment-specific configuration files for different deployment scenarios
