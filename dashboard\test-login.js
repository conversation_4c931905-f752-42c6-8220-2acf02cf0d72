const bcrypt = require('bcryptjs');
const axios = require('axios');

async function testLogin() {
  console.log('=== ExLog Login Test ===\n');

  // Test 1: Generate correct password hash
  console.log('1. Generating password hash for "Admin123!"...');
  const password = 'Admin123!';
  const salt = await bcrypt.genSalt(12);
  const hash = await bcrypt.hash(password, salt);
  console.log(`Generated hash: ${hash}\n`);

  // Test 2: Verify the hash in the database matches
  console.log('2. Testing password verification...');
  const isValid = await bcrypt.compare(password, '$2a$12$cr6szGDjXG7haljTZSxM7uMhahvHz/A6xqjYjSVWhKvEmsMYdws5G');
  console.log(`Password verification result: ${isValid}\n`);

  // Test 3: Test login via API
  console.log('3. Testing login via API...');
  try {
    const response = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Admin123!'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Login successful!');
    console.log(`User: ${response.data.data.user.email}`);
    console.log(`Role: ${response.data.data.user.role}`);
    console.log(`Token: ${response.data.data.token.substring(0, 20)}...`);
  } catch (error) {
    console.log('❌ Login failed!');
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Error: ${error.response.data.message || error.response.data.error}`);
      console.log('Response data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log(`Error: ${error.message}`);
    }
  }

  console.log('\n4. Testing login from external IP simulation...');
  try {
    const response = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Admin123!'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://*************'
      }
    });

    console.log('✅ External IP login successful!');
  } catch (error) {
    console.log('❌ External IP login failed!');
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Error: ${error.response.data.message || error.response.data.error}`);
    } else {
      console.log(`Error: ${error.message}`);
    }
  }
}

testLogin().catch(console.error);
