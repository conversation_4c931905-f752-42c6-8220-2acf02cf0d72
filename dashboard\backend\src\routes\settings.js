const express = require('express');
const { body, validationResult } = require('express-validator');
const crypto = require('crypto');
const User = require('../models/User');
const SystemSettings = require('../models/SystemSettings');
const { authenticateToken, authorize } = require('../middleware/auth');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/settings/profile
 * @desc    Get user profile settings
 * @access  Private
 */
router.get('/profile', authenticateToken, catchAsync(async (req, res) => {
  const user = await User.findById(req.userId).select('-password -mfaSecret');
  
  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      profile: {
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        preferences: user.preferences,
        securitySettings: user.securitySettings,
        mfaEnabled: user.mfaEnabled,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/profile
 * @desc    Update user profile settings
 * @access  Private
 */
router.put('/profile', authenticateToken, [
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { firstName, lastName, email } = req.body;
  const user = await User.findById(req.userId);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Check if email is already taken by another user
  if (email && email !== user.email) {
    const existingUser = await User.findOne({ email, _id: { $ne: req.userId } });
    if (existingUser) {
      throw new AppError('Email is already taken', 400);
    }
  }

  // Update fields
  if (firstName) user.firstName = firstName;
  if (lastName) user.lastName = lastName;
  if (email) user.email = email;

  await user.save();

  logger.info(`User profile updated: ${user.email}`, { userId: req.userId });

  res.json({
    status: 'success',
    message: 'Profile updated successfully',
    data: {
      profile: {
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
      },
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/preferences
 * @desc    Update user preferences
 * @access  Private
 */
router.put('/preferences', authenticateToken, [
  body('theme')
    .optional()
    .isIn(['light', 'dark', 'auto'])
    .withMessage('Theme must be light, dark, or auto'),
  body('timezone')
    .optional()
    .isString()
    .withMessage('Timezone must be a valid string'),
  body('language')
    .optional()
    .isIn(['en', 'es', 'fr', 'de', 'ja', 'zh'])
    .withMessage('Language must be a supported language code'),
  body('dateFormat')
    .optional()
    .isIn(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'DD-MM-YYYY'])
    .withMessage('Date format must be a supported format'),
  body('timeFormat')
    .optional()
    .isIn(['12h', '24h'])
    .withMessage('Time format must be 12h or 24h'),
  body('dashboardRefreshInterval')
    .optional()
    .isInt({ min: 10, max: 300 })
    .withMessage('Dashboard refresh interval must be between 10 and 300 seconds'),
  body('logsPerPage')
    .optional()
    .isInt({ min: 10, max: 200 })
    .withMessage('Logs per page must be between 10 and 200'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const user = await User.findById(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Update preferences
  const allowedPreferences = [
    'theme', 'timezone', 'language', 'dateFormat', 'timeFormat',
    'dashboardRefreshInterval', 'logsPerPage'
  ];

  allowedPreferences.forEach(pref => {
    if (req.body[pref] !== undefined) {
      user.preferences[pref] = req.body[pref];
    }
  });

  // Handle nested notification preferences
  if (req.body.notifications) {
    Object.keys(req.body.notifications).forEach(key => {
      if (user.preferences.notifications[key] !== undefined) {
        if (typeof req.body.notifications[key] === 'object') {
          Object.assign(user.preferences.notifications[key], req.body.notifications[key]);
        } else {
          user.preferences.notifications[key] = req.body.notifications[key];
        }
      }
    });
  }

  // Handle dashboard preferences
  if (req.body.dashboard) {
    Object.keys(req.body.dashboard).forEach(key => {
      if (user.preferences.dashboard[key] !== undefined) {
        user.preferences.dashboard[key] = req.body.dashboard[key];
      }
    });
  }

  // Handle security preferences
  if (req.body.security) {
    Object.keys(req.body.security).forEach(key => {
      if (user.preferences.security[key] !== undefined) {
        user.preferences.security[key] = req.body.security[key];
      }
    });
  }

  await user.save();

  logger.info(`User preferences updated: ${user.email}`, { userId: req.userId });

  res.json({
    status: 'success',
    message: 'Preferences updated successfully',
    data: {
      preferences: user.preferences,
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/password
 * @desc    Change user password
 * @access  Private
 */
router.put('/password', authenticateToken, [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match');
      }
      return true;
    }),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { currentPassword, newPassword } = req.body;
  const user = await User.findById(req.userId).select('+password');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new AppError('Current password is incorrect', 400);
  }

  // Update password
  user.password = newPassword;
  user.securitySettings.passwordLastChanged = new Date();
  user.securitySettings.passwordChangeRequired = false;

  await user.save();

  logger.info(`Password changed for user: ${user.email}`, { userId: req.userId });

  res.json({
    status: 'success',
    message: 'Password changed successfully',
  });
}));

/**
 * @route   GET /api/v1/settings/api-keys
 * @desc    Get user API keys
 * @access  Private
 */
router.get('/api-keys', authenticateToken, catchAsync(async (req, res) => {
  const user = await User.findById(req.userId);
  
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Return API keys with masked keys for security
  const apiKeys = user.apiKeys.map(apiKey => ({
    _id: apiKey._id,
    name: apiKey.name,
    description: apiKey.description,
    key: `${apiKey.key.substring(0, 8)}...${apiKey.key.substring(apiKey.key.length - 4)}`,
    createdAt: apiKey.createdAt,
    expiresAt: apiKey.expiresAt,
    lastUsed: apiKey.lastUsed,
    usageCount: apiKey.usageCount,
    isActive: apiKey.isActive,
    permissions: apiKey.permissions,
    ipWhitelist: apiKey.ipWhitelist,
  }));

  res.json({
    status: 'success',
    data: {
      apiKeys,
      total: apiKeys.length,
    },
  });
}));

/**
 * @route   POST /api/v1/settings/api-keys
 * @desc    Generate new API key
 * @access  Private
 */
router.post('/api-keys', authenticateToken, [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('API key name must be between 1 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Expiration date must be a valid ISO8601 date'),
  body('permissions')
    .optional()
    .isArray()
    .withMessage('Permissions must be an array'),
  body('ipWhitelist')
    .optional()
    .isArray()
    .withMessage('IP whitelist must be an array'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { name, description, expiresAt, permissions, ipWhitelist } = req.body;
  const user = await User.findById(req.userId);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Check if user has reached the maximum number of API keys
  const maxKeys = 10; // This could come from system settings
  if (user.apiKeys.length >= maxKeys) {
    throw new AppError(`Maximum number of API keys (${maxKeys}) reached`, 400);
  }

  // Generate secure API key
  const apiKey = crypto.randomBytes(32).toString('hex');

  // Create new API key object
  const newApiKey = {
    name,
    description: description || '',
    key: apiKey,
    expiresAt: expiresAt ? new Date(expiresAt) : null,
    permissions: permissions || [],
    ipWhitelist: ipWhitelist || [],
    isActive: true,
  };

  user.apiKeys.push(newApiKey);
  await user.save();

  logger.info(`New API key created: ${name}`, { userId: req.userId });

  res.status(201).json({
    status: 'success',
    message: 'API key created successfully',
    data: {
      apiKey: {
        _id: user.apiKeys[user.apiKeys.length - 1]._id,
        name,
        description,
        key: apiKey, // Return full key only on creation
        expiresAt: newApiKey.expiresAt,
        permissions: newApiKey.permissions,
        ipWhitelist: newApiKey.ipWhitelist,
        createdAt: newApiKey.createdAt,
      },
    },
  });
}));

/**
 * @route   PUT /api/v1/settings/api-keys/:keyId
 * @desc    Update API key
 * @access  Private
 */
router.put('/api-keys/:keyId', authenticateToken, [
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('API key name must be between 1 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('permissions')
    .optional()
    .isArray()
    .withMessage('Permissions must be an array'),
  body('ipWhitelist')
    .optional()
    .isArray()
    .withMessage('IP whitelist must be an array'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { keyId } = req.params;
  const { name, description, isActive, permissions, ipWhitelist } = req.body;

  const user = await User.findById(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  const apiKey = user.apiKeys.id(keyId);
  if (!apiKey) {
    throw new AppError('API key not found', 404);
  }

  // Update fields
  if (name !== undefined) apiKey.name = name;
  if (description !== undefined) apiKey.description = description;
  if (isActive !== undefined) apiKey.isActive = isActive;
  if (permissions !== undefined) apiKey.permissions = permissions;
  if (ipWhitelist !== undefined) apiKey.ipWhitelist = ipWhitelist;

  await user.save();

  logger.info(`API key updated: ${apiKey.name}`, { userId: req.userId, keyId });

  res.json({
    status: 'success',
    message: 'API key updated successfully',
    data: {
      apiKey: {
        _id: apiKey._id,
        name: apiKey.name,
        description: apiKey.description,
        isActive: apiKey.isActive,
        permissions: apiKey.permissions,
        ipWhitelist: apiKey.ipWhitelist,
        lastUsed: apiKey.lastUsed,
        usageCount: apiKey.usageCount,
      },
    },
  });
}));

/**
 * @route   DELETE /api/v1/settings/api-keys/:keyId
 * @desc    Delete API key
 * @access  Private
 */
router.delete('/api-keys/:keyId', authenticateToken, catchAsync(async (req, res) => {
  const { keyId } = req.params;

  const user = await User.findById(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  const apiKey = user.apiKeys.id(keyId);
  if (!apiKey) {
    throw new AppError('API key not found', 404);
  }

  const keyName = apiKey.name;
  user.apiKeys.pull(keyId);
  await user.save();

  logger.info(`API key deleted: ${keyName}`, { userId: req.userId, keyId });

  res.json({
    status: 'success',
    message: 'API key deleted successfully',
  });
}));

/**
 * @route   GET /api/v1/settings/sessions
 * @desc    Get user active sessions
 * @access  Private
 */
router.get('/sessions', authenticateToken, catchAsync(async (req, res) => {
  const user = await User.findById(req.userId);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Filter active sessions and sort by last activity
  const activeSessions = user.sessions
    .filter(session => session.isActive)
    .sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity));

  res.json({
    status: 'success',
    data: {
      sessions: activeSessions,
      total: activeSessions.length,
    },
  });
}));

/**
 * @route   GET /api/v1/settings/sessions/all
 * @desc    Get all active sessions across all users (admin only)
 * @access  Private (Admin)
 */
router.get('/sessions/all', authenticateToken, authorize(['system_admin', 'manage_users']), catchAsync(async (req, res) => {
  // Get all users with active sessions
  const users = await User.find({
    'sessions.isActive': true
  }).select('_id email firstName lastName role sessions');

  const allSessions = [];

  users.forEach(user => {
    const activeSessions = user.sessions
      .filter(session => session.isActive)
      .map(session => ({
        ...session.toObject(),
        userId: user._id,
        userEmail: user.email,
        userName: `${user.firstName} ${user.lastName}`,
        userRole: user.role
      }));

    allSessions.push(...activeSessions);
  });

  // Sort by last activity (newest first)
  allSessions.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity));

  res.json({
    status: 'success',
    data: {
      sessions: allSessions,
      total: allSessions.length,
    },
  });
}));

/**
 * @route   DELETE /api/v1/settings/sessions/:sessionId
 * @desc    Terminate a specific session
 * @access  Private
 */
router.delete('/sessions/:sessionId', authenticateToken, catchAsync(async (req, res) => {
  const { sessionId } = req.params;

  const user = await User.findById(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  const session = user.sessions.find(s => s.sessionId === sessionId);
  if (!session) {
    throw new AppError('Session not found', 404);
  }

  session.isActive = false;
  await user.save();

  logger.info(`Session terminated: ${sessionId}`, { userId: req.userId });

  res.json({
    status: 'success',
    message: 'Session terminated successfully',
  });
}));

/**
 * @route   DELETE /api/v1/settings/sessions
 * @desc    Terminate all other sessions (except current)
 * @access  Private
 */
router.delete('/sessions', authenticateToken, catchAsync(async (req, res) => {
  const user = await User.findById(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Get current session ID from token or request
  const currentSessionId = req.sessionId; // This would need to be set in auth middleware

  let terminatedCount = 0;
  user.sessions.forEach(session => {
    if (session.isActive && session.sessionId !== currentSessionId) {
      session.isActive = false;
      terminatedCount++;
    }
  });

  await user.save();

  logger.info(`${terminatedCount} sessions terminated`, { userId: req.userId });

  res.json({
    status: 'success',
    message: `${terminatedCount} sessions terminated successfully`,
    data: {
      terminatedCount,
    },
  });
}));

/**
 * @route   GET /api/v1/settings/login-history
 * @desc    Get user login history
 * @access  Private
 */
router.get('/login-history', authenticateToken, catchAsync(async (req, res) => {
  const { page = 1, limit = 20 } = req.query;

  const user = await User.findById(req.userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Sort login history by timestamp (newest first)
  const sortedHistory = user.loginHistory
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Implement pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedHistory = sortedHistory.slice(startIndex, endIndex);

  res.json({
    status: 'success',
    data: {
      loginHistory: paginatedHistory,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(sortedHistory.length / limit),
        totalItems: sortedHistory.length,
        itemsPerPage: parseInt(limit),
      },
    },
  });
}));

module.exports = router;
