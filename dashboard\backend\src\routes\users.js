const express = require('express');
const mongoose = require('mongoose');
const { body, query, validationResult } = require('express-validator');
const User = require('../models/User');
const { catchAsync, AppError, handleValidationError } = require('../middleware/errorHandler');
const { authorize, authorizeRoles, authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/users
 * @desc    Get all users (admin only)
 * @access  Private
 */
router.get('/', authorize(['view_users']), [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('role')
    .optional({ checkFalsy: true })
    .isIn(['admin', 'security_analyst', 'compliance_officer', 'executive'])
    .withMessage('Invalid role'),
  query('status')
    .optional({ checkFalsy: true })
    .isIn(['active', 'inactive', 'locked'])
    .withMessage('Invalid status'),
  query('search')
    .optional({ checkFalsy: true })
    .isString()
    .withMessage('Search must be a string'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const {
    page = 1,
    limit = 20,
    role,
    status,
    search,
  } = req.query;

  // Build query
  const query = {};
  if (role) query.role = role;
  if (status) query.status = status;
  if (search) {
    query.$or = [
      { username: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
    ];
  }

  const skip = (page - 1) * limit;

  const [users, totalCount] = await Promise.all([
    User.find(query)
      .select('-password -mfaSecret')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit)),
    User.countDocuments(query),
  ]);

  const totalPages = Math.ceil(totalCount / limit);

  res.json({
    status: 'success',
    data: {
      users,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        limit: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    },
  });
}));

/**
 * @route   GET /api/v1/users/stats
 * @desc    Get user statistics
 * @access  Private
 */
router.get('/stats', authorize(['view_users']), catchAsync(async (req, res) => {
  const [
    totalUsers,
    activeUsers,
    inactiveUsers,
    lockedUsers,
    roleDistribution,
    recentUsers,
  ] = await Promise.all([
    User.countDocuments(),
    User.countDocuments({ status: 'active' }),
    User.countDocuments({ status: 'inactive' }),
    User.countDocuments({ status: 'locked' }),
    User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
        },
      },
    ]),
    User.find()
      .select('-password -mfaSecret')
      .sort({ createdAt: -1 })
      .limit(5),
  ]);

  const stats = {
    totalUsers,
    statusDistribution: {
      active: activeUsers,
      inactive: inactiveUsers,
      locked: lockedUsers,
    },
    roleDistribution: roleDistribution.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {}),
    recentUsers,
  };

  res.json({
    status: 'success',
    data: {
      stats,
    },
  });
}));

/**
 * @route   GET /api/v1/users/:id
 * @desc    Get user by ID
 * @access  Private
 */
router.get('/:id', authorize(['view_users']), catchAsync(async (req, res) => {
  const user = await User.findById(req.params.id).select('-password -mfaSecret');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      user,
    },
  });
}));

/**
 * @route   POST /api/v1/users
 * @desc    Create new user (admin only)
 * @access  Private
 */
router.post('/', authorize(['manage_users']), [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long'),
  body('firstName')
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required'),
  body('lastName')
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required'),
  body('role')
    .isIn(['admin', 'security_analyst', 'compliance_officer', 'executive'])
    .withMessage('Invalid role'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { username, email, password, firstName, lastName, role } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    $or: [{ email }, { username }],
  });

  if (existingUser) {
    throw new AppError('User with this email or username already exists', 400);
  }

  const user = new User({
    username,
    email,
    password,
    firstName,
    lastName,
    role,
  });

  await user.save();

  const userResponse = user.toObject();
  delete userResponse.password;

  res.status(201).json({
    status: 'success',
    message: 'User created successfully',
    data: {
      user: userResponse,
    },
  });
}));

/**
 * @route   PUT /api/v1/users/:id
 * @desc    Update user
 * @access  Private
 */
router.put('/:id', authorize(['manage_users']), [
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be less than 50 characters'),
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be less than 50 characters'),
  body('role')
    .optional()
    .isIn(['admin', 'security_analyst', 'compliance_officer', 'executive'])
    .withMessage('Invalid role'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'locked'])
    .withMessage('Invalid status'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const allowedUpdates = ['firstName', 'lastName', 'role', 'status', 'permissions'];
  const updates = {};

  Object.keys(req.body).forEach(key => {
    if (allowedUpdates.includes(key)) {
      updates[key] = req.body[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.params.id,
    updates,
    { new: true, runValidators: true }
  ).select('-password -mfaSecret');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json({
    status: 'success',
    message: 'User updated successfully',
    data: {
      user,
    },
  });
}));

/**
 * @route   PUT /api/v1/users/:id/status
 * @desc    Update user status
 * @access  Private
 */
router.put('/:id/status', authorize(['manage_users']), [
  body('status')
    .isIn(['active', 'inactive', 'locked'])
    .withMessage('Invalid status'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { status } = req.body;

  const user = await User.findByIdAndUpdate(
    req.params.id,
    { status },
    { new: true, runValidators: true }
  ).select('-password -mfaSecret');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json({
    status: 'success',
    message: 'User status updated successfully',
    data: {
      user,
    },
  });
}));

/**
 * @route   POST /api/v1/users/:id/reset-password
 * @desc    Reset user password
 * @access  Private
 */
router.post('/:id/reset-password', authorize(['manage_users']), [
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long'),
  body('forceChange')
    .optional()
    .isBoolean()
    .withMessage('Force change must be a boolean'),
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw handleValidationError(errors);
  }

  const { newPassword, forceChange = true } = req.body;

  const user = await User.findById(req.params.id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  user.password = newPassword;
  user.passwordChangeRequired = forceChange;
  user.securitySettings.passwordLastChanged = new Date();
  await user.save();

  res.json({
    status: 'success',
    message: 'Password reset successfully',
  });
}));

/**
 * @route   DELETE /api/v1/users/:id
 * @desc    Delete user
 * @access  Private
 */
router.delete('/:id', authorize(['manage_users']), catchAsync(async (req, res) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Prevent deletion of the last admin user
  if (user.role === 'admin') {
    const adminCount = await User.countDocuments({ role: 'admin' });
    if (adminCount <= 1) {
      throw new AppError('Cannot delete the last admin user', 400);
    }
  }

  await User.findByIdAndDelete(req.params.id);

  res.json({
    status: 'success',
    message: 'User deleted successfully',
  });
}));

/**
 * @route   GET /api/v1/users/system/login-activity
 * @desc    Get system-wide login activity for all users (admin only)
 * @access  Private (Admin)
 */
router.get('/system/login-activity', authenticateToken, authorize(['view_users', 'system_admin']), [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('success')
    .optional()
    .isBoolean()
    .withMessage('Success filter must be a boolean'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be in ISO8601 format'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be in ISO8601 format'),
  query('userId')
    .optional()
    .isMongoId()
    .withMessage('User ID must be a valid MongoDB ObjectId'),
], catchAsync(async (req, res) => {
  const { page = 1, limit = 50, success, startDate, endDate, userId } = req.query;

  // Build aggregation pipeline to get all login attempts across all users
  const pipeline = [
    // Unwind the loginHistory array
    { $unwind: '$loginHistory' },

    // Match filters
    {
      $match: {
        ...(success !== undefined && { 'loginHistory.success': success === 'true' }),
        ...(startDate && { 'loginHistory.timestamp': { $gte: new Date(startDate) } }),
        ...(endDate && { 'loginHistory.timestamp': { ...{ $gte: new Date(startDate) }, $lte: new Date(endDate) } }),
        ...(userId && { '_id': new mongoose.Types.ObjectId(userId) }),
      }
    },

    // Project the fields we need
    {
      $project: {
        userId: '$_id',
        username: '$username',
        email: '$email',
        firstName: '$firstName',
        lastName: '$lastName',
        role: '$role',
        loginHistory: 1,
      }
    },

    // Sort by timestamp (newest first)
    { $sort: { 'loginHistory.timestamp': -1 } },
  ];

  // Get total count for pagination
  const totalPipeline = [...pipeline, { $count: 'total' }];
  const totalResult = await User.aggregate(totalPipeline);
  const totalItems = totalResult.length > 0 ? totalResult[0].total : 0;

  // Add pagination
  const skip = (page - 1) * limit;
  pipeline.push({ $skip: skip }, { $limit: parseInt(limit) });

  // Execute the aggregation
  const loginActivities = await User.aggregate(pipeline);

  // Calculate statistics
  const statsPipeline = [
    { $unwind: '$loginHistory' },
    {
      $group: {
        _id: null,
        totalLogins: { $sum: 1 },
        successfulLogins: {
          $sum: { $cond: [{ $eq: ['$loginHistory.success', true] }, 1, 0] }
        },
        failedLogins: {
          $sum: { $cond: [{ $eq: ['$loginHistory.success', false] }, 1, 0] }
        },
        uniqueUsers: { $addToSet: '$_id' },
      }
    },
    {
      $project: {
        totalLogins: 1,
        successfulLogins: 1,
        failedLogins: 1,
        uniqueUsers: { $size: '$uniqueUsers' },
        successRate: {
          $cond: [
            { $gt: ['$totalLogins', 0] },
            { $multiply: [{ $divide: ['$successfulLogins', '$totalLogins'] }, 100] },
            0
          ]
        }
      }
    }
  ];

  const statsResult = await User.aggregate(statsPipeline);
  const stats = statsResult.length > 0 ? statsResult[0] : {
    totalLogins: 0,
    successfulLogins: 0,
    failedLogins: 0,
    uniqueUsers: 0,
    successRate: 0
  };

  res.json({
    status: 'success',
    data: {
      loginActivities: loginActivities.map(activity => ({
        user: {
          id: activity.userId,
          username: activity.username,
          email: activity.email,
          firstName: activity.firstName,
          lastName: activity.lastName,
          role: activity.role,
        },
        ...activity.loginHistory,
      })),
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: page * limit < totalItems,
        hasPrevPage: page > 1,
      },
      stats,
    },
  });
}));

/**
 * @route   GET /api/v1/users/:id/activity
 * @desc    Get user activity (login history and sessions)
 * @access  Private (Admin or own profile)
 */
router.get('/:id/activity', authenticateToken, catchAsync(async (req, res) => {
  const { id } = req.params;

  // Allow users to view their own activity or require view_users permission
  if (req.userId !== id && !req.user.getEffectivePermissions().includes('view_users') && !req.user.getEffectivePermissions().includes('system_admin')) {
    throw new AppError('Access denied', 403);
  }

  const user = await User.findById(id).select('loginHistory sessions loginAttempts lastLogin');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Sort login history by timestamp (newest first)
  const sortedLoginHistory = user.loginHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Sort sessions by last activity (newest first)
  const sortedSessions = user.sessions.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity));

  res.json({
    status: 'success',
    data: {
      loginHistory: sortedLoginHistory,
      sessions: sortedSessions,
      loginAttempts: user.loginAttempts,
      lastLogin: user.lastLogin,
      stats: {
        totalLogins: user.loginHistory.filter(h => h.success).length,
        failedLogins: user.loginHistory.filter(h => !h.success).length,
        activeSessions: user.sessions.filter(s => s.isActive).length,
        totalSessions: user.sessions.length,
      }
    },
  });
}));

/**
 * @route   POST /api/v1/users/:id/sessions/:sessionId/terminate
 * @desc    Terminate a specific user session
 * @access  Private (Admin or own profile)
 */
router.post('/:id/sessions/:sessionId/terminate', authenticateToken, catchAsync(async (req, res) => {
  const { id, sessionId } = req.params;

  // Allow users to terminate their own sessions or require manage_users permission
  if (req.userId !== id && !req.user.getEffectivePermissions().includes('manage_users') && !req.user.getEffectivePermissions().includes('system_admin')) {
    throw new AppError('Access denied', 403);
  }

  const user = await User.findById(id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  const session = user.sessions.find(s => s.sessionId === sessionId);

  if (!session) {
    throw new AppError('Session not found', 404);
  }

  session.isActive = false;
  await user.save();

  logger.info(`Session ${sessionId} terminated for user ${user.email} by ${req.user.email}`);

  res.json({
    status: 'success',
    message: 'Session terminated successfully',
  });
}));

/**
 * @route   POST /api/v1/users/:id/sessions/terminate-all
 * @desc    Terminate all user sessions except current
 * @access  Private (Admin or own profile)
 */
router.post('/:id/sessions/terminate-all', authenticateToken, catchAsync(async (req, res) => {
  const { id } = req.params;

  // Allow users to terminate their own sessions or require manage_users permission
  if (req.userId !== id && !req.user.getEffectivePermissions().includes('manage_users') && !req.user.getEffectivePermissions().includes('system_admin')) {
    throw new AppError('Access denied', 403);
  }

  const user = await User.findById(id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Terminate all sessions except the current one
  let terminatedCount = 0;
  user.sessions.forEach(session => {
    if (session.isActive && session.sessionId !== req.sessionId) {
      session.isActive = false;
      terminatedCount++;
    }
  });

  await user.save();

  logger.info(`${terminatedCount} sessions terminated for user ${user.email} by ${req.user.email}`);

  res.json({
    status: 'success',
    message: `${terminatedCount} sessions terminated successfully`,
    data: {
      terminatedCount
    }
  });
}));

module.exports = router;
