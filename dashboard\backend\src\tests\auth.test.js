const request = require('supertest');
const mongoose = require('mongoose');
const ExLogServer = require('../index');
const User = require('../models/User');

describe('Authentication API', () => {
  let app;
  let server;

  beforeAll(async () => {
    // Create server instance
    server = new ExLogServer();
    app = server.app;

    // Setup middleware and routes
    server.setupMiddleware();
    server.setupRoutes();
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({});
    
    // Close database connections
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
    }
  });

  beforeEach(async () => {
    // Clear users before each test
    await User.deleteMany({});
  });

  describe('POST /api/v1/auth/register', () => {
    const validUserData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      role: 'security_analyst',
    };

    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(validUserData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('User registered successfully');
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.user.username).toBe('testuser');
      expect(response.body.data.user.role).toBe('security_analyst');
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
      expect(response.body.data.user.password).toBeUndefined();

      // Verify user was saved to database
      const savedUser = await User.findOne({ email: '<EMAIL>' });
      expect(savedUser).toBeTruthy();
      expect(savedUser.username).toBe('testuser');
    });

    it('should reject registration with invalid email', async () => {
      const invalidData = { ...validUserData, email: 'invalid-email' };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('Validation Error');
    });

    it('should reject registration with weak password', async () => {
      const invalidData = { ...validUserData, password: 'weak' };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.error).toBe('Validation Error');
    });

    it('should reject registration with duplicate email', async () => {
      // Create first user
      await request(app)
        .post('/api/v1/auth/register')
        .send(validUserData)
        .expect(201);

      // Try to create second user with same email
      const duplicateData = { ...validUserData, username: 'differentuser' };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(duplicateData)
        .expect(400);

      expect(response.body.message).toContain('already exists');
    });

    it('should reject registration with duplicate username', async () => {
      // Create first user
      await request(app)
        .post('/api/v1/auth/register')
        .send(validUserData)
        .expect(201);

      // Try to create second user with same username
      const duplicateData = { ...validUserData, email: '<EMAIL>' };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(duplicateData)
        .expect(400);

      expect(response.body.message).toContain('already exists');
    });

    it('should set default role when not provided', async () => {
      const dataWithoutRole = { ...validUserData };
      delete dataWithoutRole.role;

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(dataWithoutRole)
        .expect(201);

      expect(response.body.data.user.role).toBe('security_analyst');
    });
  });

  describe('POST /api/v1/auth/login', () => {
    let testUser;

    beforeEach(async () => {
      // Create test user
      testUser = new User({
        username: 'logintest',
        email: '<EMAIL>',
        password: 'LoginPassword123!',
        firstName: 'Login',
        lastName: 'Test',
        role: 'security_analyst',
        status: 'active',
      });
      await testUser.save();
    });

    it('should login successfully with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'LoginPassword123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Login successful');
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
      expect(response.body.data.user.password).toBeUndefined();
    });

    it('should reject login with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'LoginPassword123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should reject login with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should reject login for inactive user', async () => {
      // Update user status to inactive
      await User.findByIdAndUpdate(testUser._id, { status: 'inactive' });

      const loginData = {
        email: '<EMAIL>',
        password: 'LoginPassword123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body.message).toBe('Account is not active');
    });

    it('should validate email format', async () => {
      const loginData = {
        email: 'invalid-email',
        password: 'LoginPassword123!',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(400);

      expect(response.body.error).toBe('Validation Error');
    });

    it('should require password', async () => {
      const loginData = {
        email: '<EMAIL>',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(400);

      expect(response.body.error).toBe('Validation Error');
    });
  });

  describe('GET /api/v1/auth/me', () => {
    let testUser;
    let authToken;

    beforeEach(async () => {
      // Create test user
      testUser = new User({
        username: 'metest',
        email: '<EMAIL>',
        password: 'MePassword123!',
        firstName: 'Me',
        lastName: 'Test',
        role: 'admin',
        status: 'active',
      });
      await testUser.save();

      // Generate auth token
      const jwt = require('jsonwebtoken');
      const config = require('../config');
      authToken = jwt.sign({ userId: testUser._id }, config.jwt.secret, {
        expiresIn: '1h',
      });
    });

    it('should return current user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.user.email).toBe('<EMAIL>');
      expect(response.body.data.user.username).toBe('metest');
      expect(response.body.data.user.role).toBe('admin');
      expect(response.body.data.user.password).toBeUndefined();
    });

    it('should reject request without token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .expect(401);

      expect(response.body.error).toBe('Unauthorized');
      expect(response.body.message).toBe('Access token is required');
    });

    it('should reject request with invalid token', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.error).toBe('Unauthorized');
      expect(response.body.message).toBe('Invalid token');
    });

    it('should reject request with expired token', async () => {
      const jwt = require('jsonwebtoken');
      const config = require('../config');
      const expiredToken = jwt.sign({ userId: testUser._id }, config.jwt.secret, {
        expiresIn: '-1h', // Expired 1 hour ago
      });

      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body.error).toBe('Unauthorized');
      expect(response.body.message).toBe('Token expired');
    });
  });

  describe('POST /api/v1/auth/refresh', () => {
    let testUser;
    let refreshToken;

    beforeEach(async () => {
      // Create test user
      testUser = new User({
        username: 'refreshtest',
        email: '<EMAIL>',
        password: 'RefreshPassword123!',
        firstName: 'Refresh',
        lastName: 'Test',
        role: 'security_analyst',
        status: 'active',
      });
      await testUser.save();

      // Generate refresh token
      const jwt = require('jsonwebtoken');
      const config = require('../config');
      refreshToken = jwt.sign(
        { userId: testUser._id, type: 'refresh' },
        config.jwt.secret,
        { expiresIn: '7d' }
      );
    });

    it('should refresh token successfully with valid refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Token refreshed successfully');
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
      expect(response.body.data.token).not.toBe(refreshToken);
    });

    it('should reject request without refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({})
        .expect(400);

      expect(response.body.error).toBe('Validation Error');
    });

    it('should reject request with invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body.message).toBe('Invalid or expired refresh token');
    });

    it('should reject regular access token as refresh token', async () => {
      const jwt = require('jsonwebtoken');
      const config = require('../config');
      const accessToken = jwt.sign({ userId: testUser._id }, config.jwt.secret, {
        expiresIn: '1h',
      });

      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refreshToken: accessToken })
        .expect(401);

      expect(response.body.message).toBe('Invalid refresh token');
    });
  });
});
