{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-19 14:46:02:462"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 14:46:09:469"}
{"cause":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"mongodb:27017":{"$clusterTime":null,"address":"mongodb:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":701757,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"errorLabelSet":{},"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to MongoDB: getaddrinfo EAI_AGAIN mongodb\u001b[39m","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"mongodb:27017":{"$clusterTime":null,"address":"mongodb:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":701757,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":null,"stale":false,"type":"Unknown"},"stack":"MongooseServerSelectionError: getaddrinfo EAI_AGAIN mongodb\n    at _handleConnectionErrors (/app/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/app/node_modules/mongoose/lib/connection.js:1096:11)\n    at async DatabaseManager.connectMongoDB (/app/src/config/database.js:19:7)\n    at async Promise.allSettled (index 0)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 14:46:32:4632"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:48:35:4835","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:48:54:4854","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:50:27:5027","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:51:20:5120","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:52:23:5223","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Operation `users.findOne()` buffering timed out after 10000ms\u001b[39m","method":"POST","stack":"MongooseError: Operation `users.findOne()` buffering timed out after 10000ms\n    at Timeout.<anonymous> (/app/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js:187:23)\n    at listOnTimeout (node:internal/timers:569:17)\n    at process.processTimers (node:internal/timers:512:7)","timestamp":"2025-06-19 14:56:14:5614","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-19 14:59:16:5916"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 14:59:23:5923"}
{"address":"**********","code":"ECONNREFUSED","errno":-111,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to TimescaleDB: connect ECONNREFUSED **********:5432\u001b[39m","port":5432,"stack":"Error: connect ECONNREFUSED **********:5432\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1555:16)","syscall":"connect","timestamp":"2025-06-19 15:12:03:123"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to connect to Elasticsearch: connect ECONNREFUSED **********:9200\u001b[39m","meta":{"headers":{},"meta":{"aborted":false,"attempts":3,"connection":{"_openRequests":0,"deadCount":0,"headers":{"user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"id":"http://elasticsearch:9200/","maxEventListeners":100,"pool":{"_events":{},"_eventsCount":1},"resurrectTimeout":0,"timeout":30000,"tls":null,"url":"http://elasticsearch:9200/","weight":1000},"context":null,"name":"elasticsearch-js","request":{"id":1,"options":{},"params":{"headers":{"accept":"application/vnd.elasticsearch+json; compatible-with=8,text/plain","user-agent":"elasticsearch-js/8.18.2 (linux ********-microsoft-standard-WSL2-x64; Node.js 18.20.8; Transport 8.9.7)","x-elastic-client-meta":"es=8.18.2,js=18.20.8,t=8.9.7,un=18.20.8"},"method":"HEAD","path":"/","querystring":""}}},"statusCode":0,"warnings":null},"name":"ConnectionError","options":{"redaction":{"additionalKeys":[],"type":"replace"}},"stack":"ConnectionError: connect ECONNREFUSED **********:9200\n    at SniffingTransport._request (/app/node_modules/@elastic/transport/lib/Transport.js:595:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async /app/node_modules/@elastic/transport/lib/Transport.js:627:32\n    at async SniffingTransport.request (/app/node_modules/@elastic/transport/lib/Transport.js:623:20)\n    at async Client.PingApi [as ping] (/app/node_modules/@elastic/elasticsearch/lib/api/api/ping.js:41:12)\n    at async DatabaseManager.connectElasticsearch (/app/src/config/database.js:73:7)\n    at async Promise.allSettled (index 2)\n    at async DatabaseManager.connectAll (/app/src/config/database.js:109:21)\n    at async ExLogServer.connectDatabases (/app/src/index.js:144:7)\n    at async ExLogServer.start (/app/src/index.js:155:7)","timestamp":"2025-06-19 15:12:11:1211"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Invalid email or password\u001b[39m","method":"POST","stack":"Error: Invalid email or password\n    at /app/src/routes/auth.js:280:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-19 21:48:51:4851","url":"/api/v1/auth/login","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"**********","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: Validation failed\u001b[39m","method":"POST","stack":"Error: Validation failed\n    at handleValidationError (/app/src/middleware/errorHandler.js:151:10)\n    at /app/src/routes/logs.js:201:11\n    at /app/src/middleware/errorHandler.js:129:5\n    at Layer.handle [as handle_request] (/app/node_modules/express/lib/router/layer.js:95:5)\n    at next (/app/node_modules/express/lib/router/route.js:149:13)\n    at middleware (/app/node_modules/express-validator/lib/middlewares/check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-19 21:52:03:523","url":"/api/v1/logs","userAgent":"python-requests/2.32.4","userId":"68542ba6a893eae02269e328"}
