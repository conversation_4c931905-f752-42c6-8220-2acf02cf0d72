# Use Node.js LTS version
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --only=production

# Copy WebSocket server source
COPY src/websocket/ ./src/websocket/
COPY src/config/ ./src/config/
COPY src/utils/ ./src/utils/

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S exlog -u 1001

# Change ownership of the app directory
RUN chown -R exlog:nodejs /app

# Switch to non-root user
USER exlog

# Expose port
EXPOSE 5001

# Start the WebSocket server
CMD ["node", "src/websocket/index.js"]
