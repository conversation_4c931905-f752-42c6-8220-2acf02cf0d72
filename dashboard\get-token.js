const axios = require('axios');

async function getToken() {
  try {
    const response = await axios.post('http://localhost:5000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log('JWT Token:', response.data.data.token);
    return response.data.data.token;
  } catch (error) {
    console.error('Failed to get token:', error.response?.data || error.message);
  }
}

getToken();
